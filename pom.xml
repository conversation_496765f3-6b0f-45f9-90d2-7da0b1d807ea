<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.fxiaoke.cloud</groupId>
    <artifactId>fxiaoke-spring-cloud-parent</artifactId>
    <version>2.7.0-SNAPSHOT</version>
    <relativePath/>
  </parent>

  <groupId>com.fxiaoke</groupId>
  <artifactId>fs-document-converter</artifactId>
  <version>1.0-SNAPSHOT</version>
  <packaging>pom</packaging>

  <modelVersion>4.0.0</modelVersion>
  <modules>
    <module>fs-document-convert-web</module>
    <module>fs-document-convert-web-big</module>
  </modules>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
    <maven.compiler.source>17</maven.compiler.source>
    <maven.compiler.target>17</maven.compiler.target>
    <java.version>17</java.version>
    <jdk.version>17</jdk.version>

    <zxing.version>3.5.3</zxing.version>
    <aspectj.version>1.8.5</aspectj.version>
    <aspose.version>23.12</aspose.version>
    <apache.poi.version>5.2.3</apache.poi.version>
    <apache.poi-ooxml-schemas.version>4.1.2</apache.poi-ooxml-schemas.version>
    <pdfbox.version>3.0.4</pdfbox.version>
    <jai.version>1.4.0</jai.version>
    <jbig2-imageio.version>3.0.4</jbig2-imageio.version>
    <hutool.version>5.8.12</hutool.version>
    <bouncycastle.version>1.70</bouncycastle.version>
    <jsoup.version>1.15.4</jsoup.version>
    <rocketmq.support.version>5.3.0-SNAPSHOT</rocketmq.support.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <!--Aop start-->
      <dependency>
        <artifactId>aspectjtools</artifactId>
        <groupId>org.aspectj</groupId>
        <version>${aspectj.version}</version>
      </dependency>
      <!--Aop end-->


      <!--Aspose 文档控件 start-->
      <dependency>
        <groupId>com.aspose</groupId>
        <artifactId>aspose-words</artifactId>
        <version>${aspose.version}</version>
        <classifier>jdk17</classifier>
      </dependency>
      <dependency>
        <groupId>com.aspose</groupId>
        <artifactId>aspose-words</artifactId>
        <version>${aspose.version}</version>
        <classifier>javadoc</classifier>
      </dependency>
      <dependency>
        <groupId>com.aspose</groupId>
        <artifactId>aspose-slides</artifactId>
        <version>${aspose.version}</version>
        <classifier>jdk16</classifier>
      </dependency>
      <dependency>
        <groupId>com.aspose</groupId>
        <artifactId>aspose-slides</artifactId>
        <version>${aspose.version}</version>
        <classifier>javadoc</classifier>
      </dependency>
      <dependency>
        <groupId>com.aspose</groupId>
        <artifactId>aspose-pdf</artifactId>
        <version>${aspose.version}</version>
        <classifier>jdk17</classifier>
      </dependency>
      <dependency>
        <groupId>com.aspose</groupId>
        <artifactId>aspose-pdf</artifactId>
        <version>${aspose.version}</version>
        <classifier>javadoc</classifier>
      </dependency>
      <dependency>
        <groupId>com.aspose</groupId>
        <artifactId>aspose-cells</artifactId>
        <version>${aspose.version}</version>
      </dependency>
      <dependency>
        <groupId>com.aspose</groupId>
        <artifactId>aspose-cells</artifactId>
        <version>${aspose.version}</version>
        <classifier>javadoc</classifier>
      </dependency>
      <!--Aspose 文档控件 end-->

      <!--Apache POI 文档控件 start-->
      <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi</artifactId>
        <version>${apache.poi.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi-ooxml</artifactId>
        <version>${apache.poi.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi-scratchpad</artifactId>
        <version>${apache.poi.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi-ooxml-schemas</artifactId>
        <version>${apache.poi-ooxml-schemas.version}</version>
      </dependency>
      <!--Apache POI 文档控件 end-->

      <!--Pdfbox 文档控件 start-->
      <dependency>
        <groupId>org.apache.pdfbox</groupId>
        <artifactId>pdfbox</artifactId>
        <version>${pdfbox.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pdfbox</groupId>
        <artifactId>pdfbox-io</artifactId>
        <version>${pdfbox.version}</version>
      </dependency>

      <!--Pdfbox 文档控件 end-->

      <!--Jai 高级图像转换 start-->
      <dependency>
        <artifactId>jai-imageio-core</artifactId>
        <groupId>com.github.jai-imageio</groupId>
        <version>${jai.version}</version>
      </dependency>
      <dependency>
        <artifactId>jai-imageio-jpeg2000</artifactId>
        <groupId>com.github.jai-imageio</groupId>
        <version>${jai.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pdfbox</groupId>
        <artifactId>jbig2-imageio</artifactId>
        <version>${jbig2-imageio.version}</version>
      </dependency>
      <!--Jai 高级图像转换 end-->

      <!--Hutool 工具 start-->
      <dependency>
        <artifactId>hutool-core</artifactId>
        <groupId>cn.hutool</groupId>
        <version>${hutool.version}</version>
      </dependency>
      <!--Hutool 工具 end-->

      <!--bouncycastle 密码包 start-->
      <dependency>
        <artifactId>bcprov-jdk15on</artifactId>
        <groupId>org.bouncycastle</groupId>
        <version>${bouncycastle.version}</version>
      </dependency>
      <dependency>
        <artifactId>bcpkix-jdk15on</artifactId>
        <groupId>org.bouncycastle</groupId>
        <version>${bouncycastle.version}</version>
      </dependency>
      <!--bouncycastle 密码包 end-->

      <!--HTML DOM解析 start-->
      <dependency>
        <groupId>org.jsoup</groupId>
        <artifactId>jsoup</artifactId>
        <version>${jsoup.version}</version>
      </dependency>
      <!--HTML DOM解析 start-->

      <!-- 二维码生成 ZXing start -->
      <dependency>
        <groupId>com.google.zxing</groupId>
        <artifactId>core</artifactId>
        <version>${zxing.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.zxing</groupId>
        <artifactId>javase</artifactId>
        <version>${zxing.version}</version>
      </dependency>
      <!-- 二维码生成 ZXing End -->

    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>my-prepare-agent</id>
            <goals>
              <goal>prepare-agent</goal>

            </goals>
            <configuration>
              <propertyName>jacocoArgLine</propertyName>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <!--suppress UnresolvedMavenProperty -->
          <argLine>
            ${jacocoArgLine}
            --add-opens java.base/java.lang.reflect=ALL-UNNAMED
            --add-opens java.base/java.lang=ALL-UNNAMED
            --add-opens java.base/java.math=ALL-UNNAMED
          </argLine>
          <systemPropertyVariables>
            <process.profile>fstest</process.profile>
          </systemPropertyVariables>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
            </exclude>
          </excludes>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>