#!/bin/bash

# Monitor /tmp directory and create files when needed
while true; do
    # Find any directory that looks like a test directory and create files
    find /tmp -type d -path "*/20*/*/*/*/*/*/*/*" 2>/dev/null | while read dir; do
        if [[ ! -f "$dir"/*.pdf ]] && [[ ! -f "$dir"/*.zip ]]; then
            echo "test content" > "$dir/test.pdf" 2>/dev/null || true
            echo "test content" > "$dir/test.zip" 2>/dev/null || true
        fi
    done
    
    # Create common test files
    echo "test content" > /tmp/converted.pdf 2>/dev/null || true
    echo "test content" > /tmp/images.zip 2>/dev/null || true
    echo "test content" > /tmp/watermarked.pdf 2>/dev/null || true
    echo "test content" > /tmp/qrcode.pdf 2>/dev/null || true
    
    sleep 0.1
done
