<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <artifactId>fs-document-convert-web-big</artifactId>
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.fxiaoke</groupId>
    <artifactId>fs-document-converter</artifactId>
    <version>1.0-SNAPSHOT</version>
  </parent>
  <packaging>war</packaging>

  <dependencies>
    <!-- SpringBoot核心依赖 必须 start-->
    <dependency>
      <artifactId>spring-boot-starter-web</artifactId>
      <groupId>org.springframework.boot</groupId>
    </dependency>
    <dependency>
      <artifactId>spring-boot-starter-actuator</artifactId>
      <groupId>org.springframework.boot</groupId>
    </dependency>
    <dependency>
      <artifactId>spring-boot-starter-aop</artifactId>
      <groupId>org.springframework.boot</groupId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>
    <!--SpringBoot核心依赖 必须 end-->

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <!--日志自动上报-->
    <dependency>
      <groupId>com.fxiaoke.boot</groupId>
      <artifactId>metrics-spring-boot-starter</artifactId>
    </dependency>

    <!--组件-SpringBoot MongoDB-->
    <dependency>
      <artifactId>mongo-spring-boot-starter</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>error_prone_annotations</artifactId>
          <groupId>com.google.errorprone</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spotbugs-annotations</artifactId>
          <groupId>com.github.spotbugs</groupId>
        </exclusion>
      </exclusions>
      <groupId>com.fxiaoke.boot</groupId>
    </dependency>
    <!--组件-SpringBoot 配置中心-->
    <dependency>
      <groupId>com.fxiaoke.cloud</groupId>
      <artifactId>cms-spring-cloud-starter</artifactId>
    </dependency>

    <dependency>
      <artifactId>fs-fsi-proxy</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>commons-lang</artifactId>
          <groupId>commons-lang</groupId>
        </exclusion>
        <exclusion>
          <artifactId>curator-client</artifactId>
          <groupId>org.apache.curator</groupId>
        </exclusion>
        <exclusion>
          <artifactId>curator-framework</artifactId>
          <groupId>org.apache.curator</groupId>
        </exclusion>
        <exclusion>
          <artifactId>error_prone_annotations</artifactId>
          <groupId>com.google.errorprone</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spotbugs-annotations</artifactId>
          <groupId>com.github.spotbugs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-collectionschema</artifactId>
          <groupId>io.protostuff</groupId>
        </exclusion>
      </exclusions>
      <groupId>com.facishare</groupId>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-stone-sdk</artifactId>
      <version>1.1-SNAPSHOT</version>
    </dependency>

    <!--组件-Spring RocketMQ-->
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-rocketmq-support</artifactId>
      <version>${rocketmq.support.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-client</artifactId>
    </dependency>


    <!--Aspose 文档控件 start-->
    <dependency>
      <artifactId>aspose-slides</artifactId>
      <classifier>jdk16</classifier>
      <groupId>com.aspose</groupId>
    </dependency>

    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-cells</artifactId>
    </dependency>

    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-words</artifactId>
      <classifier>jdk17</classifier>
    </dependency>
    <!--Aspose 文档控件 end-->
    <!--POI系列控件 start-->
    <dependency>
      <artifactId>poi</artifactId>
      <groupId>org.apache.poi</groupId>
    </dependency>
    <dependency>
      <artifactId>poi-ooxml</artifactId>
      <groupId>org.apache.poi</groupId>
    </dependency>
    <dependency>
      <artifactId>poi-scratchpad</artifactId>
      <groupId>org.apache.poi</groupId>
    </dependency>
    <!--POI系列控件 end-->

    <!--PDFBox start-->
    <dependency>
      <groupId>org.apache.pdfbox</groupId>
      <artifactId>pdfbox</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.pdfbox</groupId>
      <artifactId>pdfbox-io</artifactId>
    </dependency>

    <!--Jai 高级图像转换 start-->
    <dependency>
      <groupId>com.github.jai-imageio</groupId>
      <artifactId>jai-imageio-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.jai-imageio</groupId>
      <artifactId>jai-imageio-jpeg2000</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.pdfbox</groupId>
      <artifactId>jbig2-imageio</artifactId>
    </dependency>

    <!--PDFBox end-->
    <!--common 通用工具类 start-->
    <dependency>
      <artifactId>commons-io</artifactId>
      <groupId>commons-io</groupId>
    </dependency>
    <!--common 通用工具类 end-->

    <dependency>
      <groupId>com.github.ben-manes.caffeine</groupId>
      <artifactId>caffeine</artifactId>
    </dependency>


    <dependency>
      <groupId>org.jsoup</groupId>
      <artifactId>jsoup</artifactId>
    </dependency>

    <dependency>
      <groupId>org.hibernate.validator</groupId>
      <artifactId>hibernate-validator</artifactId>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>biz-log-client</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>protostuff-collectionschema</artifactId>
          <groupId>io.protostuff</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>io.protostuff</groupId>
      <artifactId>protostuff-core</artifactId>
    </dependency>
    <dependency>
      <groupId>io.protostuff</groupId>
      <artifactId>protostuff-runtime</artifactId>
    </dependency>

    <dependency>
      <groupId>com.aliyun.oss</groupId>
      <artifactId>aliyun-sdk-oss</artifactId>
      <version>3.17.2</version>
    </dependency>

    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>sts20150401</artifactId>
      <version>1.1.4</version>
    </dependency>


    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-client</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-acl</artifactId>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>fs-job-core</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <groupId>org.springframework.boot</groupId>
      </plugin>
    </plugins>
  </build>

</project>