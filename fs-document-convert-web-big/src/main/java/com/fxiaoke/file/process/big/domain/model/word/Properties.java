package com.fxiaoke.file.process.big.domain.model.word;

import java.util.Date;
import lombok.Data;
@Data
public class Properties {
  // 文档标题
  private String title;
  // 文档主题
  private String subject;
  // 文档作者
  private String author;
  // 文档描述
  private String comments;
  // 文档字符数(包括空格)
  private Integer characters;
  // 文件大小
  private Long fileSize;
  // 文档页数
  private Integer pageCount;
  // 文档创建时间
  private Date createTime;
  // 文档最后保存时间
  private Date lastSaveTime;
}
