package com.fxiaoke.file.process.big.domain.mq;

import com.fxiaoke.file.process.big.domain.model.word.ShardContent;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class ShardContentMessage {
  String id;
  String ea;
  ShardContent shardContent;
  String txtPath;
  boolean finish;

  public ShardContentMessage(String id,String ea) {
    this.id = id;
    this.ea = ea;
  }

  /**
   * 结束消息
   * @param id      任务id
   * @param ea      企业EA
   * @param txtPath 纯文本path
   * @param finish  是否结束
   */
  public ShardContentMessage(String id,String ea,String txtPath,boolean finish) {
    this.id = id;
    this.ea = ea;
    this.txtPath = txtPath;
    this.finish = finish;
  }
}
