package com.fxiaoke.file.process.big.domain.entity;


import com.fxiaoke.file.process.big.domain.entity.fieids.ConvertTaskCacheFieIds;
import com.fxiaoke.file.process.big.domain.entity.model.pdf.ImagePathCache;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Field;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.IndexOptions;
import org.mongodb.morphia.annotations.Indexed;
import org.mongodb.morphia.annotations.Indexes;
import org.mongodb.morphia.annotations.Property;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Entity(value = "CTC", noClassnameStored = true)
@Indexes({
    //为ea+path的查询创建索引
    @Index(fields = {@Field(ConvertTaskCacheFieIds.ea), @Field(ConvertTaskCacheFieIds.path)}, options = @IndexOptions(name = "EA_PATH_1", background = true))
})
public class ConvertTaskCache {

  @Id
  private ObjectId id;

  @Property(ConvertTaskCacheFieIds.ea)
  private String ea;

  @Property(ConvertTaskCacheFieIds.path)
  private String path;

  @Property(ConvertTaskCacheFieIds.securityGroup)
  String securityGroup;

  @Property(ConvertTaskCacheFieIds.size)
  private long size;

  @Property(ConvertTaskCacheFieIds.extension)
  private String extension;

  @Property(ConvertTaskCacheFieIds.pageCount)
  private int pageCount;

  @Property(ConvertTaskCacheFieIds.localCachePath)
  private String localCachePath;

  @Embedded(ConvertTaskCacheFieIds.imagePaths)
  private List<ImagePathCache> imagePaths;

  @Indexed(options = @IndexOptions(expireAfterSeconds = 0))
  @Property(ConvertTaskCacheFieIds.expireTime)
  private Date expireTime;

  // 0:已缓存文件未转换 1:转换中 2:转换完成 3:转换失败
  @Property(ConvertTaskCacheFieIds.status)
  private int status;

  @Property(ConvertTaskCacheFieIds.failReason)
  private String failReason;

  // 心跳时间 任务进行中更新
  // 超过3分钟未更新 且任务状态为 1 则认为转换失败 需重试
  // 重试次数超过2次数则认为转换失败 并回调转换任务发起方,并发送失败消息
  @Property(ConvertTaskCacheFieIds.heartbeatTime)
  private Date heartbeatTime;

  // 重试次数
  @Property(ConvertTaskCacheFieIds.retryCount)
  private int retryCount;

  public List<ImagePathCache> getImagePaths() {
    if (this.imagePaths == null) {
      this.imagePaths = new ArrayList<>();
    }
    return this.imagePaths;
  }
}
