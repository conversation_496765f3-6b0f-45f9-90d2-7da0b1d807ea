package com.fxiaoke.file.process.big.domain.api;

import com.fxiaoke.file.process.big.domain.constant.Status;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

public interface Pdf2Image {

  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class Arg{

    @NotBlank(message = "ea cannot be empty")
    private String ea;
    @NotBlank(message = "path cannot be empty")
    @Pattern(regexp = "^(N_|TN_|C_|TC_|TA_|A_).*\\.(pdf)$",message ="path format error")
    private String path;
    private String securityGroup;

  }

  @Data
  @ToString
  @NoArgsConstructor
  @AllArgsConstructor
  @EqualsAndHashCode
  class Result{
    public String ea;
    public String taskId;
    public int status;
    public List<Page> paths;
    public String message;

    public static Result Queue(String ea,String taskId){
      Result result = new Result();
      result.ea = ea;
      result.taskId = taskId;
      result.status = Status.QUEUE;
      return result;
    }

    public static Result Retry(String ea,String taskId){
      Result result = new Result();
      result.ea = ea;
      result.taskId = taskId;
      result.status = Status.RETRY;
      return result;
    }

    public static Result Process(String ea,String taskId){
      Result result = new Result();
      result.ea = ea;
      result.taskId = taskId;
      result.status = Status.PROCESS;
      return result;
    }

    public static Result Success(String ea,String taskId, List<Page> paths){
      Result result = new Result();
      result.ea = ea;
      result.taskId = taskId;
      result.status = Status.SUCCESS;
      result.paths = paths;
      return result;
    }

    public static Result Fail(String ea,String taskId, String message){
      Result result = new Result();
      result.ea = ea;
      result.taskId = taskId;
      result.status = Status.FAIL;
      result.message = message;
      return result;
    }

  }

  @Data
  @ToString
  @NoArgsConstructor
  @EqualsAndHashCode
  class Page{
    public int pageNumber;
    public String extension;
    public String path;
    public long size;
    public long width;
    public long height;

    public Page(int pageNumber, String extension, String path, long size, long width, long height) {
      this.pageNumber = pageNumber;
      this.extension = extension;
      this.path = path;
      this.size = size;
      this.width = width;
      this.height = height;
    }
  }

}
