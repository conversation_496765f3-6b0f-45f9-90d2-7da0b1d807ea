package com.fxiaoke.file.process.big.service;

import com.aliyun.oss.ClientConfiguration;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.sts20150401.Client;
import com.aliyun.sts20150401.models.AssumeRoleRequest;
import com.aliyun.sts20150401.models.AssumeRoleResponse;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.fxiaoke.common.Guard;
import com.fxiaoke.file.process.big.domain.dto.AliDownloadAuthDTO;
import com.fxiaoke.file.process.big.domain.exception.BaseException;
import com.fxiaoke.file.process.big.util.EncryptUtils;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.github.autoconf.ConfigFactory;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.moandjiezana.toml.Toml;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * creator: liuys
 * CreateTime: 2024-12-24
 * Description:
 */
@Slf4j
@Service
public class AliOssConfigInitialService {
  private static final String COMMON_KEY = "default";
  private static Guard guard = new Guard("1111111111111111");
  private static LoadingCache<String, OSSClient> ossClientCache;
  private static final Cache<String, AliDownloadAuthDTO> enterpriseConfigCache = CacheBuilder.newBuilder().build();
  private static LoadingCache<String, Client> stsClientCache;
  private static final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("bfm");
  private String egressProxy;
  private static final long DEFAULT_DURATION_SECONDS = 3600;
  private long durationSeconds;
  private static final long DIFF_TIME = 200L;
  private ClientConfiguration clientConfiguration = new ClientConfiguration();

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-dps-config", config -> {
      egressProxy = config.get("egress.proxy");
      if (StringUtils.isNotBlank(egressProxy)) {
        clientConfiguration.setProxyPort(Integer.parseInt(egressProxy.substring(egressProxy.indexOf(":") + 1)));
        clientConfiguration.setProxyHost(egressProxy.substring(0, egressProxy.indexOf(":")));
      }
    });


    ConfigFactory.getConfig("fs-bfm-enterprise-toml", config -> {
      Toml toml = new Toml().read(config.getString());
      toml.entrySet().forEach(entry -> {
        Toml value = (Toml) entry.getValue();
        AliDownloadAuthDTO authDTO = AliDownloadAuthDTO.builder()
            .accessKeyId(decode(value.getString("accessKeyId")))
            .accessKeySecret(decode(value.getString("accessKeySecret")))
            .bucketName(value.getString("bucketName"))
            .region(value.getString("region"))
            .ossRegion(value.getString("ossRegion"))
            .roleArn(value.getString("roleArn")).build();
        AliDownloadAuthDTO tomlConfig = enterpriseConfigCache.getIfPresent(entry.getKey());
        if (Objects.nonNull(tomlConfig) && authDTO.hashCode() != tomlConfig.hashCode()) {
          enterpriseConfigCache.put(entry.getKey(), authDTO);
          stsClientCache.invalidate(entry.getKey());
          ossClientCache.invalidate(entry.getKey());
        } else {
          enterpriseConfigCache.put(entry.getKey(), authDTO);
        }
      });
    });

    ConfigFactory.getConfig("fs-bfm-ali-oss", iConfig -> {
      durationSeconds = iConfig.getLong("ali.oss.sts.duration.seconds", DEFAULT_DURATION_SECONDS);
      AliDownloadAuthDTO commonConfig = AliDownloadAuthDTO.builder()
          .accessKeyId(EncryptUtils.decode(iConfig.get("ali.oss.access.key.id")))
          .accessKeySecret(EncryptUtils.decode(iConfig.get("ali.oss.access.key.secret")))
          .bucketName(iConfig.get("ali.oss.bucket.name"))
          .region(iConfig.get("ali.region.name"))
          .ossRegion(iConfig.get("ali.oss.region.name"))
          .roleArn(iConfig.get("ali.oss.roleArn")).build();
      enterpriseConfigCache.put(COMMON_KEY, commonConfig);
    });


    ossClientCache = CacheBuilder.newBuilder().expireAfterWrite(durationSeconds - DIFF_TIME, TimeUnit.SECONDS).build(
        new CacheLoader<>() {
          @NotNull
          @Override
          public OSSClient load(@NotNull String ea) {
            return initOssClient(ea);
          }
        }
    );

    stsClientCache = CacheBuilder.newBuilder().build(
        new CacheLoader<>() {
          @NotNull
          @Override
          public Client load(@NotNull String ea) {
            return initStsClient(ea);
          }
        }
    );
  }

  private String decode(String token) {
    return guard.decode(token);
  }


  private OSSClient initOssClient(String ea) {
    boolean isGray = gray.isAllow("withOutOurOssEnterprise", ea);
    String key = isGray ? ea : COMMON_KEY;
    AliDownloadAuthDTO config = enterpriseConfigCache.getIfPresent(key);
    if (Objects.isNull(config)) {
      throw new BaseException("未找到对应的配置", 500, "");
    }
    if (!isGray) {
      CredentialsProvider credentialsProvider = getOssCredentialsProvider(config.getAccessKeyId(), config.getAccessKeySecret());
      return new OSSClient(getEndpoint(config.getOssRegion()), credentialsProvider, clientConfiguration);
    } else { // 专属企业
      Client stsClient = stsClientCache.getUnchecked(key);
      AssumeRoleResponse assumeRoleResp = getExclusiveEnterpriseAssumeRoleResp(stsClient, key);
      return new OSSClient(getEndpoint(config.getOssRegion()),
          getOssCredentialsProvider(
              assumeRoleResp.getBody().getCredentials().accessKeyId,
              assumeRoleResp.body.getCredentials().accessKeySecret,
              assumeRoleResp.body.getCredentials().securityToken),
          clientConfiguration);
    }
  }

  private Client initStsClient(String ea) {
    try {
      boolean isGray = gray.isAllow("withOutOurOssEnterprise", ea);
      String key = isGray ? ea : COMMON_KEY;
      AliDownloadAuthDTO tomlConfig = enterpriseConfigCache.getIfPresent(key);
      if (Objects.isNull(tomlConfig)) {
        throw new BaseException("未找到对应的配置", 500, "");
      }
      Config config = new Config();
      config.setAccessKeyId(tomlConfig.getAccessKeyId());
      config.setAccessKeySecret(tomlConfig.getAccessKeySecret());
      config.setRegionId(tomlConfig.getRegion());
      return new Client(config);
    } catch (Exception e) {
      throw new BaseException("初始化stsClient失败", 500, "");
    }
  }

  /**
   * 获取专属企业的stsToken
   */
  private AssumeRoleResponse getExclusiveEnterpriseAssumeRoleResp(Client stsClient, String ea) {
    try {
      AssumeRoleRequest request = new AssumeRoleRequest();
      AliDownloadAuthDTO configForSts = enterpriseConfigCache.getIfPresent(ea);
      request.setRoleArn(configForSts.getRoleArn());
      request.setDurationSeconds(durationSeconds);
      request.setRoleSessionName(ea + "_" + UUID.randomUUID());
      RuntimeOptions runtimeOptions = new RuntimeOptions();
      if (StringUtils.isNotBlank(this.egressProxy)) {
        runtimeOptions.setHttpProxy("http://" + this.egressProxy);
      }
      return stsClient.assumeRoleWithOptions(request, runtimeOptions);
    } catch (Exception e) {
      throw new BaseException("sts服务获取授权失败", 500, "");
    }
  }

  public OSSClient getOssClient(String ea) {
    boolean isGray = gray.isAllow("withOutOurOssEnterprise", ea);
    String key = isGray ? ea : COMMON_KEY;
    return ossClientCache.getUnchecked(key);
  }

  public AliDownloadAuthDTO getCredential(String ea) {
    boolean isGray = gray.isAllow("withOutOurOssEnterprise", ea);
    String key = isGray ? ea : COMMON_KEY;
    return enterpriseConfigCache.getIfPresent(key);
  }

  private CredentialsProvider getOssCredentialsProvider(String ak, String sk) {
    return new DefaultCredentialProvider(ak, sk);
  }

  private String getEndpoint(String ossRegionName) {
    return "https://" + ossRegionName + ".aliyuncs.com";
  }

  private CredentialsProvider getOssCredentialsProvider(String ak, String sk, String stsToken) {
    return new DefaultCredentialProvider(ak, sk, stsToken);
  }

}
