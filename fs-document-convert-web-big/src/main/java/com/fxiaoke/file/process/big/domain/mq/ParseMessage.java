package com.fxiaoke.file.process.big.domain.mq;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ParseMessage{
  String id;
  String ea;
  String filePath;
  boolean generateTxt;
  String txtPath;
  boolean standard;

  public ParseMessage(String id,String ea,String filePath,boolean generateTxt,String txtPath,boolean standard){
    this.id =id;
    this.ea =ea;
    this.filePath =filePath;
    this.generateTxt =generateTxt;
    this.txtPath =txtPath;
    this.standard =standard;
  }
}
