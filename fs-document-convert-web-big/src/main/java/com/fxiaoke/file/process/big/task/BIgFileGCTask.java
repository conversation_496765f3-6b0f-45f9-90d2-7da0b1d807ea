package com.fxiaoke.file.process.big.task;

import java.io.File;
import java.util.Date;
import java.text.SimpleDateFormat;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Component;

import com.fxiaoke.file.process.big.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.big.dao.IConvertTaskCacheDao;
import com.github.autoconf.ConfigFactory;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
@JobHander(value = "bigFileGCHandler")
public class BIgFileGCTask extends IJobHandler {

    @Resource
    CmsPropertiesConfig config;
    private int allowGcDay = 7;
    private int timeOut;
    @Resource
    private IConvertTaskCacheDao convertTaskCacheDao;

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-dps-config", config -> {
            allowGcDay = config.getInt("allow.gc.day");
            timeOut = config.getInt("timeOut", 18000); // 默认5小时超时(5*3600=18000秒)
        });
    }

    @Override
    public ReturnT execute(TriggerParam params) {
        long currentTime = System.currentTimeMillis();
        long GcCurrentTime = (long) allowGcDay * 24 * 3600 * 1000;
        Date expires = new Date(currentTime - GcCurrentTime);
        log.info("开始执行文件缓存清理任务");
        String cathePath = config.getSmbDiskPath();
        File file = new File(cathePath);
        if (!file.exists()) {
            log.info("缓存路径不存在，跳过清理任务");
            return ReturnT.SUCCESS;
        }
        File[] files = file.listFiles();
        if (files == null || files.length == 0) {
            log.info("缓存路径为空，跳过清理任务");
            return ReturnT.SUCCESS;
        }
        for (File yyyyMMddFile : files) {

            if (System.currentTimeMillis() - currentTime > timeOut * 1000L) { // 如果超过了执行时间，退出
                log.warn("gc任务执行超时，已运行{}小时，强制退出", timeOut / 3600);
                break;
            }

            String dirName = yyyyMMddFile.getName();
            // FIXME （以后要规范梳理 /smb 目录下的文件夹命名）检查是否为8位数字格式
            if (dirName.matches("\\d{8}")) {
                try {
                    // 解析日期
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                    Date fileDate = sdf.parse(dirName);
                    // 如果文件夹日期早于过期时间，则删除
                    if (fileDate.before(expires)) {
                        // 确保meta信息一定不存在
                        convertTaskCacheDao.deleteBySmbPath(yyyyMMddFile.getAbsolutePath());
                        FileUtils.forceDelete(yyyyMMddFile);
                        log.info("删除过期目录: {}", yyyyMMddFile.getAbsolutePath());
                    }
                } catch (Exception e) {
                    log.error("处理目录:{}失败,错误原因:{}", yyyyMMddFile.getAbsolutePath(), e.getMessage());
                }
            }

        }
        return ReturnT.SUCCESS;
    }
}
