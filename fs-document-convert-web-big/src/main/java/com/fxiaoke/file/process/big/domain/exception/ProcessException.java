package com.fxiaoke.file.process.big.domain.exception;

import com.fxiaoke.file.process.big.domain.constant.ErrorInfo;
import lombok.Getter;

@Getter
public class ProcessException extends BaseException{

  public ProcessException(ErrorInfo errorInfo, String module, Object... args) {
    super(errorInfo, module, args);
  }

  public ProcessException(ErrorInfo errorInfo, Exception e, String module, Object... args) {
    super(errorInfo, e, module, args);
  }
}
