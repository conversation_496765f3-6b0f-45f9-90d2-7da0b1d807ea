package com.fxiaoke.file.process.big.service.aspose;

import com.fxiaoke.file.process.big.domain.constant.ErrorInfo;
import com.fxiaoke.file.process.big.domain.exception.ParseException;
import com.fxiaoke.file.process.big.domain.model.word.Content;
import com.fxiaoke.file.process.big.domain.model.word.ShardContent;
import com.fxiaoke.file.process.big.domain.mq.ShardContentMessage;
import com.fxiaoke.file.process.big.mq.ParseProducer;
import com.fxiaoke.file.process.big.service.IFileParseService;
import com.fxiaoke.file.process.big.service.options.SlidesOptions;
import com.fxiaoke.file.process.big.util.FileNameUtil;
import com.google.common.base.Strings;
import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.sl.usermodel.Shape;
import org.apache.poi.sl.usermodel.Slide;
import org.apache.poi.sl.usermodel.SlideShow;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xslf.usermodel.XSLFGroupShape;
import org.apache.poi.xslf.usermodel.XSLFShape;
import org.apache.poi.xslf.usermodel.XSLFTextParagraph;
import org.apache.poi.xslf.usermodel.XSLFTextShape;
import org.springframework.stereotype.Service;

@Service
public class SlidesService implements IFileParseService {

  @Resource
  SlidesOptions slidesOptions;

  @Resource
  ParseProducer parseProducer;

  /**
   * 解析文件 返回文件内容
   *
   * @param filePath 文件路径
   * @param async    是否异步
   * @return content       文件内容
   */
  @Override
  public Content parse(String filePath, boolean async) {
    // 文件格式升级(内含文件是否加密判断)
    if (filePath.endsWith(".ppt")){
      filePath = slidesOptions.upgrade(filePath);
    }

    // 异步解析
    if (async){
      return Content.builder().standard(false).build();
    }

    // 同步解析
    try {
      List<ShardContent> shardContentList = parsePlainTxtFromNoStandard(filePath);
      return Content.builder().ShardContents(shardContentList).standard(false).build();
    } catch (IOException e) {
      throw new ParseException(ErrorInfo.PARSE_ERROR, e, "SlidesService.parse", filePath);
    }
  }

  /**
   * 异步解析PPT
   *
   * @param id        任务id
   * @param ea        企业账号
   * @param filePath  文件路径
   * @param standard  是否标准化文档,PDF全部不标准化处理
   */
  @Override
  public void asyncParse(String id, String ea, String filePath, boolean standard) {
    // 文件格式升级(内含文件是否加密判断)
    if (filePath.endsWith(".ppt")){
      filePath = slidesOptions.upgrade(filePath);
    }
    ShardContentMessage message = new ShardContentMessage(id, ea);
    try (InputStream stream = Files.newInputStream(Path.of(filePath))) {
      SlideShow<XSLFShape, XSLFTextParagraph> slideShow = new XMLSlideShow(stream);
      List<? extends Slide<XSLFShape, XSLFTextParagraph>> slides = slideShow.getSlides();
      int pageIndex = 1;
      int sequence = 1;
      for (Slide<XSLFShape, XSLFTextParagraph> slide : slides) {
        List<XSLFShape> shapes = slide.getShapes();
        for (XSLFShape sh : shapes) {
          String shapesTxt = getShapesTxt(sh);
          if (Strings.isNullOrEmpty(shapesTxt)) {
            continue;
          }
          ShardContent content = new ShardContent(false, 9);
          content.setSequence(sequence++);
          content.setPageIndex(pageIndex);
          shapesTxt= StringUtils.replaceChars(shapesTxt, "\n\r\t", "");
          shapesTxt= StringUtils.replaceChars(shapesTxt, "\"", "'");
          content.setContent(shapesTxt);
          message.setShardContent(content);
          parseProducer.send(message);
        }
        pageIndex++;
      }
    } catch (IOException e) {
      throw new ParseException(ErrorInfo.PARSE_ERROR, e, "SlidesService.asyncParse", filePath);
    }
  }

  /**
   * PPT 转TXT
   * @param filePath PDF文件路径
   * @return         TXT文件路径
   */
  @Override
  public String toTxt(String filePath) {
    // 文件格式升级(内含文件是否加密判断)
    if (filePath.endsWith(".ppt")){
      filePath = slidesOptions.upgrade(filePath);
    }
    String txtFilePath = FileNameUtil.toTxt(filePath);
    try (InputStream stream = Files.newInputStream(Path.of(filePath))) {
      SlideShow<XSLFShape, XSLFTextParagraph> slideShow = new XMLSlideShow(stream);
      List<? extends Slide<XSLFShape, XSLFTextParagraph>> slides = slideShow.getSlides();
      StringBuilder content = new StringBuilder();
      for (Slide<XSLFShape, XSLFTextParagraph> slide : slides) {
        List<XSLFShape> shapes = slide.getShapes();
        for (XSLFShape sh : shapes) {
          String shapesTxt = getShapesTxt(sh);
          if (Strings.isNullOrEmpty(shapesTxt)) {
            continue;
          }
          content.append(shapesTxt);
        }
      }
      // 将文本保存到TXT文件
      try (BufferedWriter writer = new BufferedWriter(new FileWriter(txtFilePath))) {
        writer.write(content.toString());
        writer.close();
        return txtFilePath;
      }
    } catch (IOException e) {
      throw new ParseException(ErrorInfo.PARSE_ERROR, e, "SlidesService.toTxt", filePath);
    }
  }

  private List<ShardContent> parsePlainTxtFromNoStandard(String filePath) throws IOException {
    List<ShardContent> shardContentList = new ArrayList<>();
    try (InputStream stream = Files.newInputStream(Path.of(filePath))) {
      SlideShow<XSLFShape, XSLFTextParagraph> slideShow = new XMLSlideShow(stream,"ppt/media/");
      List<? extends Slide<XSLFShape, XSLFTextParagraph>> slides = slideShow.getSlides();
      int pageIndex = 1;
      int sequence = 1;
      for (Slide<XSLFShape, XSLFTextParagraph> slide : slides) {
        List<XSLFShape> shapes = slide.getShapes();
        for (XSLFShape sh : shapes) {
          String shapesTxt = getShapesTxt(sh);
          if (Strings.isNullOrEmpty(shapesTxt)) {
            continue;
          }
          shapesTxt= StringUtils.replaceChars(shapesTxt, "\n\r\t", "");
          shapesTxt= StringUtils.replaceChars(shapesTxt, "\"", "'");
          ShardContent shardContent = new ShardContent(false, 9);
          shardContent.setSequence(sequence++);
          shardContent.setPageIndex(pageIndex);
          shardContent.setContent(shapesTxt);
          shardContentList.add(shardContent);
        }
        pageIndex++;
      }
    }
    return shardContentList;
  }

  private String getShapesTxt(Shape shape) {
    StringBuilder content = new StringBuilder();
    if (shape instanceof XSLFTextShape iTextSharp) {
      String sentence = iTextSharp.getText();
      content.append(sentence);
    } else if (shape instanceof XSLFGroupShape shape2) { // 如果是组合类型，继续下钻直到取到文本
      for (XSLFShape xslfShape : shape2) {
        content.append(getShapesTxt(xslfShape));
      }
    }
    return content.toString();
  }
}
