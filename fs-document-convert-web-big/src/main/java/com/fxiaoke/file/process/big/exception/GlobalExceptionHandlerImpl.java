package com.fxiaoke.file.process.big.exception;

import com.aspose.slides.exceptions.ArgumentException;
import com.fxiaoke.file.process.big.domain.Result;
import com.fxiaoke.file.process.big.domain.constant.Constants;
import com.fxiaoke.file.process.big.domain.constant.ErrorInfo;
import com.fxiaoke.file.process.big.domain.exception.ProcessException;
import com.fxiaoke.file.process.big.domain.exception.TerminationException;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR> [Andy]
 * @date 2023-01-05
 */

@Slf4j(topic = "GlobalExceptionHandler")
@RestControllerAdvice
public class GlobalExceptionHandlerImpl {

  @ExceptionHandler(value = Exception.class)
  public Result<String> handler(HttpServletResponse response, Exception e) {
    ErrorInfo unknown = ErrorInfo.UNKNOWN_EXCEPTION;
    log.error("unknown exception:", e);
    response.setStatus(unknown.getCode());
    return Result.error(unknown.getCode(), unknown.getMessage());
  }

  @ExceptionHandler(value = ConstraintViolationException.class)
  public Result<String> handler(HttpServletResponse response, ConstraintViolationException e) {
    log.warn("Client exception:{},\n Missing parameter types:{},\n parameter name:{}", e, e.getMessage(), e.getConstraintViolations());
    response.setStatus(400);
    return Result.error(400, e.getMessage());
  }

  @ExceptionHandler(value = MethodArgumentNotValidException.class)
  public Result<String> handler(HttpServletResponse response, MethodArgumentNotValidException e) {
    log.warn("parameter check no pass,Reason:{}", e.getMessage(), e);
    response.setStatus(400);
    return Result.error(400, Objects.requireNonNull(e.getFieldError()).getDefaultMessage());
  }

  @ExceptionHandler(value = IllegalArgumentException.class)
  public Result<String> handler(HttpServletResponse response, IllegalArgumentException e) {
    log.warn("Illegal argument exception:", e);
    response.setStatus(400);
    return Result.error(400, Constants.UNIVERSAL_ERROR);
  }

  @ExceptionHandler(value = MissingServletRequestParameterException.class)
  public Result<String> handler(HttpServletResponse response,MissingServletRequestParameterException e) {
    log.warn("missing parameter,parameterType:{},parameterName:{}",e.getParameterType(),e.getParameterName(),e);
    response.setStatus(400);
    return Result.error(400, "missing required parameter");
  }

  @ExceptionHandler(value = ArgumentException.class)
  public Result<String> handler(HttpServletResponse response, ArgumentException e) {
    log.warn("Invalid parameter:{},exception:", e.getMessage(), e);
    response.setStatus(400);
    return Result.error(400, Constants.UNIVERSAL_ERROR);
  }

  @ExceptionHandler(value = ProcessException.class)
  public Result<String> handler(HttpServletResponse response, ProcessException e) {
    if (e.getCode() == 400) {
      log.warn("Document convert fail:{},exception:", e.getReason(), e);
    } else {
      log.error("Document convert fail:{},exception:",e.getReason(),e);
    }
    response.setStatus(e.getCode());
    return Result.error(e.getCode(), e.getMessage());
  }

  @ExceptionHandler(value = TerminationException.class)
  public Result<String> handler(HttpServletResponse response, TerminationException e) {
    if (e.getCode() == 400) {
      log.warn("Thread execute fail:{},exception:", e.getReason(), e);
    } else {
      log.error("Thread execute fail:{},exception: ",e.getReason(),e);
    }
    response.setStatus(e.getCode());
    return Result.error(e.getCode(), e.getMessage());
  }
  @ExceptionHandler(value = AssertionError.class)
  public Result<String> handler(HttpServletResponse response, AssertionError e) {
    log.warn("AssertionError message:{},exception:", e.getMessage(), e);
    response.setStatus(400);
    return Result.error(400,e.getMessage());
  }

  @ExceptionHandler(value = HttpMessageNotReadableException.class)
  public Result<String> handler(HttpServletResponse response, HttpMessageNotReadableException e) {
    log.warn("parameter check no pass,Reason:{}", e.getMessage(), e);
    response.setStatus(400);
    return Result.error(400, "json parse error");
  }

}


