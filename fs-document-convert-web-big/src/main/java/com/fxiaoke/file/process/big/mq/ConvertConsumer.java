package com.fxiaoke.file.process.big.mq;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.file.process.big.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.big.service.ConvertTaskService;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ConvertConsumer implements ApplicationListener<ContextRefreshedEvent> {
  private final CmsPropertiesConfig config;
  private AutoConfMQPushConsumer consumer;
  ConvertTaskService convertTaskService;

  public ConvertConsumer(CmsPropertiesConfig config, ConvertTaskService convertTaskService) {
    this.config = config;
    this.convertTaskService = convertTaskService;
  }

  @PostConstruct
  public void init() {
    log.info("init consumer,configName={},mqSection={}",config.getMqConfigName(), config.getMqConvertSection());
    consumer = new AutoConfMQPushConsumer(config.getMqConfigName(), config.getMqConvertSection(),
        (MessageListenerConcurrently) (msgs, context) -> {
          log.info("receive message:{}", msgs.size());
          for (MessageExt msg : msgs) {
            MessageHelper.fillContextFromMessage(TraceContext.get(), msg);
            try {
              String taskId = JSON.parseObject(msg.getBody(), String.class);
              // 消费逻辑
              boolean processResult = convertTaskService.processTask(taskId);
              log.info("process task result:{}", processResult);
            } finally {
              TraceContext.remove();
            }
          }
          log.info("consume message success msgSize={}", msgs.size());
          return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    log.info("consumer subscribe topic:{}", config.getMqTopic());
  }

  @PreDestroy
  public void close() {
    consumer.close();
  }

  @Override
  public void onApplicationEvent(ContextRefreshedEvent event) {
    if (consumer != null && event.getApplicationContext().getParent() == null) {
      consumer.start();
    }
  }
}
