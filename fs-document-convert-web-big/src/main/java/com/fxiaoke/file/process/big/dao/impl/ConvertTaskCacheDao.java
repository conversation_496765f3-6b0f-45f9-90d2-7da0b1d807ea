package com.fxiaoke.file.process.big.dao.impl;

import com.fxiaoke.file.process.big.dao.IConvertTaskCacheDao;
import com.fxiaoke.file.process.big.domain.constant.Status;
import com.fxiaoke.file.process.big.domain.entity.ConvertTaskCache;
import com.fxiaoke.file.process.big.domain.entity.fieids.ConvertTaskCacheFieIds;
import com.fxiaoke.file.process.big.domain.entity.model.pdf.ImagePathCache;
import com.github.mongo.support.DatastoreExt;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import org.bson.types.ObjectId;
import org.mongodb.morphia.Key;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.stereotype.Repository;

@Repository
public class ConvertTaskCacheDao implements IConvertTaskCacheDao {

  @Resource
  private DatastoreExt dpsDataStore;

  @PostConstruct
  public void initIndex() {
    dpsDataStore.ensureIndexes(ConvertTaskCache.class);
  }

  private Query<ConvertTaskCache> getQuery() {
    return dpsDataStore.createQuery(ConvertTaskCache.class);
  }

  @Override
  public ConvertTaskCache findTask(String id) {
    Query<ConvertTaskCache> query = getQuery();
    query.field(ConvertTaskCacheFieIds.id).equal(new ObjectId(id));
    return query.get();
  }

  @Override
  public ConvertTaskCache findTask(String ea, String path, String securityGroup) {
    Query<ConvertTaskCache> query = getQuery();
    query.field(ConvertTaskCacheFieIds.ea).equal(ea);
    query.field(ConvertTaskCacheFieIds.path).equal(path);
    query.field(ConvertTaskCacheFieIds.securityGroup).equal(securityGroup);
    return query.get();
  }

  @Override
  public List<ImagePathCache> findImagePathCache(String id) {
    Query<ConvertTaskCache> query = getQuery();
    query.field(ConvertTaskCacheFieIds.id).equal(new ObjectId(id));
    return query.get().getImagePaths();
  }

  @Override
  public String initTask(String ea, String path, String securityGroup, long size, String extension, int pageCount,
      String localCachePath) {
    ConvertTaskCache taskCache = new ConvertTaskCache();
    taskCache.setEa(ea);
    taskCache.setPath(path);
    taskCache.setSize(size);
    taskCache.setSecurityGroup(securityGroup);
    taskCache.setExtension(extension);
    taskCache.setPageCount(pageCount);
    taskCache.setLocalCachePath(localCachePath);
    taskCache.setHeartbeatTime(new Date());
    taskCache.setExpireTime(new Date(System.currentTimeMillis() + 1000 * 60 * 60 * 24 * 7));
    taskCache.setImagePaths(new ArrayList<>());
    // 任务状态
    taskCache.setStatus(Status.QUEUE);
    // 重试次数
    taskCache.setRetryCount(0);
    Key<ConvertTaskCache> result = dpsDataStore.save(taskCache);
    Object id = result.getId();
    return id.toString();
  }

  @Override
  public void retryTask(String id, boolean restStatus) {
    Query<ConvertTaskCache> query = getQuery();
    query.field(ConvertTaskCacheFieIds.id).equal(new ObjectId(id));
    UpdateOperations<ConvertTaskCache> updateOperations = dpsDataStore.createUpdateOperations(ConvertTaskCache.class);
    updateOperations.inc(ConvertTaskCacheFieIds.retryCount);
    updateOperations.set(ConvertTaskCacheFieIds.heartbeatTime, new Date());
    if (restStatus) {
      updateOperations.set(ConvertTaskCacheFieIds.status, Status.QUEUE);
    }
    dpsDataStore.update(query, updateOperations);
  }


  @Override
  public void updateStatus(String id, int status, String failReason) {
    Query<ConvertTaskCache> query = getQuery();
    query.field(ConvertTaskCacheFieIds.id).equal(new ObjectId(id));
    UpdateOperations<ConvertTaskCache> updateOperations = dpsDataStore.createUpdateOperations(ConvertTaskCache.class);
    updateOperations.set(ConvertTaskCacheFieIds.status, status);
    updateOperations.set(ConvertTaskCacheFieIds.heartbeatTime, new Date());
    if (status == Status.FAIL) {
      updateOperations.set(ConvertTaskCacheFieIds.failReason, failReason);
    }
    dpsDataStore.update(query, updateOperations);
  }


  @Override
  public void addImagePathCache(String taskId, ImagePathCache imagePathCache) {
    Query<ConvertTaskCache> query = getQuery();
    query.field(ConvertTaskCacheFieIds.id).equal(new ObjectId(taskId));
    ConvertTaskCache taskCache = query.get();
    List<ImagePathCache> pages = taskCache.getImagePaths();
    pages.add(imagePathCache);
    UpdateOperations<ConvertTaskCache> updateOperations = dpsDataStore.createUpdateOperations(ConvertTaskCache.class);
    updateOperations.set(ConvertTaskCacheFieIds.imagePaths, pages);
    updateOperations.set(ConvertTaskCacheFieIds.heartbeatTime, new Date());
    dpsDataStore.update(query, updateOperations);
  }

  @Override
  public void deleteBySmbPath(String smbPath) {
    Query<ConvertTaskCache> query = getQuery();
    query.field(ConvertTaskCacheFieIds.localCachePath).equal(smbPath);
    dpsDataStore.delete(query);
  }
}
