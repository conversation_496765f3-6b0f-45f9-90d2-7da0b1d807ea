package com.fxiaoke.file.process.big.domain.exception;

import com.fxiaoke.file.process.big.domain.constant.ErrorInfo;
import java.util.Arrays;
import lombok.Getter;

@Getter
public class BaseException extends RuntimeException{
  private static final String MODULE_NAME = "module:";
  private static final String ERROR_REASON = "reason:";
  private static final String ARGS_NAME = "args:";
  private final int code;
  private final String message;
  private final String reason;

  public BaseException(String message,int code,String module,Object... args) {
    super(message);
    this.code = code;
    this.message = message;
    this.reason = MODULE_NAME + module +"-"+
        ERROR_REASON + message +"-"+
        ARGS_NAME + Arrays.toString(args);
  }

  public BaseException(Exception e,int code,String module, Object... args) {
    super(e.getMessage(), e);
    this.code = code;
    this.message = e.getMessage();
    this.reason = MODULE_NAME + module +"-"+
        ERROR_REASON + e.getMessage() +"-"+
        ARGS_NAME + Arrays.toString(args);
  }

  public BaseException(ErrorInfo errorInfo, String module, Object... args) {
    super(errorInfo.getMessage());
    this.code = errorInfo.getCode();
    this.message = errorInfo.getMessage();
    this.reason = MODULE_NAME + module +"-"+
        ERROR_REASON + errorInfo.getReason() +"-"+
        ARGS_NAME + Arrays.toString(args);
  }

  public BaseException(ErrorInfo errorInfo, Exception e, String module, Object... args) {
    super(e.getMessage(), e);
    this.code = errorInfo.getCode();
    this.message = errorInfo.getMessage();
    this.reason = MODULE_NAME + module +"-"+
        ERROR_REASON + errorInfo.getReason() + "-" + e.getMessage() +"-"+
        ARGS_NAME + Arrays.toString(args);
  }

}
