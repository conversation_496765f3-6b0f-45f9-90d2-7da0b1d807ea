package com.fxiaoke.file.process.big.domain.exception;

import com.fxiaoke.file.process.big.domain.constant.Constants;
import com.fxiaoke.file.process.big.domain.constant.ErrorInfo;
import lombok.Getter;

@Getter
public class TerminationException extends RuntimeException {

  private final int code;
  private final String message;
  private final String reason;

  public TerminationException(ErrorInfo errorInfo, Exception e, String module) {
    super(e.getMessage(), e);
    if (e.getCause() instanceof ProcessException exception) {
      this.code = exception.getCode();
      this.message = exception.getMessage();
      this.reason =exception.getReason();
    } else {
      this.code = errorInfo.getCode();
      this.message = errorInfo.getMessage();
      this.reason = Constants.MODULE_NAME + module + "-" +
          Constants.ERROR_REASON + errorInfo.getReason() + "-" + e.getMessage();
    }
  }
}
