package com.fxiaoke.file.process.big.service.options;



import com.aspose.words.DocSaveOptions;
import com.aspose.words.Document;
import com.aspose.words.Field;
import com.aspose.words.FieldType;
import com.aspose.words.FileFormatUtil;
import com.aspose.words.LoadFormat;
import com.aspose.words.Node;
import com.aspose.words.NodeCollection;
import com.aspose.words.NodeType;
import com.aspose.words.Paragraph;
import com.aspose.words.ParagraphFormat;
import com.aspose.words.Run;
import com.aspose.words.SaveFormat;
import com.aspose.words.Shape;
import com.aspose.words.TxtSaveOptions;
import com.fxiaoke.file.process.big.domain.constant.ErrorInfo;
import com.fxiaoke.file.process.big.domain.exception.BaseException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class WordOptions {

  private static final String MODULE = "WordOptions";
  private static final String[] METHOD_NAME = {"-init", "-upgrade","-clearShapes", "-upgradeAndClearShapes", "-toTxt"};

  /**
   * 检查文件格式,只支持doc和docx,否则抛出异常
   * @param filePath 文件路径
   */
  public void detectFileFormat(String filePath){
    try{
      int loadFormat=FileFormatUtil.detectFileFormat(filePath).getLoadFormat();
      if(loadFormat!= LoadFormat.DOCX&&loadFormat!=LoadFormat.DOC){
        throw new BaseException("不支持的文件格式",400,"WordOptions.detectFileFormat",filePath.toString());
      }
    } catch (Exception e) {
      throw new BaseException("文件损坏",400,"WordOptions.detectFileFormat",filePath.toString());
    }
  }

  /**
   * 初始化Word文件
   * @param filePath 文件路径
   * @return         文档对象 {@link Document}
   */
  public Document init(String filePath) {
    try(InputStream stream = Files.newInputStream(Path.of(filePath))){
      return new Document(stream);
    } catch (Exception e) {
      if (isCheckEncrypt(filePath)) {
        throw new BaseException("文件加密",400,"WordOptions.init", filePath);
      }
      throw new BaseException("文件损坏", 400,"WordOptions.init", filePath);
    }
  }

  /**
   * 文档是否加密
   * @param filePath 文件路径
   * @return true:加密 false:未加密 或者 文件损坏
   */
  public boolean isCheckEncrypt(String filePath) {
    // 如果文件加密 返回true
    try {
      return FileFormatUtil.detectFileFormat(filePath).isEncrypted();
    } catch (Exception e) {
      return false;
    }
  }

  /**
   * 文档格式升级为DOCX
   *
   * @param filePath 源文件路径
   * @return 升级后的文件路径
   */
  public String upgrade(String filePath, String targetPath) {
    if (Files.exists(Path.of(targetPath))) {
      return targetPath;
    }
    Document document = init(filePath);
    try {
      document.save(targetPath,new DocSaveOptions());
    } catch (Exception e) {
      throw new BaseException("文件格式升级为Docx时失败",500,"WordOptions.upgrade", filePath);
    }
    return targetPath;
  }

  /**
   * 判断文档中是否存在目录
   *
   * @param document 文档对象 {@link Document}
   * @return true:包含目录;false:不包含目录
   */
  public boolean isToc(Document document) {
    for (Field field : document.getRange().getFields()) {
      if (field.getType() == FieldType.FIELD_TOC) {
        return true;
      }
    }
    return false;
  }

  public String toTxt(Document document,String targetPath) {
    try{
      document.save(targetPath, getTxtSaveOptions());
      return targetPath;
    } catch (Exception e) {
      throw new BaseException("文件保存到Txt格式失败",500,"WordOptions.toTxt",targetPath);
    }
  }

  /**
   * 将Word文档保存为纯文本
   *
   * @param filePath   源文件路径(word文档路径)
   * @param targetPath 目标文件路径(纯文本路径)
   */
  public void toTxt(String filePath, String targetPath) {
    try{
      Document document = init(filePath);
      document.save(targetPath, getTxtSaveOptions());
    } catch (Exception e) {
      throw new BaseException("文件保存到Txt格式失败",500,"WordOptions.toTxt",filePath);
    }
  }

  public boolean isTitle(ParagraphFormat paragraphFormat) {
    return paragraphFormat.isHeading();
  }

  /**
   * 获取标题下所有的正文内容
   * @param paragraph 标题段落
   * @return 标题下所有的正文内容
   */
  public String getStandardPlainText(Paragraph paragraph) {
    StringBuilder content = new StringBuilder();
    Node currentNode = paragraph.getNextSibling();
    while (currentNode != null && currentNode.getNodeType() == NodeType.PARAGRAPH) {
      if (((Paragraph) currentNode).getParagraphFormat().isHeading()){
        break;
      }
      for (Run run : ((Paragraph) currentNode).getRuns()) {
        content.append(run.getText());
      }
      currentNode = currentNode.getNextSibling();
    }
    return content.toString().trim();
  }

  /**
   * 清除文档中的所有图形
   *
   * @param filePath 源文件路径
   * @return 清除完图形的源文件路径
   */
  public String clearShapes(String filePath) {
    try{
      Document document = init(filePath);
      NodeCollection<Shape> shapes = document.getChildNodes(NodeType.SHAPE, true);
      shapes.clear();
      document.save(filePath);
      return filePath;
    } catch (Exception e) {
      throw new BaseException(ErrorInfo.UPGRADE, e, MODULE + METHOD_NAME[2], filePath);
    }
  }

  /**
   * 升级并清除文档中的所有图形
   *
   * @param filePath   源文件路径
   * @param targetPath 目标文件路径
   * @return 目标文件路径
   */
  public String upgradeAndClearShapes(String filePath, String targetPath) {
    try{
      Document document = init(filePath);
      NodeCollection<Shape> shapes = document.getChildNodes(NodeType.SHAPE, true);
      shapes.clear();
      document.save(targetPath, new DocSaveOptions());
      return targetPath;
    } catch (Exception e) {
      throw new BaseException(ErrorInfo.UPGRADE, e, MODULE + METHOD_NAME[3], filePath);
    }
  }

  /**
   * 纯文本保存选项
   *
   * @return 纯文本保存选项 {@link TxtSaveOptions}
   */
  private TxtSaveOptions getTxtSaveOptions() {
    TxtSaveOptions saveOptions = new TxtSaveOptions();
    saveOptions.setSaveFormat(SaveFormat.TEXT);
    saveOptions.setEncoding(StandardCharsets.UTF_8);
    return saveOptions;
  }
}
