package com.fxiaoke.file.process.big.domain.exception;

import com.fxiaoke.file.process.big.domain.constant.ErrorInfo;

public class ParseException extends BaseException{

  public ParseException(ErrorInfo errorInfo, String module, Object... args) {
    super(errorInfo, module, args);
  }

  public ParseException(ErrorInfo errorInfo, Exception e, String module, Object... args) {
    super(errorInfo, e, module, args);
  }
}
