package com.fxiaoke.file.process.big.service;

import com.facishare.fsi.proxy.model.warehouse.a.ADownloadFile;
import com.facishare.fsi.proxy.model.warehouse.a.AGetFileMetaData;
import com.facishare.fsi.proxy.model.warehouse.a.User;
import com.facishare.fsi.proxy.service.AFileStorageService;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileDownloadRequest;
import com.facishare.stone.sdk.request.StoneFileGetMetaDataRequest;
import com.facishare.stone.sdk.request.StoneFileUploadRequest;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.fxiaoke.file.process.big.domain.constant.ErrorInfo;
import com.fxiaoke.file.process.big.domain.exception.BaseException;
import com.fxiaoke.file.process.big.domain.exception.ProcessException;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j(topic = "StoneService")
public class StoneService {
  private static final Integer EM_ID = -10000;
  private static final String BUSINESS = "FileProcess";
  private static final String MODULE="FileStorageService";
  private final StoneProxyApi stoneProxyApi;
  private final AFileStorageService aFileStorageService;

  public StoneService(StoneProxyApi stoneProxyApi, AFileStorageService aFileStorageService) {
    this.stoneProxyApi = stoneProxyApi;
    this.aFileStorageService = aFileStorageService;
  }

  public String uploadFileToCDN(String ea,String filePath){
    File file = new File(filePath);
    if (!file.exists()){
      throw new BaseException("文件不存在,请确认文件缓存是否已被移除",500,"StoneService",ea,filePath);
    }
    int fileSize= (int) file.length();
    String extension= FilenameUtils.getExtension(filePath);
    try(InputStream inputStream = new FileInputStream(filePath)){
      StoneFileUploadRequest stoneFileUploadRequest = new StoneFileUploadRequest();
      stoneFileUploadRequest.setEa(ea);
      stoneFileUploadRequest.setEmployeeId(EM_ID);
      stoneFileUploadRequest.setNeedCdn(true);
      stoneFileUploadRequest.setFileSize(fileSize);
      stoneFileUploadRequest.setExtensionName(extension);
      stoneFileUploadRequest.setNeedThumbnail(false);
      stoneFileUploadRequest.setBusiness(BUSINESS);
      stoneFileUploadRequest.setKeepFormat(true);
      StoneFileUploadResponse uploadResult = stoneProxyApi.uploadByStream("n", stoneFileUploadRequest, inputStream);
      return uploadResult.getPath();
    } catch (IOException | FRestClientException e) {
      log.error("Upload file to CDN fail,location:{},ea:{},filePath:{}",MODULE+"uploadFileToCDN",ea,filePath,e);
      throw new ProcessException(ErrorInfo.FILE_UPLOAD_FAIL,e,"StoneService",ea,filePath);
    }
  }

  public long getSizeByPath(String ea,String path,String securityGroup) {
    try {
      if (path.startsWith("N_") || path.startsWith("TN_")||path.startsWith("C")||path.startsWith("TC")) {
        StoneFileGetMetaDataRequest request = new StoneFileGetMetaDataRequest();
        request.setEa(ea);
        request.setEmployeeId(EM_ID);
        request.setBusiness(BUSINESS);
        request.setPath(path);
        request.setSecurityGroup(securityGroup);
        request.setNeedSha256(false);
        return stoneProxyApi.getBaseFileMeta(request).getSize();
      }else if (path.startsWith("A")||path.startsWith("TA")) {
        AGetFileMetaData.Arg arg = new AGetFileMetaData.Arg();
        arg.setFileName(path);
        arg.setBusiness(BUSINESS);
        arg.setFileSecurityGroup(securityGroup);
        User user = new User(ea, EM_ID);
        arg.setUser(user);
        return aFileStorageService.getFileMetaData(arg).getSize();
      }
    }catch (Exception e){
      log.warn("Get File Size fail,location:{},ea:{},path:{}",MODULE +"getSizeByPath", ea, path);
    }
    return -1;
  }

  public String downloadToLocal(String ea,String path,String securityGroup,String localCachePath){
    // Check if the file already exists
    if (new File(localCachePath).exists()){
      log.info("File already exists,ea:{},path:{},securityGroup:{},localCachePath:{}",ea,path,securityGroup,localCachePath);
      return localCachePath;
    }
    try {
      if (path.startsWith("N_") || path.startsWith("TN_")||path.startsWith("C")||path.startsWith("TC")) {
        return saveFileToLocalWithStream(ea, path, securityGroup, localCachePath);
      }else if (path.startsWith("A_") || path.startsWith("TA_")) {
        ADownloadFile.Arg arg = new ADownloadFile.Arg();
        arg.setaPath(path);
        arg.setBusiness(BUSINESS);
        arg.setFileSecurityGroup(securityGroup);
        User user = new User(ea, EM_ID);
        arg.setUser(user);
        byte[] fileBytes = aFileStorageService.downloadFile(arg).getData();
        return saveFileToLocalWithByte(fileBytes,fileBytes.length,localCachePath);
      }
    } catch (Exception e){
      log.error("Download file fail,location:{},ea:{},path:{}",MODULE +"downloadFileToLocal", ea, path);
    }
    return null;
  }


  private String saveFileToLocalWithByte(byte[] fileBytes,long size,String filePath) {
    log.info("Start save file to local fileSize:{} filePath:{}",size,filePath);
    try {
      FileUtils.forceMkdir(new File(FilenameUtils.getFullPath(filePath)));
      File file = new File(filePath);
      FileUtils.writeByteArrayToFile(file, fileBytes);
    } catch (IOException e) {
      log.error("Save file to local fail ,location:{},fileSize:{},filePath:{}",MODULE+"saveFileToLocal",size,filePath);
      return null;
    }
    log.info("Save file to local success fileSize:{} filePath:{}",size,filePath);
    return filePath;
  }

  private String saveFileToLocalWithStream(String ea,String path,String securityGroup,String localCachePath) throws FRestClientException, IOException {
    StoneFileDownloadRequest request = new StoneFileDownloadRequest();
    request.setEa(ea);
    request.setEmployeeId(EM_ID);
    request.setBusiness(BUSINESS);
    request.setPath(path);
    request.setSecurityGroup(securityGroup);
    log.info("Start save file to local downloadRequest:{}",request);
    try(InputStream stream = stoneProxyApi.downloadStream(request)) {
      FileUtils.forceMkdir(new File(FilenameUtils.getFullPath(localCachePath)));
      File file = new File(localCachePath);
      FileUtils.copyInputStreamToFile(stream, file);
    }
    log.info("Save file to local success downloadRequest:{},filePath:{}",request,localCachePath);
    return localCachePath;
  }

}
