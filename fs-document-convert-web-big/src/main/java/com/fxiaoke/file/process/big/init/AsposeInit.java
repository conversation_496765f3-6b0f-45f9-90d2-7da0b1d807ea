package com.fxiaoke.file.process.big.init;

import com.fxiaoke.file.process.big.domain.constant.Constants;
import com.fxiaoke.file.process.big.domain.exception.BaseException;
import java.io.File;
import java.util.Locale;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

@Service
@Slf4j(topic = "Initialize")
public class AsposeInit {

  private static final String MODULE = "InitializeService";
  private static final String LIC = "Aspose.Total.Java.lic";

  private static final String[] asposeCache = {
      Constants.WORD_CACHE_FOLDER,
      Constants.CELLS_CACHE_FOLDER,
      Constants.SLIDES_CACHE_FOLDER,
      Constants.PDF_CACHE_FOLDER
  };

  /**
   * ServerInitialize 1. Aspose License authentication 2. Aspose cacheFolder init
   */
  @PostConstruct
  public void registerLicense() {
    try {
      log.info("License authentication Start, LicenseName:{}", LIC);
      ClassPathResource resource = new ClassPathResource(LIC);
      com.aspose.slides.License pptLicense = new com.aspose.slides.License();
      pptLicense.setLicense(resource.getInputStream());
      com.aspose.cells.License xlsLicense = new com.aspose.cells.License();
      xlsLicense.setLicense(resource.getInputStream());
      com.aspose.words.License docLicense = new com.aspose.words.License();
      docLicense.setLicense(resource.getInputStream());
      resource.getInputStream().close();
      log.info("License authentication End, LicenseName:{}", LIC);

      Locale locale = new Locale("zh", "CN");
      Locale.setDefault(locale);

      for (String cacheFolder : asposeCache) {
        File file = new File(cacheFolder);
        if (!file.exists()) {
          FileUtils.forceMkdir(file);
          log.info("cache Folder not exist, create cache Folder:{}", file.getPath());
        } else {
          FileUtils.cleanDirectory(file);
          log.info("cache Folder exist, clean cache Folder:{}", file.getPath());
        }
      }
    } catch (Exception e) {
      throw new BaseException(e,500,MODULE+"-AsposeInit","Aspose registerLicense fail"+LIC);
    }
  }
}
