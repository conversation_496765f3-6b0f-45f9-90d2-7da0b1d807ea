package com.fxiaoke.file.process.big.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class Result<T> {

  private boolean success;
  private Integer code;
  private String message;
  private T data;

  private Result(boolean success, Integer code, String message) {
    this.success = success;
    this.code = code;
    this.message = message;
  }

  private Result(boolean success, Integer code, String message, T data) {
    this.success = success;
    this.code = code;
    this.message = message;
    this.data = data;
  }

  public static <T> Result<T> ok(T data) {
    return new Result<>(true, 200, "请求成功", data);
  }

  public static Result<String> error(int code, String message) {
    return new Result<>(false, code, message);
  }
}
