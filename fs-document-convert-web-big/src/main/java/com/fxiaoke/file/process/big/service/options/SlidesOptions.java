package com.fxiaoke.file.process.big.service.options;


import com.aspose.slides.BlobManagementOptions;
import com.aspose.slides.LoadOptions;
import com.aspose.slides.Presentation;
import com.aspose.slides.PresentationFactory;
import com.aspose.slides.PresentationLockingBehavior;
import com.aspose.slides.SaveFormat;
import com.fxiaoke.file.process.big.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.big.domain.constant.Constants;
import com.fxiaoke.file.process.big.domain.constant.ErrorInfo;
import com.fxiaoke.file.process.big.domain.exception.BaseException;
import java.nio.file.Files;
import java.nio.file.Path;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class SlidesOptions {

  private static final String MODULE = "SlidesOptions";
  private static final String[] METHOD_NAME = {"-init"};
  @Resource
  CmsPropertiesConfig cms;

  public Presentation init( String filePath) {
    try {
      return new Presentation(filePath, loadOptions());
    } catch (Exception e) {
      if (isCheckEncrypt(filePath)) {
        throw new BaseException(ErrorInfo.ENCRYPTION, e, MODULE + METHOD_NAME[0], filePath);
      }
      throw new BaseException(ErrorInfo.CORRUPTED, e, MODULE + METHOD_NAME[0], filePath);
    }
  }

  public boolean isCheckEncrypt(String filePath) {
    // 如果文件加密 返回true
    try {
      return PresentationFactory.getInstance().getPresentationInfo(filePath).isEncrypted();
    } catch (Exception e) {
      throw new BaseException(ErrorInfo.CORRUPTED, e, MODULE + METHOD_NAME[0], filePath);
    }
  }

  private BlobManagementOptions blobManagementOptions() {
    BlobManagementOptions options = new BlobManagementOptions();
    // 设置允许保存在内存中的二进制数据的最大大小(Aspose 默认600M,设置默认200M),超出部分将保存到临时文件中
    options.setMaxBlobsBytesInMemory(Constants.SLIDES_CACHE_SIZE);
    // 允许在保存期间生成临时文件
    options.setTemporaryFilesAllowed(true);
    // 设置保存临时文件的根文件夹
    options.setTempFilesRootPath(Constants.SLIDES_CACHE_FOLDER);
    // 设置在保存期间锁定文件的行为
    options.setPresentationLockingBehavior(PresentationLockingBehavior.KeepLocked);
    return options;
  }

  private LoadOptions loadOptions() {
    LoadOptions loadOptions = new LoadOptions();
    loadOptions.setBlobManagementOptions(blobManagementOptions());
    return loadOptions;
  }

  public String upgrade(String filePath) {
    try{
      String upgradeFilePath = filePath + Constants.SUFFIX;
      if (Files.exists(Path.of(upgradeFilePath))) {
        return upgradeFilePath;
      }
      Presentation presentation = init(filePath);
      presentation.save(upgradeFilePath, SaveFormat.Pptx);
      return upgradeFilePath;
    } catch (Exception e) {
      throw new BaseException(ErrorInfo.UPGRADE, e, MODULE + ".upgrade", filePath);
    }
  }
}
