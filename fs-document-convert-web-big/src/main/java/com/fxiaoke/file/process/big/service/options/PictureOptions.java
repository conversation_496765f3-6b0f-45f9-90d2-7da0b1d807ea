package com.fxiaoke.file.process.big.service.options;

import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.file.process.big.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.big.domain.exception.BaseException;
import com.fxiaoke.file.process.big.util.FileNameUtil;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okio.BufferedSink;
import org.apache.commons.io.FileUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

@Component
public class PictureOptions {

  private final OkHttpSupport client;
  private final String serverUrl;

  public PictureOptions(OkHttpSupport client, CmsPropertiesConfig config) {
    this.client = client;
    this.serverUrl = config.getImaginaryServerUrl();
  }

  private static final MediaType mediaType = MediaType.parse("image/png");

  public String convert(String filePath, String type) {
    File file = new File(filePath);
    String convertFilePath = FileNameUtil.replaceExtension(filePath, "." + type);
    File convertFile = new File(convertFilePath);
    if (convertFile.exists()) {
      return convertFilePath;
    }
    String url = serverUrl + "/convert?type=" + type;
    RequestBody body = new RequestBody() {
      @Nullable
      @Override
      public MediaType contentType() {
        return mediaType;
      }

      @Override
      public void writeTo(@NotNull BufferedSink sink) throws IOException {
        try (FileInputStream fileInputStream = new FileInputStream(file)) {
          byte[] buffer = new byte[8192];
          int bytesRead;
          while ((bytesRead = fileInputStream.read(buffer)) != -1) {
            sink.write(buffer, 0, bytesRead);
          }
        }
      }
    };

    Request request = new Request.Builder().url(url).post(body).build();

    try {
      return (String) client.syncExecute(request, new SyncCallback() {
        @Override
        public Object response(Response response) throws Exception {
          if (response.isSuccessful()) {
            if (response.body() != null) {
              try (InputStream inputStream = response.body().byteStream()) {
                FileUtils.copyInputStreamToFile(inputStream, convertFile);
              }
            }
            return convertFilePath;
          }
          throw new BaseException("image process fail", 500, "imageOptions", url, filePath, convertFilePath);
        }
      });
    } catch (Exception e) {
      throw new BaseException(e, 500, "imageOptions", url, filePath, convertFilePath);
    }
  }

}
