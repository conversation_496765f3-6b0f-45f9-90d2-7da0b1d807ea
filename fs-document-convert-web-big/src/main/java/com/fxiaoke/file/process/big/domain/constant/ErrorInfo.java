package com.fxiaoke.file.process.big.domain.constant;

import lombok.Getter;

@Getter
public enum ErrorInfo {

  INIT_FAIL(500, "服务繁忙", "Service initialization failed"),
  UNKNOWN_EXCEPTION(500, Constants.UNIVERSAL_ERROR, "unknown exception"),
  // 文件不存在或过期
  FILE_NOT_FOUND(400,"File not found", "File not found"),
  // 文件大小超出限制
  FILE_SIZE_EXCEEDED(400, "文件大小超出限制", "File size exceeded the limit"),
  // 文件下载失败
  FILE_DOWNLOAD_FAIL(500, "文件下载失败", "File download failed"),
  //页码超出限制
  FILE_PAGE_LIMIT(400,"文件页数超出限制","File page limit exceeded"),

  //文件加密
  FILE_ENCRYPTED(400,"不支持解析加密文件","File is encrypted"),
  //文件异常无法解析
  FILE_CORRUPTED(400,"文件无法解析,请检查文件是否加密或损坏","File is corrupted"),
  //文件上传失败
  FILE_UPLOAD_FAIL(500, "文件上传失败", "File upload failed"),



  IO_EXCEPTION(500, Constants.UNIVERSAL_ERROR, "IO exception"),
  DOCUMENT_DPI_LIMIT_EXCEEDED(400, Constants.UNIVERSAL_ERROR, "Document dpi exceeded the limit"),
  TASK_INTERRUPTED_GETMETAINFO(500, Constants.UNIVERSAL_ERROR,
      "Thread execution interrupt exception while file process"),
  TASK_TIMEOUT_GETMETAINFO(500, Constants.UNIVERSAL_ERROR,
      "Task timeout termination"),
  TASK_EXCEPTION_GETMETAINFO(500, Constants.UNIVERSAL_ERROR,
      "Thread execution exception file process"),
  PARAMETER_CHECKING(400, Constants.UNIVERSAL_ERROR, "Parameter checking failed"),
  ENCRYPTION(400, "非常抱歉,暂不支持预览加密或受保护的文件", "The document is encrypted or protected"),
  CORRUPTED(400, Constants.UNIVERSAL_ERROR, "Document corruption"),
  ASYNC_FAIL(500, Constants.UNIVERSAL_ERROR, "Async convert task failed"),
  UPGRADE(500,Constants.UNIVERSAL_ERROR,"Upgrade format fail" ),
  PARSE_FAIL(500,Constants.UNIVERSAL_ERROR,"Parse format fail" ),
  FILE_STORAGE_DOWNLOAD_FAILURE(500,Constants.UNIVERSAL_ERROR , "File storage download failure"),
  FILE_STORAGE_GET_SIZE_ERROR(500, Constants.UNIVERSAL_ERROR, "File storage get size error" ),
  PARSE_ERROR(500,Constants.PARSE_ERROR,"Parse content fail" ),

  STORAGE_DOWNLOAD_FAIL(500,"File Download Fail", "File Download Fail,"+Constants.STORAGE_COMMON_ERROR),
  STORAGE_GET_SIZE_FAIL(500,"Get File Size Fail","Get File Size Fail,"+Constants.STORAGE_COMMON_ERROR),
  STORAGE_UPLOAD_FILE_FAIL(500,"Upload File Fail","Upload File Fail,"+Constants.STORAGE_COMMON_ERROR ),
  STORAGE_SAVE_FILE_FAIL(500,"File save to local fail","File save to local fail,please check the disk space"),
  RE_ERROR(400,"该文件无法处理,请勿重试","The file cannot be processed, please do not try again"),
  ;

  private final int code;
  private final String message;
  private final String reason;

  ErrorInfo(int code, String message, String reason) {
    this.code = code;
    this.message = message;
    this.reason = reason;
  }
}
