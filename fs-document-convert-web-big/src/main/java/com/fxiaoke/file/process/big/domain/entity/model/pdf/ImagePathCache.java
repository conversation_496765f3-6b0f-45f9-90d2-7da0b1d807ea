package com.fxiaoke.file.process.big.domain.entity.model.pdf;

import com.fxiaoke.file.process.big.domain.entity.fieids.ConvertTaskCacheFieIds;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

@Data
@ToString
@NoArgsConstructor
@EqualsAndHashCode
public class ImagePathCache {
  // 页码
  @Property(ConvertTaskCacheFieIds.imagePaths_pageNumber)
  private int pageNumber;
  // 扩展名
  @Property(ConvertTaskCacheFieIds.imagePaths_extension)
  private String extension;
  // 本地缓存路径
  @Property(ConvertTaskCacheFieIds.imagePaths_path)
  private String path;

  @Property(ConvertTaskCacheFieIds.imagePaths_size)
  private long size;

  @Property(ConvertTaskCacheFieIds.imagePaths_width)
  private long width;

  @Property(ConvertTaskCacheFieIds.imagePaths_height)
  private long height;

  public ImagePathCache(int pageNumber, String extension, String path, long size, long width, long height) {
    this.pageNumber = pageNumber;
    this.extension = extension;
    this.path = path;
    this.size = size;
    this.width = width;
    this.height = height;
  }
}
