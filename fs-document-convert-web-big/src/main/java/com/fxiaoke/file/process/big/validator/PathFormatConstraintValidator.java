package com.fxiaoke.file.process.big.validator;

import com.fxiaoke.file.process.big.annotion.PathFormat;
import java.util.regex.Pattern;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 * 校验文件path格式
 * 1.不能为NULL或者为空
 * 2.必须以TC_或C_开头
 * 3.必须包含_
 * 4.根据_分割后的数组长度必须不为3
 */
public class PathFormatConstraintValidator implements ConstraintValidator<PathFormat, String> {
  Pattern pattern = Pattern.compile("^(N_|TN_|A_|TA_|G_|ALIOSS_).*\\.(doc|docx|ppt|pptx|pdf|xls|xlsx)$");
  @Override
  public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
    if (value == null||value.isEmpty()) {
      return false;
    }
    return pattern.matcher(value).matches();
  }
}
