package com.fxiaoke.file.process.big.annotion;

import com.fxiaoke.file.process.big.validator.PathFormatConstraintValidator;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import javax.validation.Constraint;
import javax.validation.Payload;


@Target({ElementType.PARAMETER, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = PathFormatConstraintValidator.class)
public @interface PathFormat {
  String message() default "This file type is not supported";

  Class<?>[] groups() default { };

  Class<? extends Payload>[] payload() default { };
}