package com.fxiaoke.file.process.big.service;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.DownloadFileRequest;
import com.fxiaoke.file.process.big.domain.dto.AliDownloadAuthDTO;
import com.fxiaoke.file.process.big.domain.exception.BaseException;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * creator: liuys
 * CreateTime: 2024-12-25
 * Description:
 */
@Service
@Slf4j
public class AliOssS3Service {
  @Resource
  private AliOssConfigInitialService aliOssConfigInitialService;
  private Long partSize;
  private int chunkDownloadParallelism;
  private int trafficLimit; // 下载限速
  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-dps-config", config -> {
      partSize = config.getLong("partSize", 10 * 1024L * 1024L);
      chunkDownloadParallelism = config.getInt("chunkDownloadParallelism", 1);
      trafficLimit = config.getInt("trafficLimit", 10);
    });
  }


  public String downloadFileFromAliOss(String ea, String objectKey, String localFilePath) {
    OSSClient ossClient = aliOssConfigInitialService.getOssClient(ea);
    AliDownloadAuthDTO credential = aliOssConfigInitialService.getCredential(ea);
    DownloadFileRequest downloadFileRequest = new DownloadFileRequest(credential.getBucketName(), objectKey);
    downloadFileRequest.setDownloadFile(localFilePath);
    downloadFileRequest.setTaskNum(chunkDownloadParallelism);
    downloadFileRequest.setTrafficLimit(trafficLimit * 1024 * 1024);
    downloadFileRequest.setPartSize(partSize * 1024 * 1024);
    try {
      ossClient.downloadFile(downloadFileRequest);
    } catch (Throwable e) {
      throw new BaseException("download file from ali oss fail", 500, "");
    }
    return localFilePath;
  }
}
