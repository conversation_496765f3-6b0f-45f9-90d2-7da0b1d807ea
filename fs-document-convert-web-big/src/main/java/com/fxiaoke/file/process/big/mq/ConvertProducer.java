package com.fxiaoke.file.process.big.mq;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.file.process.big.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.big.domain.api.Pdf2Image;
import com.fxiaoke.file.process.big.domain.api.Pdf2Image.Page;
import com.fxiaoke.file.process.big.domain.entity.model.pdf.ImagePathCache;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ConvertProducer {
  @Resource
  private CmsPropertiesConfig config;
  private AutoConfMQProducer producer;
  private String convertTaskTag;
  private String mqConvertContentTag;
  private String topic;

  @PostConstruct
  public void init(){
    convertTaskTag = config.getMqConvertTaskTag();
    mqConvertContentTag = config.getMqConvertContentTag();
    log.info("init producer,configName={},mqSection={}",config.getMqConfigName(), config.getMqConvertSection());
    producer=new AutoConfMQProducer(config.getMqConfigName(), config.getMqConvertSection());
    topic = producer.getDefaultTopic();
  }

  public void send(String taskId){
    log.info("send message,convert taskId:{}",taskId);
    Message message = new Message(topic, convertTaskTag, JSON.toJSONBytes(taskId));
    producer.send(message);
  }

  public void send(String ea,String taskId, List<ImagePathCache> imagePaths){
    List<Page> paths = new ArrayList<>();
    imagePaths.forEach(imagePath -> {
      Pdf2Image.Page page = new Pdf2Image.Page();
      page.setPageNumber(imagePath.getPageNumber());
      page.setExtension(imagePath.getExtension());
      page.setPath(imagePath.getPath());
      page.setSize(imagePath.getSize());
      page.setWidth(imagePath.getWidth());
      page.setHeight(imagePath.getHeight());
      paths.add(page);
    });
    Pdf2Image.Result result = Pdf2Image.Result.Success(ea, taskId, paths);
    log.info("send message,convert success,ea:{},taskId:{},status:{},pathsSize:{}",result.getEa(),result.getTaskId(),result.getStatus(),result.getPaths().size());
    Message message = new Message(topic, mqConvertContentTag, JSON.toJSONBytes(result));
    producer.send(message);
  }
}
