package com.fxiaoke.file.process.big.mq;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.file.process.big.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.big.domain.mq.ParseMessage;
import com.fxiaoke.file.process.big.service.OperationService;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;
@Slf4j
@Component
public class ParseConsumer implements ApplicationListener<ContextRefreshedEvent> {

  private final CmsPropertiesConfig config;
  private final OperationService operationService;
  private AutoConfMQPushConsumer consumer;

  public ParseConsumer(CmsPropertiesConfig config,OperationService operationService){
    this.config = config;
    this.operationService = operationService;
  }

  @PostConstruct
  public void init() {
    log.info("init consumer,configName={},mqSection={}",config.getMqConfigName(), config.getMqParseSection());
    consumer = new AutoConfMQPushConsumer(config.getMqConfigName(), config.getMqParseSection(),
        (MessageListenerConcurrently) (msgs, context) -> {
          log.info("receive message:{}", msgs.size());
          for (MessageExt msg : msgs) {
            MessageHelper.fillContextFromMessage(TraceContext.get(), msg);
            try {
              ParseMessage message = JSON.parseObject(msg.getBody(), ParseMessage.class);
              operationService.asyncParse(message.getId(),message.getEa(),message.isGenerateTxt(),message.getTxtPath(),message.getFilePath(),message.isStandard());
            } finally {
              TraceContext.remove();
            }
          }
          return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
    log.info("consumer subscribe topic:{}", config.getMqTopic());
  }

  @PreDestroy
  public void close() {
    consumer.close();
  }

  @Override
  public void onApplicationEvent(@NotNull ContextRefreshedEvent event) {
    if (consumer != null && event.getApplicationContext().getParent() == null) {
      consumer.start();
    }
  }
}
