package com.fxiaoke.file.process.big.domain.entity.fieids;

public class ConvertTaskCacheFieIds {
  public static final String id="_id";
  public static final String ea="EA";
  public static final String path="PT";
  public static final String size="SZ";
  public static final String securityGroup ="SG" ;

  public static final String pageCount = "PC";
  public static final String extension="EXT";
  public static final String localCachePath="LCP";
  public static final String expireTime="ET";



  public static final String status = "ST";
  public static final String failReason = "FR";
  public static final String heartbeatTime = "HT";
  public static final String retryCount = "RC";
  public static final String imageLocalCachePaths = "ILCP";

  public static final String imagePaths = "IP";
  public static final String imagePaths_pageNumber ="IP_PN";
  public static final String imagePaths_extension ="IP_EXT";
  public static final String imagePaths_path ="IP_PT";
  public static final String imagePaths_localCachePath ="IP_LCP";
  public static final String imagePaths_size = "IP_SZ";
  public static final String imagePaths_width = "IP_W";
  public static final String imagePaths_height = "IP_H";
}