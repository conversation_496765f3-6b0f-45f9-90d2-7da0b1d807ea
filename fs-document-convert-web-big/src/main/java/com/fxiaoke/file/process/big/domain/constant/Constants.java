package com.fxiaoke.file.process.big.domain.constant;

public class Constants {
  private Constants() {
  }
  public static final String UNIVERSAL_ERROR = "该文件不支持预览,请您下载后查看";

  public static final String STORAGE_COMMON_ERROR = "Please check whether the file exists or the file storage server is abnormal";

  public static final double IMAGE_MAX_DPI=2147483648D;
  public static final double IMAGE_STANDARD_WIDTH =1280D;
  public static final String HTML_SUFFIX = ".html";
  public static final String MODULE_NAME = "module:";
  public static final String ERROR_REASON = "reason:";
  public static final String SUFFIX="x";
  public static final String PARSE_ERROR = "解析文件失败";

  public static final String FILE_PATH_SEPARATOR ="_";

  public static final String FILE_C_PATH_SUFFIX ="C_";

  public static final String FILE_TC_PATH_SUFFIX ="TC_";

  public static final Integer FILE_PATH_BLANK_LENGTH = 3;

  // Aspose 临时缓存文件夹
  public static final String CACHE_FOLDER = "/opt/tomcat/localCache";
  public static final String SLIDES_CACHE_FOLDER = CACHE_FOLDER + "/slides";
  public static final String CELLS_CACHE_FOLDER = CACHE_FOLDER + "/cells";
  public static final String PDF_CACHE_FOLDER = CACHE_FOLDER + "/pdf";
  public static final String WORD_CACHE_FOLDER = CACHE_FOLDER + "/word";

  public static final long SLIDES_CACHE_SIZE = 200 * 1024 * 1024;
  public static final long CELLS_CACHE_SIZE = 200 * 1024 * 1024;
}
