package com.fxiaoke.file.process.big.mq;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.file.process.big.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.big.domain.mq.ParseMessage;
import com.fxiaoke.file.process.big.domain.mq.ShardContentMessage;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ParseProducer {
  @Resource
  CmsPropertiesConfig config;
  private AutoConfMQProducer producer;
  private String topic;
  private String parseTaskTag;
  private String contentTag;

  @PostConstruct
  public void init(){
    String configName = config.getMqConfigName();
    String mqSection = config.getMqParseSection();
    log.info("init producer,configName={},mqSection={}",configName, mqSection);
    producer=new AutoConfMQProducer(configName, mqSection);
    topic = producer.getDefaultTopic();
    parseTaskTag =config.getMqParseTaskTag();
    contentTag =config.getMqParseContentTag();
  }

  @PreDestroy
  public void shutDown() {
    producer.close();
  }

  public void send(ParseMessage parseMessage){
    log.info("send message:{}",parseMessage);
    Message message = new Message(topic, parseTaskTag, JSON.toJSONBytes(parseMessage));
    producer.send(message);
  }

  public void send(ShardContentMessage message){
    log.info("send message:{}",message);
    Message msg = new Message(topic, contentTag, JSON.toJSONBytes(message));
    producer.send(msg);
  }
}
