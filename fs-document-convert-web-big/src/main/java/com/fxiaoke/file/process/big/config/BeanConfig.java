package com.fxiaoke.file.process.big.config;

import com.facishare.dubbo.plugin.client.DubboRestFactoryBean;
import com.facishare.fsi.proxy.FsiServiceProxyFactory;
import com.facishare.fsi.proxy.FsiServiceProxyFactoryBean;
import com.facishare.fsi.proxy.FsiWarehouseProxyFactory;
import com.facishare.fsi.proxy.service.AFileStorageService;
import com.facishare.fsi.proxy.service.GFileStorageService;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.restful.client.FRestApiProxyFactoryBean;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.common.http.spring.HttpSupportFactoryBean;
import com.github.mongo.support.MongoDataStoreFactoryBean;
import com.xxl.job.core.executor.XxlJobExecutor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;

@Configuration
@ImportResource(locations = {
    "classpath*:spring/ei-ea-converter.xml",
    "classpath:fs-uc-cache.xml"
})
public class BeanConfig {
  @Bean
  public MongoDataStoreFactoryBean dpsDataStore(CmsPropertiesConfig config){
    MongoDataStoreFactoryBean mongoDataStoreFactoryBean = new MongoDataStoreFactoryBean();
    mongoDataStoreFactoryBean.setConfigName(config.getFsDpsMongoConfigName());
    return mongoDataStoreFactoryBean;
  }

  @Bean
  public HttpSupportFactoryBean imaginaryOkHttpClient(CmsPropertiesConfig config){
    HttpSupportFactoryBean factoryBean = new HttpSupportFactoryBean();
    factoryBean.setConfigName(config.getImaginaryHttpClientConfigName());
    factoryBean.init();
    return factoryBean;
  }

  @Bean
  public FRestApiProxyFactoryBean<StoneProxyApi> stoneProxyApi() {
    FRestApiProxyFactoryBean<StoneProxyApi> factoryBean = new FRestApiProxyFactoryBean<>();
    factoryBean.setType(StoneProxyApi.class);
    return factoryBean;
  }

  @Bean
  public FsiWarehouseProxyFactory fsiWarehouseProxyFactory(CmsPropertiesConfig config, DubboRestFactoryBean<EnterpriseEditionService> enterpriseEditionService) {
    FsiWarehouseProxyFactory factory = new FsiWarehouseProxyFactory();
    factory.setConfigKey(config.getFsiProxyConfigName());
    factory.setEnterpriseEditionService(enterpriseEditionService.getObject());
    factory.init();
    return factory;
  }

  @Bean
  public FsiServiceProxyFactoryBean<AFileStorageService> aFileStorageService(FsiServiceProxyFactory fsiServiceProxyFactory) {
    FsiServiceProxyFactoryBean<AFileStorageService> factoryBean = new FsiServiceProxyFactoryBean<>();
    factoryBean.setType(AFileStorageService.class);
    factoryBean.setFactory(fsiServiceProxyFactory);
    return factoryBean;
  }

  @Bean
  public FsiServiceProxyFactoryBean<NFileStorageService> nFileStorageService(FsiServiceProxyFactory fsiServiceProxyFactory) {
    FsiServiceProxyFactoryBean<NFileStorageService> factoryBean = new FsiServiceProxyFactoryBean<>();
    factoryBean.setType(NFileStorageService.class);
    factoryBean.setFactory(fsiServiceProxyFactory);
    return factoryBean;
  }

  @Bean
  public FsiServiceProxyFactory fsiServiceProxyFactory(CmsPropertiesConfig config) {
    FsiServiceProxyFactory factory = new FsiServiceProxyFactory();
    factory.setConfigKey(config.getFsiProxyConfigName());
    factory.init();
    return factory;
  }

  @Bean
  public FsiServiceProxyFactoryBean<GFileStorageService> gFileStorageService(FsiServiceProxyFactory fsiServiceProxyFactory) {
    FsiServiceProxyFactoryBean<GFileStorageService> factoryBean = new FsiServiceProxyFactoryBean<>();
    factoryBean.setType(GFileStorageService.class);
    factoryBean.setFactory(fsiServiceProxyFactory);
    return factoryBean;
  }

  @Bean(initMethod = "start", destroyMethod = "destroy")
  public XxlJobExecutor xxlJobExecutor(CmsPropertiesConfig config) {
    XxlJobExecutor executor = new XxlJobExecutor();
    executor.setIp(config.getExecutorIp());
    executor.setPort(config.getExecutorPort());
    executor.setAppName(config.getAppName());
    executor.setAdminAddresses(config.getAdminAddresses());
    executor.setLogPath(config.getExecutorLogpath());
    executor.setAccessToken(config.getAccessToken());
    return executor;
  }
}
