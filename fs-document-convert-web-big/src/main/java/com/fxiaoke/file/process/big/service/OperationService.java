package com.fxiaoke.file.process.big.service;

import com.fxiaoke.file.process.big.domain.cache.ParseCache;
import com.fxiaoke.file.process.big.domain.constant.ErrorInfo;
import com.fxiaoke.file.process.big.domain.constant.FileType;
import com.fxiaoke.file.process.big.domain.exception.ParseException;
import com.fxiaoke.file.process.big.domain.model.word.Content;
import com.fxiaoke.file.process.big.domain.mq.ParseMessage;
import com.fxiaoke.file.process.big.domain.mq.ShardContentMessage;
import com.fxiaoke.file.process.big.mq.ParseProducer;
import com.fxiaoke.file.process.big.service.aspose.PDFService;
import com.fxiaoke.file.process.big.service.aspose.SlidesService;
import com.fxiaoke.file.process.big.service.aspose.WordService;
import com.fxiaoke.file.process.big.util.FileNameUtil;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.base.Strings;
import java.util.concurrent.TimeUnit;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;

@Service
public class OperationService{

  // 缓存避免重复下载
  Cache<String, ParseCache> cache = Caffeine.newBuilder()
      .expireAfterWrite(1, TimeUnit.DAYS) // 设置缓存过期时间
      .initialCapacity(1000)
      .maximumSize(1000) // 设置缓存最大容量
      .build();

  ParseProducer parseProducer;
  WordService wordService;
  PDFService pdfService;
  SlidesService slidesService;
  FileStorageService storageService;


  public OperationService(ParseProducer parseProducer, WordService wordService,PDFService pdfService,
      SlidesService slidesService,FileStorageService storageService) {
    this.parseProducer = parseProducer;
    this.storageService = storageService;
    this.wordService = wordService;
    this.pdfService = pdfService;
    this.slidesService = slidesService;
  }
  IFileParseService getParseService(String filePath){
    String extension = FileNameUtil.getExtension(filePath);
    if (FileType.isWords(extension)){
      return wordService;
    }
    if (FileType.isPDF(extension)){
      return pdfService;
    }
    if (FileType.isSlides(extension)){
      return slidesService;
    }
    return null;
  }

  public Content parse(String id,String ea, boolean generateTxt, String path) {

    // 从缓存中判断是否已经处理过
    ParseCache parseCache = cache.getIfPresent(path);
    String txtPath = "";
    String filePath;
    long fileSize = 0;
    if (parseCache == null){
      filePath= storageService.generateFilePath(ea, path);
      fileSize = storageService.getSizeByPath(FilenameUtils.getBaseName(path), ea,filePath);
      storageService.downloadFileToLocal(FilenameUtils.getBaseName(path), ea, fileSize, filePath);
    }else if (parseCache.isStatus()){
      filePath = parseCache.getFilePath();
      txtPath= parseCache.getTxtPath();
    }else {
      throw new ParseException(ErrorInfo.RE_ERROR,"WordService-parse",id,ea,path);
    }
    // 根据是否传递任务ID,判断是否异步
    boolean async = !Strings.isNullOrEmpty(id);

    // 解析文件
    Content content= getParseService(filePath).parse(filePath,async);

    // 如果是异步发送异步任务消息
    if (async){
      parseProducer.send(new ParseMessage(id,ea,filePath,generateTxt,txtPath,content.isStandard()));
    }

    // 不是异步,缓存未命中,且需要生成txt文件,则生成txt文件并上传到文件系统
    if (!async&&Strings.isNullOrEmpty(txtPath)&&generateTxt){
      txtPath = storageService.uploadFile(ea,getParseService(filePath).toTxt(filePath));
    }
    content.setTxtPath(txtPath);

    // 更新缓存
    cache.put(path,ParseCache.builder().filePath(filePath).fileSize(fileSize).txtPath(txtPath).standard(content.isStandard()).status(true).build());
    return content;
  }

  public void asyncParse(String id, String ea, boolean generateTxt, String txtPath, String filePath,boolean standard){

    // 异步解析文件
    getParseService(filePath).asyncParse(id,ea,filePath,standard);

    // 如果需要生成txt文件,并且在缓存中不存在,则生成txt文件并上传到文件系统
    if (generateTxt&&Strings.isNullOrEmpty(txtPath)){
      txtPath = storageService.uploadFile(ea,getParseService(filePath).toTxt(filePath));
    }
    parseProducer.send(new ShardContentMessage(id,ea,txtPath,true));
  }

}
