package com.fxiaoke.file.process.big.service.aspose;

import com.aspose.words.Body;
import com.aspose.words.Document;
import com.aspose.words.LayoutCollector;
import com.aspose.words.Node;
import com.aspose.words.NodeCollection;
import com.aspose.words.NodeType;
import com.aspose.words.Paragraph;
import com.aspose.words.ParagraphFormat;
import com.aspose.words.Run;
import com.aspose.words.SaveFormat;
import com.fxiaoke.file.process.big.domain.constant.ErrorInfo;
import com.fxiaoke.file.process.big.domain.constant.FileType;
import com.fxiaoke.file.process.big.domain.exception.ParseException;
import com.fxiaoke.file.process.big.domain.model.word.Content;
import com.fxiaoke.file.process.big.domain.model.word.ShardContent;
import com.fxiaoke.file.process.big.domain.mq.ShardContentMessage;
import com.fxiaoke.file.process.big.mq.ParseProducer;
import com.fxiaoke.file.process.big.service.IFileParseService;
import com.fxiaoke.file.process.big.service.options.WordOptions;
import com.fxiaoke.file.process.big.util.FileNameUtil;
import com.google.common.base.Strings;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j(topic = "WordService")
public class WordService implements IFileParseService {

  ParseProducer parseProducer;
  WordOptions wordOptions;

  public WordService(WordOptions wordOptions, ParseProducer parseProducer) {
    this.wordOptions = wordOptions;
    this.parseProducer = parseProducer;
  }

  /**
   * 解析Word文件 返回文件内容
   *
   * @param  filePath  文件路径
   * @return Content   文件内容 {@link Content}
   */
  @Override
  public Content parse(String filePath,boolean async) {

    // 检查文件格式,只支持doc和docx,否则抛出异常
    wordOptions.detectFileFormat(filePath);

    // Doc 文件先转换成 Docx 文件
    String extension = FileNameUtil.getExtension(filePath);
    if(FileType.isDoc(extension)){
      filePath =wordOptions.upgrade(filePath,FileNameUtil.upgrade(filePath));
    }
    // 初始化Word文件
    Document document = wordOptions.init(filePath);

    // 判断文件是否为标准的Word文件,即是否存在Toc
    boolean standard = wordOptions.isToc(document);

    if (async){
      // 异步解析
      return Content.builder().standard(standard).build();
    }

    // 根据standard 的值,采取不同策略,获取文件内容片段
    List<ShardContent> shardContents;
    if (standard){
      shardContents=parsePlainTxtFromStandard(document);
    }else {
      shardContents=parsePlainTxtFromNoStandard(document);
    }

    return Content.builder().ShardContents(shardContents).standard(standard).build();
  }

  public String toTxt(String filePath){

    // 检查文件格式,只支持doc和docx,否则抛出异常
    wordOptions.detectFileFormat(filePath);

    // Doc 文件先转换成 Docx 文件
    String extension = FileNameUtil.getExtension(filePath);
    if(FileType.isDoc(extension)){
      filePath =wordOptions.upgrade(filePath,FileNameUtil.upgrade(filePath));
    }
    String txtFilePath = FileNameUtil.toTxt(filePath);
    wordOptions.toTxt(filePath,txtFilePath);
    return txtFilePath;
  }

  public void asyncParse(String id, String ea, String filePath, boolean standard) {
    try {
      Document document = wordOptions.init(filePath);
      if (standard) {
        asyncParesStandardDocument(id, ea, document);
      } else {
        asyncParesNonStandardDocument(id, ea, document);
      }
    } catch (Exception e) {
      throw new ParseException(ErrorInfo.PARSE_ERROR, e, "WordService.asyncParse", filePath);
    }
  }


  /**
   * 获取标准文档内容
   * 标准文档中,内容以标题为分隔,每个标题下面的内容为一个片段
   * @param document 文档对象 {@link Document}
   * @return  片段内容的集合 {@link ShardContent}
   */
  private List<ShardContent> parsePlainTxtFromStandard(Document document) {
    List<ShardContent> shardContentList = new ArrayList<>();
    NodeCollection<Body> childNodes = document.getChildNodes(NodeType.BODY, true);
    LayoutCollector collector = new LayoutCollector(document);
    int sequence = 1;
      for (Body body:childNodes){
        NodeCollection<Paragraph> paragraphs = body.getChildNodes(NodeType.PARAGRAPH, true);
        for (Paragraph paragraph : paragraphs) {
          if (paragraph.getParagraphFormat().isHeading()) {
            int pageIndex=0;
            try {
              pageIndex = collector.getStartPageIndex(paragraph);
            } catch (Exception e) {
              log.warn("Failed to get header pageIndex", e);
            }
            ShardContent shardContent = getShardContentFromParagraph(sequence++, paragraph, pageIndex);
            shardContentList.add(shardContent);
          }
        }
      }
    return shardContentList;
  }

  private ShardContent getShardContentFromParagraph(int sequence,Paragraph paragraph,int pageIndex){
    ShardContent shardContent = new ShardContent();
    shardContent.setSequence(sequence);
    shardContent.setLevel(getLevel(paragraph.getParagraphFormat()));
    try {
      shardContent.setTitle(paragraph.toString(SaveFormat.TEXT).replaceAll("[\\r\\n\\f]", "").trim());
    } catch (Exception e) {
      log.warn("Failed to get header content", e);
    }
    shardContent.setPageIndex(pageIndex);
    shardContent.setContent(getStandardPlainText(paragraph));
    return shardContent;
  }
  private int getLevel(ParagraphFormat paragraphFormat) {
    return paragraphFormat.getStyle().getParagraphFormat().getOutlineLevel();
  }

  private String getStandardPlainText(Paragraph paragraph) {
    StringBuilder content = new StringBuilder();
    Node currentNode = paragraph.getNextSibling();
    while (currentNode != null && currentNode.getNodeType() == NodeType.PARAGRAPH && !((Paragraph) currentNode).getParagraphFormat().isHeading()) {
      for (Run run : ((Paragraph) currentNode).getRuns()) {
        content.append(run.getText());
      }
      currentNode = currentNode.getNextSibling();
    }
    return content.toString().trim();
  }

  /**
   * 获取非标准文档内容
   * 非标准文档中,内容以页为分隔,每页的内容为一个片段
   * @param document 文档对象 {@link Document}
   * @return 片段内容的集合 {@link ShardContent}
   */
  private List<ShardContent> parsePlainTxtFromNoStandard(Document document){
    List<ShardContent> shardContentList = new ArrayList<>();
    try {
      int sequence = 1;
      NodeCollection<Body> childNodes =document.getChildNodes(NodeType.BODY, true);
      LayoutCollector collector = new LayoutCollector(document);
      // 获取文档中的所有段落
      for (Body body : childNodes) {
        NodeCollection<Paragraph> paragraphs = body.getChildNodes(NodeType.PARAGRAPH, true);
        for (Paragraph paragraph : paragraphs) {
          String text = getNonStandardPlainText(paragraph);
          if (Strings.isNullOrEmpty(text)) {
            continue;
          }
          int pageIndex=0;
          try{
            pageIndex = collector.getStartPageIndex(paragraph);
          }catch (Exception e) {
            log.warn("Failed to get header pageIndex", e);
          }
          ShardContent shardContent = getShardContentFormParagraph(sequence++, pageIndex, paragraph.getParagraphFormat(), text);
          shardContentList.add(shardContent);
        }
      }
    } catch (Exception e) {
      log.warn("Failed to get pageCount", e);
    }
    return shardContentList;
  }

  private void asyncParesStandardDocument(String id, String ea,Document document) {
      // 获取所有的 body section
      NodeCollection<Body> childNodes = document.getChildNodes(NodeType.BODY, true);
      // 分片内容块的起始序号
      int sequence = 1;
      for (Body body : childNodes) {
        // 获取所有的 paragraph
        NodeCollection<Paragraph> paragraphs = body.getChildNodes(NodeType.PARAGRAPH, true);
        LayoutCollector collector = new LayoutCollector(document);
        ShardContentMessage message = new ShardContentMessage(id, ea);
        for (Paragraph paragraph : paragraphs) {
          // 判断是否是标题
          if (paragraph.getParagraphFormat().isHeading()) {
            ShardContent shardContent = new ShardContent();
            shardContent.setSequence(sequence++);
            int outlineLevel = paragraph.getParagraphFormat().getStyle().getParagraphFormat().getOutlineLevel();
            shardContent.setLevel(outlineLevel);
            try {
              shardContent.setTitle(paragraph.toString(SaveFormat.TEXT).replaceAll("[\\r\\n\\f]", "").trim());
            } catch (Exception e) {
              log.warn("Failed to get header content", e);
            }
            try {
              // 获取标题所在页码
              shardContent.setPageIndex(collector.getStartPageIndex(paragraph));
            } catch (Exception e) {
              log.warn("Failed to get header pageIndex", e);
            }
            // 获取标题下正文内容
            shardContent.setContent(getStandardPlainText(paragraph));
            message.setShardContent(shardContent);
            // 发送消息
            parseProducer.send(message);
          }
        }
      }
  }

  private void asyncParesNonStandardDocument(String id, String ea,Document document) {
    try {
      ShardContentMessage message = new ShardContentMessage(id, ea);
      int sequence = 1;
      NodeCollection<Body> childNodes = document.getChildNodes(NodeType.BODY, true);
      LayoutCollector collector = new LayoutCollector(document);
      // 获取文档中的所有段落
      for (Body body : childNodes) {
        NodeCollection<Paragraph> paragraphs = body.getChildNodes(NodeType.PARAGRAPH, true);
        for (Paragraph paragraph : paragraphs) {
          ShardContent content = new ShardContent();
          String text = getNonStandardPlainText(paragraph);
          if (Strings.isNullOrEmpty(text)) {
            continue;
          }
          content.setSequence(sequence++);
          ParagraphFormat paragraphFormat = paragraph.getParagraphFormat();
          setTitleAndLevel(content, text, paragraphFormat);
          content.setPageIndex(collector.getStartPageIndex(paragraph));
          message.setShardContent(content);
          parseProducer.send(message);
        }
      }
    } catch (Exception e) {
      log.warn("Failed to get content,id:{},ea:{}", id, ea, e);
    }
  }

  private void setTitleAndLevel(ShardContent content, String text, ParagraphFormat paragraphFormat) {
    boolean titleOrNot = wordOptions.isTitle(paragraphFormat);
    content.setTitleOrNot(titleOrNot);
    if (titleOrNot) {
      content.setLevel(getLevel(paragraphFormat));
      content.setTitle(text);
    } else {
      content.setLevel(9);
      content.setContent(text);
    }
  }
  private String getNonStandardPlainText(Paragraph paragraph){
    StringBuilder content = new StringBuilder();
    for (Run run : paragraph.getRuns()) {
      content.append(run.getText());
    }
    return content.toString();
  }
  private ShardContent getShardContentFormParagraph(int sequence,int pageIndex,ParagraphFormat paragraphFormat,String plainText){
    ShardContent content = new ShardContent();
    content.setSequence(sequence);
    setTitleAndLevel(content, plainText, paragraphFormat);
    content.setPageIndex(pageIndex);
    return content;
  }
}
