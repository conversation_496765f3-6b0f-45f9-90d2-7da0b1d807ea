package com.fxiaoke.file.process.big.dao;

import com.fxiaoke.file.process.big.domain.entity.ConvertTaskCache;
import com.fxiaoke.file.process.big.domain.entity.model.pdf.ImagePathCache;
import java.util.List;

public interface IConvertTaskCacheDao {

  ConvertTaskCache findTask(String id);

  ConvertTaskCache findTask(String ea, String path, String securityGroup);

  List<ImagePathCache> findImagePathCache(String id);

  String initTask(String ea, String path, String securityGroup, long size, String extension, int pageCount,
      String localCachePath);

  void retryTask(String id, boolean restStatus);

  void updateStatus(String id, int status, String failReason);

  void addImagePathCache(String taskId, ImagePathCache imagePathCache);

  void deleteBySmbPath(String smbPath);
}
