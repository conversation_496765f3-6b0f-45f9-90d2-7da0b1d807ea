package com.fxiaoke.file.process.big.domain.entity;

import com.fxiaoke.file.process.big.domain.entity.fieids.FileMetaField;
import com.fxiaoke.file.process.big.domain.entity.fieids.IdField;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;
import org.mongodb.morphia.annotations.*;

@Indexes({
  @Index(fields = {@Field(FileMetaField.objectKey)}, options = @IndexOptions(background = true)),
  @Index(fields = {@Field(FileMetaField.filePath)}, options = @IndexOptions(background = true, unique = true)),
  @Index(fields = {@Field(FileMetaField.ownEA)}, options = @IndexOptions(background = true)),
  @Index(fields = {@Field(IdField.createTime)}, options = @IndexOptions(background = true)),
  @Index(fields = {@Field(FileMetaField.deleted), @Field(FileMetaField.ossDeleted)}, options = @IndexOptions(background = true)),
})
@Getter
@Setter
@Builder
@Entity(value = "file_meta", noClassnameStored = true)
public class FileMetaEntity extends IdEntity {
  @Property(FileMetaField.eTag)
  private String eTag;
  @Property(FileMetaField.objectKey)
  private String objectKey;  //Ali-OSS 对应的Key值
  @Property(FileMetaField.name)
  private String name;
  @Property(FileMetaField.size)
  private Long size;
  @Property(FileMetaField.filePath)
  private String filePath;
  @Property(FileMetaField.fileExt)
  private String fileExt;
  @Property(FileMetaField.mimeType)
  private String mimeType;
  @Property(FileMetaField.ownEA)
  private String ownEA;
  @Property(FileMetaField.deleted)
  private boolean deleted;
  @Property(FileMetaField.ossDeleted)
  private boolean ossDeleted;                     //阿里云OSS上的源文件是否已删除
  @Property(FileMetaField.appName)
  private String appName;
  @Property(FileMetaField.expireTime)
  private Long expireTime;
  @Property(FileMetaField.gcTime)
  private Long gcTime;

  @Tolerate
  public FileMetaEntity() {
  }
}
