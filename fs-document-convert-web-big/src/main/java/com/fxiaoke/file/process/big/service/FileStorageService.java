package com.fxiaoke.file.process.big.service;

import com.facishare.fsi.proxy.model.warehouse.a.ADownloadFile;
import com.facishare.fsi.proxy.model.warehouse.a.AGetFileMetaData;
import com.facishare.fsi.proxy.model.warehouse.a.User;
import com.facishare.fsi.proxy.model.warehouse.g.GFileDownload;
import com.facishare.fsi.proxy.model.warehouse.g.GFileDownload.Result;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NDownloadFile;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NGetFileMetaData.Arg;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempFileUpload;
import com.facishare.fsi.proxy.service.AFileStorageService;
import com.facishare.fsi.proxy.service.GFileStorageService;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.file.process.big.domain.constant.ErrorInfo;
import com.fxiaoke.file.process.big.domain.entity.FileMetaEntity;
import com.fxiaoke.file.process.big.domain.exception.BaseException;
import com.fxiaoke.file.process.big.util.DataTimeFormatUtil;
import com.fxiaoke.file.process.big.util.SampleUUID;
import com.fxiaoke.file.process.big.util.SourceUser;
import com.github.autoconf.ConfigFactory;
import java.io.File;
import java.io.IOException;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;

@Slf4j(topic = "fileStorageService")
@Service
public class FileStorageService {

  private static final String MODULE = "FileStorageProxy-";
  private static final String BUSINESS = "FilePreview";
  @Resource
  private AFileStorageService aFileStorageService;
  @Resource
  private NFileStorageService nFileStorageService;
  @Resource
  private GFileStorageService gFileStorageService;
  @Resource
  private OkHttpSupport client;
  @Resource
  private AliOssS3Service aliOssS3Service;
  private String dataDir;
  String securityGroup;
  private String domain;
  private String getFileMetaUrl;

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-dps-config", config -> {
      dataDir = config.get("dataDir");
      domain = config.get("domain");
      securityGroup = config.get("securityGroup", "XiaoKeNetDisk");
      log.info("FilePreview Default Folder Path:{}", dataDir);
      getFileMetaUrl = config.get("getFileMetaUrl");
    });
  }

  public byte[] getBytesByPath(String path, String ea, int employeeId,String securityGroup) {
    log.info("Start download-args,ea:{},employeeId:{},path:{}", ea,SourceUser.sourceUser(ea,employeeId), path);
    byte[] fileBytes;
    try {
      if (path.startsWith("A_") || path.startsWith("TA_")) {
        ADownloadFile.Arg arg = new ADownloadFile.Arg();
        arg.setaPath(path);
        arg.setBusiness(BUSINESS);
        arg.setFileSecurityGroup(securityGroup);
        User user = new User(ea, employeeId);
        arg.setUser(user);
        fileBytes = aFileStorageService.downloadFile(arg).getData();
      } else if (path.startsWith("G_")) {
        GFileDownload.Arg arg = new GFileDownload.Arg();
        arg.downloadUser = "E." + employeeId;
        arg.gPath = path;
        arg.downloadSecurityGroup = securityGroup;
        fileBytes = gFileStorageService.downloadFile(arg).data;
      } else {
        NDownloadFile.Arg arg = new NDownloadFile.Arg();
        arg.setnPath(path);
        arg.setDownloadUser("E." + employeeId);
        arg.setEa(ea);
        arg.setDownloadSecurityGroup(securityGroup);
        fileBytes = nFileStorageService.nDownloadFile(arg, ea).getData();
      }
    } catch (Exception e) {
      throw new BaseException(ErrorInfo.STORAGE_DOWNLOAD_FAIL, e,MODULE +"GetBytesByPath",ea,SourceUser.sourceUser(ea,employeeId), path);
    }
    log.info("End download-args,ea:{},employeeId:{},path:{}", ea,SourceUser.sourceUser(ea,employeeId),path);
    return fileBytes;
  }

  public long getSizeByPath(String path, String ea,String filePAth){
    long fileSize = getSizeByPath(path, ea, -10000, filePAth, securityGroup);
    if (fileSize<0||fileSize>1024*1024*100 && (!path.startsWith("ALIOSS_"))){
      throw new BaseException(ErrorInfo.FILE_SIZE_EXCEEDED,MODULE +"GetSizeByPath",ea,SourceUser.sourceUser(ea,-10000), path);
    }
    return fileSize;
  }

  public long getSizeByPath(String path, String ea, int employeeId,String filePath,String securityGroup) {
    long size = 0;
    try {
      if (path.startsWith("A_") || path.startsWith("TA_")){
        AGetFileMetaData.Arg arg = new AGetFileMetaData.Arg();
        arg.setFileName(path);
        arg.setBusiness("Preview");
        arg.setFileSecurityGroup(securityGroup);
        User user = new User(ea, employeeId);
        arg.setUser(user);
        size = aFileStorageService.getFileMetaData(arg).getSize();
      }else if (path.startsWith("G_")) {
        GFileDownload.Arg arg = new GFileDownload.Arg();
        arg.downloadUser = "E." + employeeId;
        arg.gPath = path;
        arg.downloadSecurityGroup = securityGroup;
        Result result = gFileStorageService.downloadFile(arg);
        size = result.data.length;
        saveFileToLocal(result.data,size,filePath);
      } else if (path.startsWith("ALIOSS_")) {
        size = getMetaData(path).getSize();
      }else{
        Arg arg = new Arg();
        arg.setEa(ea);
        arg.setFileName(path);
        arg.setDownUser("E." + employeeId);
        arg.setDownloadSecurityGroup(securityGroup);
        size = nFileStorageService.nGetFileMetaData(arg, ea).getSize();
      }
    } catch (Exception e) {
      throw new BaseException(ErrorInfo.STORAGE_GET_SIZE_FAIL, e,MODULE +"GetSizeByPath",ea,SourceUser.sourceUser(ea,employeeId), path);
    }
    log.info("Get File Size success fileSize:{} args: ea:{}, employeeId:{},paths:{}",size, ea,SourceUser.sourceUser(ea,employeeId), path);
    return size;
  }

  public String uploadFile(String ea,String filePath){
    return uploadFile(ea,-10000,filePath,FilenameUtils.getExtension(filePath));
  }

  public String uploadFile(String ea,int employeeId,String filePath,String fileType) {
    String path;
    byte[] bytes;
    File file = new File(filePath);
    if (!file.exists()) {
      log.warn("File not found,ea:{},employeeId:{},filePath:{}", ea, "E." + employeeId, filePath);
    }
    try {
      bytes = FileUtils.readFileToByteArray(file);
    } catch (IOException e) {
      throw new BaseException(ErrorInfo.FILE_NOT_FOUND, e,MODULE +"uploadFile",ea,SourceUser.sourceUser(ea,employeeId),filePath,fileType);
    }
    path = uploadFile(bytes, ea, employeeId, fileType);
    return path;
  }

  public String uploadFile(byte[] bytes, String ea, int employeeId, String fileType) {
    int fileSize = bytes.length;
    log.info("Start uploadFile fileSize:{}  args: ea:{},employeeId:{}", fileSize, ea, "E." + employeeId);
    String path;
    try {
      NTempFileUpload.Arg arg = new NTempFileUpload.Arg();
      arg.setEa(ea);
      arg.setData(bytes);
      arg.setSourceUser("E." + employeeId);
      arg.setBusiness(BUSINESS);
      arg.setExtensionName(fileType); //不带 . 的后缀名
      path = nFileStorageService.nTempFileUpload(arg, ea).getTempFileName();
    } catch (Exception e) {
      throw new BaseException(ErrorInfo.STORAGE_UPLOAD_FILE_FAIL, e,MODULE +"uploadFile",ea,SourceUser.sourceUser(ea,employeeId),fileType);
    }
    log.info("Upload file success fileSize:{} fileNPath:{}  args: ea:{},employeeId:{},fileType:{}",fileSize, path,ea, SourceUser.sourceUser(ea,employeeId), fileType);
    return path;
  }

  public String downloadFileToLocal(String path, String ea,long size,String filePath){
    return downloadFileToLocal(path,ea,-10000,size,filePath,securityGroup);
  }

  public String downloadFileToLocal(String path, String ea, int employeeId, long size, String filePath, String securityGroup) {
    if (new File(filePath).isFile()) {
      return filePath;
    }
    if (path.startsWith("ALIOSS_")) {
      FileMetaEntity metaData = getMetaData(path);
      return saveFileFromAli(metaData.getObjectKey(), ea, filePath);
    } else {
      byte[] fileBytes = getBytesByPath(path, ea, employeeId, securityGroup);
      return saveFileToLocal(fileBytes, size, filePath);
    }
  }

  private FileMetaEntity getMetaData(String path) {
    String url = String.format(getFileMetaUrl, path);
    Request request = new Request.Builder()
        .url(url)
        .get()
        .build();
    String response = (String) client.syncExecute(request, new SyncCallback() {
      @Override
      public Object response(Response response) throws Exception {
        if (response.body() != null) {
          return response.body().string();
        }
        return null;
      }
    });
    Gson gson = new Gson();
    com.fxiaoke.file.process.big.domain.Result<FileMetaEntity> result = gson.fromJson(response, new TypeToken<com.fxiaoke.file.process.big.domain.Result<FileMetaEntity>>() {}.getType());
    return result.getData();
  }

  private String saveFileFromAli(String objectKey, String ea, String localFilePath) {
    try {
      FileUtils.forceMkdir(new File(FilenameUtils.getFullPath(localFilePath)));
      return aliOssS3Service.downloadFileFromAliOss(ea, objectKey, localFilePath);
    } catch (IOException e) {
      throw new BaseException(ErrorInfo.STORAGE_SAVE_FILE_FAIL, e,MODULE +"saveFileToLocal",localFilePath);
    }
  }

  private String saveFileToLocal(byte[] fileBytes,long size,String filePath) {
    log.info("Start save file to local fileSize:{} filePath:{}",size,filePath);
    try {
      FileUtils.forceMkdir(new File(FilenameUtils.getFullPath(filePath)));
      File file = new File(filePath);
      FileUtils.writeByteArrayToFile(file, fileBytes);
    } catch (IOException e) {
      throw new BaseException(ErrorInfo.STORAGE_SAVE_FILE_FAIL, e,MODULE +"saveFileToLocal",filePath);
    }
    log.info("Save file to local success fileSize:{} filePath:{}",size,filePath);
    return filePath;
  }


  public String generateFilePath(String ea,String path){
    String dirPath = generateDirAsEa(ea);
    String fileName = generateFileNameAsPath(path);
    return FilenameUtils.concat(dirPath, fileName);
  }

  /**
   根据当前系统时间与ea生成临时文件夹路径
   * @param ea 用户ea
   * @return 临时文件夹
   */

  private String generateDirAsEa(String ea) {
    String yyyyMM = DataTimeFormatUtil.formatDate("yyyyMM");
    String dd = DataTimeFormatUtil.formatDate("dd");
    String hh = DataTimeFormatUtil.formatDate("HH");
    return String.format("%s/%s/%s/%s/%s/%s/%s", this.dataDir, "dps", yyyyMM, dd, hh, ea, SampleUUID.getUUID());
  }

  /**
   根据path生成临时文件名
   * @param path 文件在文件服务中的唯一标识字符串
   * @return 临时文件名
   */
  private String generateFileNameAsPath(String path) {
    return SampleUUID.getUUID() + "." + FilenameUtils.getExtension(path);
  }
}
