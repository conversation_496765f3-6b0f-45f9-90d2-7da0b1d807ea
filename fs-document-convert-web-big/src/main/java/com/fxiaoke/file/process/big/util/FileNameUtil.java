package com.fxiaoke.file.process.big.util;

import java.nio.file.Path;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import org.apache.commons.io.FilenameUtils;

public class FileNameUtil {
  private FileNameUtil() {
  }
  private static final String SUFFIX = "x";
  private static final String TXT_SUFFIX = ".txt";
  private static final String DOC_SUFFIX = "doc";
  public static final String DEFAULT_CACHE_PATH = System.getProperty("java.io.tmpdir");
  // 获取文件扩展名
  public static String getExtension(String filePath) {
    return FilenameUtils.getExtension(filePath);
  }
  // 判断文件是否为doc,如果是则替换文件扩展名后缀为docx
  public static String upgrade(String filePath) {
  if(getExtension(filePath).equals(DOC_SUFFIX)){
    return filePath.concat(SUFFIX);
  }
    return filePath;
  }
  // 替换文件扩展名后缀为txt
  public static String toTxt(String filePath) {
    return FilenameUtils.removeExtension(filePath).concat(TXT_SUFFIX);
  }

  public static String generateTempPngPathByIndex(String fullPath, int index) {
    return FilenameUtils.concat(fullPath, index + ".png");
  }

  public static String replaceExtension(String filePath, String extension) {
    return FilenameUtils.removeExtension(filePath).concat(extension);
  }

  private static String formatDate(String dateFormat) {
    return new SimpleDateFormat(dateFormat).format(new Date());
  }

  public static String generateSmbLocalPath(String smbPath,String ea,String path,String extension){
    String yyyyMMdd = formatDate("yyyyMMdd");
    String hh = formatDate("HH");
    return String.format("%s/%s/%s/%s/%s/%s.%s",smbPath,yyyyMMdd,hh, ea,path,path,extension);
  }



  /**
   * 获取文件扩展名
   * @param filename 文件名或路径
   * @return 文件扩展名
   */
  public static String getFileExtension(String filename) {
    return FilenameUtils.getExtension(filename);
  }

  /**
   * 获取父文件路径
   * @param filePath 文件路径
   * @return 文件名
   */
  public static String getFullPath(String filePath) {
    return FilenameUtils.getFullPath(filePath);
  }

  /**
   根据当前系统时间与ea生成临时文件夹路径
   * @param ea 用户ea
   * @return 临时文件夹
   */

  public static String generateDirAsEa(String ea,String rootDir) {
    String yyyyMM = formatDate("yyyyMM");
    String dd = formatDate("dd");
    String hh = formatDate("HH");
    return String.format("%s/%s/%s/%s/%s/%s/%s", rootDir, "dps", yyyyMM, dd, hh, ea, SampleUUID.getUUID());
  }


  public static String generateSimpFilePath(String prefix,String name, String suffix){
    StringBuilder fileName = new StringBuilder();
    if (prefix != null && !prefix.isEmpty()) {
      fileName.append(prefix).append("/");
    }
    fileName.append(name);
    if (suffix != null && !suffix.isEmpty()) {
      // 添加后缀
      fileName.append(".").append(suffix);
    }
    return fileName.toString();
  }

  /**
   * 生成文件名(带日期时间)
   * @param prefix 文件名前缀
   * @param name 主文件名
   * @param suffix 文件后缀（不包含点号）
   * @return 生成的完整文件名
   */
  public static String generateFilePath(String prefix,String name, String suffix) {
    // 获取当前时间
    LocalDateTime now = LocalDateTime.now();

    // 定义日期时间格式 (ddHH)
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/HH");

    // 格式化当前时间
    String dateTimeStr = now.format(formatter);

    // 构建文件名
    StringBuilder fileName = new StringBuilder();

    // 添加前缀（如果为空则使用默认缓存路径）
    if (prefix != null && !prefix.isEmpty()) {
      fileName.append(prefix).append("/");
    }else {
      fileName.append(DEFAULT_CACHE_PATH);
    }

    // 添加日期时间
    fileName.append(dateTimeStr).append("/");

    // 添加主文件名
    fileName.append(name);

    // 添加随机值防止高并发时文件名重复
    fileName.append("_").append(SampleUUID.getUUID());

    if (suffix != null && !suffix.isEmpty()) {
      // 添加后缀
      fileName.append(".").append(suffix);
    }

    return fileName.toString();
  }

  public static String getFileName(String filePath) {
    return FilenameUtils.getName(filePath);
  }

  // 生成清理后的文件路径
  public static Path generateCleanedMDFilePath(Path originalFile) {
    return originalFile.resolveSibling(originalFile.getFileName().toString().replace(".md", "_cleaned.md")
    );
  }

}
