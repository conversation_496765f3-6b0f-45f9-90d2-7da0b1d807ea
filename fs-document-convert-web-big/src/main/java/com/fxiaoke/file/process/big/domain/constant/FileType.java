package com.fxiaoke.file.process.big.domain.constant;

public enum FileType {
  DOC("doc"), DOCX("docx"), PPT("ppt"), PPTX("pptx"), XLS("xls"), XLSX("xlsx"), PDF("pdf"),
  JPG("jpg"), JPEG("jpeg"), PNG("png"), BMP("bmp"),SVG("svg"),
  TXT("txt"),TEXT("text"), HTML("html"), HTM("htm"),XML("xml"),
  JSON("json"),ASPX("aspx"),CSV("csv"),SQL("sql"),MSG("msg"),
  JS("js"),CSS("css"),GROOVY("groovy"),JAVA("java"),
  MP4("mp4"),MP3("mp3"),<PERSON>OV("mov");
  private final String fileTypeName;
  FileType(String fileTypeName) {
    this.fileTypeName = fileTypeName;
  }

  public static boolean isOffice(String fileTypeName){
    return fileTypeName.equalsIgnoreCase("doc")
        || fileTypeName.equalsIgnoreCase("docx")
        || fileTypeName.equalsIgnoreCase("ppt")
        || fileTypeName.equalsIgnoreCase("pptx")
        || fileTypeName.equalsIgnoreCase("xls")
        || fileTypeName.equalsIgnoreCase("xlsx")
        || fileTypeName.equalsIgnoreCase("pdf");
  }

  public static  boolean isCells(String fileTypeName){
    return fileTypeName.equalsIgnoreCase("xls")
        || fileTypeName.equalsIgnoreCase("xlsx");
  }

  public static boolean isXls(String fileTypeName){
    return fileTypeName.equalsIgnoreCase("xls");
  }

  public static boolean isWords(String fileTypeName){
    return fileTypeName.equalsIgnoreCase("doc")
        || fileTypeName.equalsIgnoreCase("docx");
  }

  public static boolean isDoc(String fileTypeName){
    return fileTypeName.equalsIgnoreCase("doc");
  }

  public static boolean isSlides(String fileTypeName){
    return fileTypeName.equalsIgnoreCase("ppt")
        || fileTypeName.equalsIgnoreCase("pptx");
  }

  public static boolean isPpt(String fileTypeName){
    return fileTypeName.equalsIgnoreCase("ppt");
  }

  public static boolean isPDF(String fileTypeName){
    return fileTypeName.equalsIgnoreCase("pdf");
  }

  public static boolean isImage(String fileTypeName){
    return fileTypeName.equalsIgnoreCase("jpg")
        || fileTypeName.equalsIgnoreCase("jpeg")
        || fileTypeName.equalsIgnoreCase("png")
        || fileTypeName.equalsIgnoreCase("bmp")
        || fileTypeName.equalsIgnoreCase("svg");
  }

  public static boolean isMultimedia(String fileTypeName){
    return fileTypeName.equalsIgnoreCase("mp4")
        || fileTypeName.equalsIgnoreCase("mp3")
        || fileTypeName.equalsIgnoreCase("mov");
  }

  public static boolean isText(String fileTypeName){
    return fileTypeName.equalsIgnoreCase("txt")
        || fileTypeName.equalsIgnoreCase("text")
        || fileTypeName.equalsIgnoreCase("html")
        || fileTypeName.equalsIgnoreCase("htm")
        || fileTypeName.equalsIgnoreCase("xml")
        || fileTypeName.equalsIgnoreCase("json")
        || fileTypeName.equalsIgnoreCase("aspx")
        || fileTypeName.equalsIgnoreCase("csv")
        || fileTypeName.equalsIgnoreCase("sql")
        || fileTypeName.equalsIgnoreCase("msg")
        || fileTypeName.equalsIgnoreCase("js")
        || fileTypeName.equalsIgnoreCase("css")
        || fileTypeName.equalsIgnoreCase("groovy")
        || fileTypeName.equalsIgnoreCase("java");
  }
}
