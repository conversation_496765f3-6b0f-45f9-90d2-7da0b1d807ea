package com.fxiaoke.file.process.big.web;

import com.fxiaoke.file.process.big.annotion.PathFormat;
import com.fxiaoke.file.process.big.domain.Result;
import com.fxiaoke.file.process.big.domain.api.Pdf2Image;
import com.fxiaoke.file.process.big.domain.model.word.Content;
import com.fxiaoke.file.process.big.service.ConvertTaskService;
import com.fxiaoke.file.process.big.service.OperationService;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j(topic = "Api")
@RestController
@RequestMapping(value = {"/api"})
@Validated
public class ApiController {

  @Resource
  OperationService operationService;

  @Resource
  ConvertTaskService convertTaskService;

  @GetMapping(value = {"/parse"}, params = {"ea", "path"})
  public Result<Content> parse(String id,@NotBlank(message = "parameter cannot be empty") String ea,@PathFormat String path,boolean generateTxt){
    Content content =operationService.parse(id,ea,generateTxt,path);
    return Result.ok(content);
  }

  @PostMapping(value = "/convert")
  public Result<Pdf2Image.Result> convert(@Validated @RequestBody Pdf2Image.Arg arg){
    return Result.ok(convertTaskService.submitTask(arg.getEa(),arg.getPath(),arg.getSecurityGroup()));
  }

}
