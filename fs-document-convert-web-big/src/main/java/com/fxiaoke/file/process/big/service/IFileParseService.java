package com.fxiaoke.file.process.big.service;

import com.fxiaoke.file.process.big.domain.model.word.Content;

public interface IFileParseService {
  /**
   * 解析文件 返回文件内容
   *
   * @param  filePath      文件路径
   * @param  async         是否异步
   * @return content       文件内容
   */
  Content parse(String filePath, boolean async);

  void asyncParse(String id,String ea,String filePath,boolean standard);

  String toTxt(String filePath);
}
