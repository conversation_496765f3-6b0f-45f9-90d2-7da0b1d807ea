package com.fxiaoke.file.process.big.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "file.convert.big")
public class CmsPropertiesConfig {

    private String defaultFonts ="SimSun";

    private int cellsMaxRow =3000;

    private int cellsMaxColumn =100;

    private String resourceRequestPrefix;

    private String cssLink;

    private String mqConfigName;
    private String mqTopic;

    // 文档内容解析
    private String mqParseSection;
    private String mqParseTaskTag;
    private String mqParseContentTag;

    // 文档格式转换
    private String mqConvertSection;
    public String mqConvertTaskTag;
    public String mqConvertContentTag;

    // 文件预览mongo配置文件名
    private String fsDpsMongoConfigName;
    // 请求imaginary服务HttpClient配置
    private String imaginaryHttpClientConfigName;
    // imaginary 服务地址
    private String imaginaryServerUrl;
    // fsi-proxy jar包依赖配置文件
    private String fsiProxyConfigName;

    private int pdf2ImageDpi=144;

    private String smbDiskPath="/smbshare";

    private int pdf2ImageMaxPage=1000;


    private String executorIp;
    private Integer executorPort;
    private String adminAddresses;
    private String appName;
    private String executorLogpath;
    private String accessToken;

}
