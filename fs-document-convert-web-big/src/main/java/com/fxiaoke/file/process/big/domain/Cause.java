package com.fxiaoke.file.process.big.domain;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 通用入参请求类 包裹真正地请求对象,同时记录基础的业务信息记录 谁调用 干什么 什么时候(构造时默认获取当前系统时间) callServiceName 调用方服务名 businessType
 * 业务类型 requestTime     请求时间 毫秒
 */
@Data
@NoArgsConstructor
@ToString
public class Cause<T> {

  @NotBlank(message = "callServicesName cannot be empty")
  private String callServiceName;

  @NotBlank(message = "businessType cannot be empty")
  private String businessType;

  @NotNull(message = "requestTime cannot be empty")
  private Long requestTime;

  @Valid
  @NotNull(message = "business objectData cannot be empty")
  private T data;

  private Cause(String callServiceName, String businessType, T data) {
    this.callServiceName = callServiceName;
    this.businessType = businessType;
    this.requestTime = System.currentTimeMillis();
    this.data = data;
  }

  public static <T> Cause<T> send(String callServiceName, String businessType, T data) {
    return new Cause<>(callServiceName, businessType, data);
  }
}
