package com.fxiaoke.file.process.big.service;

import com.fxiaoke.file.process.big.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.big.dao.IConvertTaskCacheDao;
import com.fxiaoke.file.process.big.domain.api.Pdf2Image;
import com.fxiaoke.file.process.big.domain.api.Pdf2Image.Page;
import com.fxiaoke.file.process.big.domain.constant.ErrorInfo;
import com.fxiaoke.file.process.big.domain.constant.Status;
import com.fxiaoke.file.process.big.domain.entity.ConvertTaskCache;
import com.fxiaoke.file.process.big.domain.entity.model.pdf.ImagePathCache;
import com.fxiaoke.file.process.big.domain.exception.ProcessException;
import com.fxiaoke.file.process.big.mq.ConvertProducer;
import com.fxiaoke.file.process.big.service.aspose.PDFService;
import com.fxiaoke.file.process.big.util.FileNameUtil;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class ConvertTaskService {

  private static final Logger log = LoggerFactory.getLogger(ConvertTaskService.class);
  ConvertProducer convertProducer;
  PDFService pdfService;
  StoneService stoneService;
  CmsPropertiesConfig config;
  IConvertTaskCacheDao convertTaskCacheDao;

  public ConvertTaskService(ConvertProducer convertProducer, PDFService pdfService, StoneService stoneService,
      CmsPropertiesConfig config, IConvertTaskCacheDao convertTaskCacheDao) {
    this.convertProducer = convertProducer;
    this.pdfService = pdfService;
    this.stoneService = stoneService;
    this.config = config;
    this.convertTaskCacheDao = convertTaskCacheDao;
  }

  /**
   * 提交转换任务
   *
   * @param ea            企业账号
   * @param path          文件路径
   * @param securityGroup 安全组
   * @return 任务ID
   */
  public Pdf2Image.Result submitTask(String ea, String path, String securityGroup) {
    log.info("PDF async to image request arg: ea={},path={},securityGroup={}",ea,path,securityGroup);
    // 获取文件扩展名
    String extension = FileNameUtil.getExtension(path);

    path= FilenameUtils.getBaseName(path);

    // 查询任务是否存在
    ConvertTaskCache taskCache = convertTaskCacheDao.findTask(ea, path, securityGroup);
    log.info("task idempotent query,taskCache:{}",taskCache);
    // 初次提交任务
    if (taskCache == null) {

      log.info("task not exist,ea:{},path:{},securityGroup:{}",ea,path,securityGroup);
      // 获取文件大小
      long size = stoneService.getSizeByPath(ea, path, securityGroup);
      log.info("get file size,ea:{},path:{},securityGroup:{},size:{}",ea,path,securityGroup,size);
      // 文件不存在
      if (size == -1) {
        throw new ProcessException(ErrorInfo.FILE_NOT_FOUND,"ConvertTaskService", ea, path, securityGroup);
      }
      // 文件大小超过100M
      if (size > 1024 * 1024 * 100) {
        throw new ProcessException(ErrorInfo.FILE_SIZE_EXCEEDED,"ConvertTaskService", ea, path, securityGroup);
      }

      // 生成本地路径并缓存到本地
      String localPath = stoneService.downloadToLocal(ea, path, securityGroup, FileNameUtil.generateSmbLocalPath(config.getSmbDiskPath(), ea, path,extension));
      if (localPath == null) {
        throw new ProcessException(ErrorInfo.FILE_DOWNLOAD_FAIL,"ConvertTaskService", ea, path, securityGroup);
      }
      log.info("download file to local,ea:{},path:{},localPath:{}",ea,path,localPath);

      // 获取文件页数
      int pageCount = pdfService.getPageCount(localPath);
      // 文件页数超过限制
      if (pageCount > config.getPdf2ImageMaxPage()) {
        throw new ProcessException(ErrorInfo.FILE_PAGE_LIMIT,"ConvertTaskService",ea, path, securityGroup, localPath);
      }
      log.info("get file page count,ea:{},path:{},pageCount:{}",ea,path,pageCount);

      // 初始化任务
      String taskId = convertTaskCacheDao.initTask(ea, path, securityGroup,size, extension, pageCount, localPath);
      log.info("init task success,ea:{},path:{},taskId:{}",ea,path,taskId);
      // 发送转换任务
      convertProducer.send(taskId);
      log.info("submit task success,ea:{},path:{},taskId:{}",ea,path,taskId);
      return Pdf2Image.Result.Queue(ea,taskId);
    } else {
      String taskId = taskCache.getId().toString();
      log.info("task exist,ea:{},path:{},taskId:{}",ea,path,taskId);
      // 多次重试失败
      int retryCount = taskCache.getRetryCount();
      if (retryCount >= 2 || taskCache.getStatus() == -1) {
        log.info("task retry count exceed limit,ea:{},path:{},taskId:{}",ea,path,taskId);
        return Pdf2Image.Result.Fail(ea,taskId, taskCache.getFailReason());
      }

      int status = taskCache.getStatus();
      return switch (status) {

        // 任务在队列中
        case 0 -> {
          log.info("task in queue,ea:{},path:{},taskId:{}",ea,path,taskId);
          //重发任务
          convertProducer.send(taskId);
          yield Pdf2Image.Result.Queue(ea,taskId);
        }

        // 任务正在转换中
        case 1 -> {
          log.info("task in process,ea:{},path:{},taskId:{}",ea,path,taskId);
          // 超过3分钟未更新 且任务状态为 1 则认为转换失败 需重试
          int heartbeatTimeOut = 1000 * 60 * 5;
          if (new Date().getTime() - taskCache.getHeartbeatTime().getTime() > heartbeatTimeOut) {
            log.info("task in process heartbeat timeOut,ea:{},path:{},taskId:{},heartbeatTimeOut:{},retry",ea,path,taskId,heartbeatTimeOut);
            // 重试并重置任务状态
            convertTaskCacheDao.retryTask(taskId,true);
            convertProducer.send(taskId);
            log.info("retry task success,ea:{},path:{},taskId:{}",ea,path,taskId);
            yield Pdf2Image.Result.Retry(ea,taskId);
          }
          log.info("task in process repeated request,ea:{},path:{},taskId:{}",ea,path,taskId);
          yield Pdf2Image.Result.Process(ea,taskId);
        }

        // 任务转换成功
        case 2 -> {
          List<Page> paths = taskCache.getImagePaths().stream()
              .map(page -> new Page(page.getPageNumber(), page.getExtension(), page.getPath(),page.getSize(),page.getWidth(),page.getHeight()))
              .collect(Collectors.toList());
          log.info("task success return the result directly,ea:{},path:{},taskId:{},pathsSize:{}",ea,path,taskId,paths.size());
          yield Pdf2Image.Result.Success(ea,taskId, paths);
        }

        // 任务转换失败
        default -> Pdf2Image.Result.Fail(ea,taskId, taskCache.getFailReason());
      };
    }
  }

  public boolean processTask(String taskId) {

    log.info("process task start,taskId:{}",taskId);
    ConvertTaskCache taskCache = convertTaskCacheDao.findTask(taskId);
    log.info("process task process one ,find cache,taskId:{},taskCache:{}",taskId,taskCache);
    int status = taskCache.getStatus();
    if (status == 0) {
      log.info("process task process two,receiving task,taskId:{}",taskId);
      convertTaskCacheDao.updateStatus(taskId, Status.PROCESS, "");
      log.info("process task process two,update status to process,taskId:{}",taskId);
      // 将PDF转换为图片
      process(taskCache);
    } else if (status == 1) {
      log.info("process task process two,receiving repeated request,taskId:{}",taskId);
      // 这种的属于MQ重试,不是用户重试
      int retryCount = taskCache.getRetryCount();
      if (retryCount >= 2) {
        log.warn("process task process two,retry count exceed limit,taskId:{}",taskId);
        convertTaskCacheDao.updateStatus(taskId, Status.FAIL, "重试次数超过限制");
      } else {
        log.warn("process task process two,retry task,taskId:{}",taskId);
        convertTaskCacheDao.retryTask(taskId, false);
        process(taskCache);
      }
    } else if (status == 2) {
      log.info("process task process two,receiving success task,taskId:{}",taskId);
      List<ImagePathCache> imagePathCache = convertTaskCacheDao.findImagePathCache(taskId);
      convertProducer.send(taskCache.getEa(),taskId, imagePathCache);
    }
    return true;
  }

  private void process(ConvertTaskCache taskCache){
    String taskId=taskCache.getId().toString();
    log.info("process pdf to image start,taskId:{},taskCache:{}",taskId,taskCache);
    // 将PDF转换为图片
    List<ImagePathCache> imagePaths = pdfService.toPng(taskCache.getLocalCachePath(), taskCache.getPageCount(), taskId, taskCache.getImagePaths(), taskCache.getEa());
    log.info("process pdf to image end,taskId:{},imagePaths:{}",taskId,imagePaths);

    convertTaskCacheDao.updateStatus(taskId,Status.SUCCESS,"");
    log.info("update process task status to success,taskId:{}",taskId);
    convertProducer.send(taskCache.getEa(),taskId, imagePaths);
  }


}
