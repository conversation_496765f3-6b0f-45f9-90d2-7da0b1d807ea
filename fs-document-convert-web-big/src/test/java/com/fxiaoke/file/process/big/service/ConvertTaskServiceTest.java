package com.fxiaoke.file.process.big.service;

import com.fxiaoke.file.process.big.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.big.dao.IConvertTaskCacheDao;
import com.fxiaoke.file.process.big.domain.api.Pdf2Image;
import com.fxiaoke.file.process.big.domain.constant.ErrorInfo;
import com.fxiaoke.file.process.big.domain.constant.Status;
import com.fxiaoke.file.process.big.domain.entity.ConvertTaskCache;
import com.fxiaoke.file.process.big.domain.entity.model.pdf.ImagePathCache;
import com.fxiaoke.file.process.big.domain.exception.ProcessException;
import com.fxiaoke.file.process.big.mq.ConvertProducer;
import com.fxiaoke.file.process.big.service.aspose.PDFService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ConvertTaskServiceTest {

    @Mock
    private ConvertProducer convertProducer;

    @Mock
    private PDFService pdfService;

    @Mock
    private StoneService stoneService;

    @Mock
    private CmsPropertiesConfig config;

    @Mock
    private IConvertTaskCacheDao convertTaskCacheDao;

    private ConvertTaskService convertTaskService;

    private static final String TEST_EA = "testEa";
    private static final String TEST_PATH = "test/document.pdf";
    private static final String TEST_SECURITY_GROUP = "testGroup";
    private static final String TEST_TASK_ID = "task123";
    private static final long TEST_FILE_SIZE = 1024 * 1024; // 1MB
    private static final int TEST_PAGE_COUNT = 10;
    private static final String TEST_LOCAL_PATH = "/tmp/test.pdf";

    @BeforeEach
    void setUp() {
        convertTaskService = new ConvertTaskService(convertProducer, pdfService, stoneService, config, convertTaskCacheDao);
        when(config.getSmbDiskPath()).thenReturn("/tmp");
        when(config.getPdf2ImageMaxPage()).thenReturn(100);
    }

    @Test
    void testSubmitTask_NewTask_Success() {
        // Given
        when(convertTaskCacheDao.findTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP)).thenReturn(null);
        when(stoneService.getSizeByPath(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP)).thenReturn(TEST_FILE_SIZE);
        when(stoneService.downloadToLocal(eq(TEST_EA), eq(TEST_PATH), eq(TEST_SECURITY_GROUP), anyString()))
            .thenReturn(TEST_LOCAL_PATH);
        when(pdfService.getPageCount(TEST_LOCAL_PATH)).thenReturn(TEST_PAGE_COUNT);
        when(convertTaskCacheDao.initTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP, TEST_FILE_SIZE, "pdf", TEST_PAGE_COUNT, TEST_LOCAL_PATH))
            .thenReturn(TEST_TASK_ID);

        // When
        Pdf2Image.Result result = convertTaskService.submitTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP);

        // Then
        assertNotNull(result);
        assertEquals(TEST_EA, result.ea);
        assertEquals(TEST_TASK_ID, result.taskId);
        assertEquals(Status.QUEUE, result.status);

        verify(convertTaskCacheDao).findTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP);
        verify(stoneService).getSizeByPath(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP);
        verify(stoneService).downloadToLocal(eq(TEST_EA), eq(TEST_PATH), eq(TEST_SECURITY_GROUP), anyString());
        verify(pdfService).getPageCount(TEST_LOCAL_PATH);
        verify(convertTaskCacheDao).initTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP, TEST_FILE_SIZE, "pdf", TEST_PAGE_COUNT, TEST_LOCAL_PATH);
        verify(convertProducer).send(TEST_TASK_ID);
    }

    @Test
    void testSubmitTask_FileNotFound_ThrowsException() {
        // Given
        when(convertTaskCacheDao.findTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP)).thenReturn(null);
        when(stoneService.getSizeByPath(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP)).thenReturn(-1L);

        // When & Then
        ProcessException exception = assertThrows(ProcessException.class,
            () -> convertTaskService.submitTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP));

        assertEquals(ErrorInfo.FILE_NOT_FOUND, exception.getErrorInfo());
        verify(convertTaskCacheDao).findTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP);
        verify(stoneService).getSizeByPath(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP);
    }

    @Test
    void testSubmitTask_FileSizeExceeded_ThrowsException() {
        // Given
        long oversizeFile = 1024 * 1024 * 101; // 101MB
        when(convertTaskCacheDao.findTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP)).thenReturn(null);
        when(stoneService.getSizeByPath(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP)).thenReturn(oversizeFile);

        // When & Then
        ProcessException exception = assertThrows(ProcessException.class,
            () -> convertTaskService.submitTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP));

        assertEquals(ErrorInfo.FILE_SIZE_EXCEEDED, exception.getErrorInfo());
        verify(convertTaskCacheDao).findTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP);
        verify(stoneService).getSizeByPath(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP);
    }

    @Test
    void testSubmitTask_DownloadFailed_ThrowsException() {
        // Given
        when(convertTaskCacheDao.findTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP)).thenReturn(null);
        when(stoneService.getSizeByPath(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP)).thenReturn(TEST_FILE_SIZE);
        when(stoneService.downloadToLocal(eq(TEST_EA), eq(TEST_PATH), eq(TEST_SECURITY_GROUP), anyString()))
            .thenReturn(null);

        // When & Then
        ProcessException exception = assertThrows(ProcessException.class,
            () -> convertTaskService.submitTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP));

        assertEquals(ErrorInfo.FILE_DOWNLOAD_FAIL, exception.getErrorInfo());
        verify(stoneService).downloadToLocal(eq(TEST_EA), eq(TEST_PATH), eq(TEST_SECURITY_GROUP), anyString());
    }

    @Test
    void testSubmitTask_PageCountExceeded_ThrowsException() {
        // Given
        when(convertTaskCacheDao.findTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP)).thenReturn(null);
        when(stoneService.getSizeByPath(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP)).thenReturn(TEST_FILE_SIZE);
        when(stoneService.downloadToLocal(eq(TEST_EA), eq(TEST_PATH), eq(TEST_SECURITY_GROUP), anyString()))
            .thenReturn(TEST_LOCAL_PATH);
        when(pdfService.getPageCount(TEST_LOCAL_PATH)).thenReturn(150); // Exceeds limit
        when(config.getPdf2ImageMaxPage()).thenReturn(100);

        // When & Then
        ProcessException exception = assertThrows(ProcessException.class,
            () -> convertTaskService.submitTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP));

        assertEquals(ErrorInfo.FILE_PAGE_LIMIT, exception.getErrorInfo());
        verify(pdfService).getPageCount(TEST_LOCAL_PATH);
    }

    @Test
    void testSubmitTask_ExistingTaskInQueue_ResendsTask() {
        // Given
        ConvertTaskCache existingTask = createMockTask(0, 0); // Status: Queue, RetryCount: 0
        when(convertTaskCacheDao.findTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP)).thenReturn(existingTask);

        // When
        Pdf2Image.Result result = convertTaskService.submitTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP);

        // Then
        assertNotNull(result);
        assertEquals(Status.QUEUE, result.status);
        assertEquals(TEST_TASK_ID, result.taskId);
        verify(convertProducer).send(TEST_TASK_ID);
    }

    @Test
    void testSubmitTask_ExistingTaskInProcess_ReturnsProcessStatus() {
        // Given
        ConvertTaskCache existingTask = createMockTask(1, 0); // Status: Process, RetryCount: 0
        existingTask.setHeartbeatTime(new Date()); // Recent heartbeat
        when(convertTaskCacheDao.findTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP)).thenReturn(existingTask);

        // When
        Pdf2Image.Result result = convertTaskService.submitTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP);

        // Then
        assertNotNull(result);
        assertEquals(Status.PROCESS, result.status);
        assertEquals(TEST_TASK_ID, result.taskId);
        verify(convertProducer, never()).send(anyString());
    }

    @Test
    void testSubmitTask_ExistingTaskInProcessTimeout_RetriesTask() {
        // Given
        ConvertTaskCache existingTask = createMockTask(1, 0); // Status: Process, RetryCount: 0
        Date oldHeartbeat = new Date(System.currentTimeMillis() - 6 * 60 * 1000); // 6 minutes ago
        existingTask.setHeartbeatTime(oldHeartbeat);
        when(convertTaskCacheDao.findTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP)).thenReturn(existingTask);

        // When
        Pdf2Image.Result result = convertTaskService.submitTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP);

        // Then
        assertNotNull(result);
        assertEquals(Status.RETRY, result.status);
        assertEquals(TEST_TASK_ID, result.taskId);
        verify(convertTaskCacheDao).retryTask(TEST_TASK_ID, true);
        verify(convertProducer).send(TEST_TASK_ID);
    }

    @Test
    void testSubmitTask_ExistingTaskSuccess_ReturnsResult() {
        // Given
        ConvertTaskCache existingTask = createMockTask(2, 0); // Status: Success, RetryCount: 0
        List<ImagePathCache> imagePaths = Arrays.asList(
            createImagePathCache(1, "png", "path1", 1024L, 800, 600),
            createImagePathCache(2, "png", "path2", 1024L, 800, 600)
        );
        existingTask.setImagePaths(imagePaths);
        when(convertTaskCacheDao.findTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP)).thenReturn(existingTask);

        // When
        Pdf2Image.Result result = convertTaskService.submitTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP);

        // Then
        assertNotNull(result);
        assertEquals(Status.SUCCESS, result.status);
        assertEquals(TEST_TASK_ID, result.taskId);
        assertEquals(2, result.paths.size());
        assertEquals(1, result.paths.get(0).getPageNumber());
        assertEquals("path1", result.paths.get(0).getPath());
    }

    @Test
    void testSubmitTask_ExistingTaskFailed_ReturnsFail() {
        // Given
        ConvertTaskCache existingTask = createMockTask(-1, 0); // Status: Failed
        existingTask.setFailReason("Processing failed");
        when(convertTaskCacheDao.findTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP)).thenReturn(existingTask);

        // When
        Pdf2Image.Result result = convertTaskService.submitTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP);

        // Then
        assertNotNull(result);
        assertEquals(Status.FAIL, result.status);
        assertEquals(TEST_TASK_ID, result.taskId);
        assertEquals("Processing failed", result.message);
    }

    @Test
    void testSubmitTask_ExistingTaskRetryLimitExceeded_ReturnsFail() {
        // Given
        ConvertTaskCache existingTask = createMockTask(0, 2); // Status: Queue, RetryCount: 2 (limit exceeded)
        existingTask.setFailReason("Retry limit exceeded");
        when(convertTaskCacheDao.findTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP)).thenReturn(existingTask);

        // When
        Pdf2Image.Result result = convertTaskService.submitTask(TEST_EA, TEST_PATH, TEST_SECURITY_GROUP);

        // Then
        assertNotNull(result);
        assertEquals(Status.FAIL, result.status);
        assertEquals(TEST_TASK_ID, result.taskId);
        assertEquals("Retry limit exceeded", result.message);
    }

    @Test
    void testProcessTask_NewTask_ProcessesSuccessfully() {
        // Given
        ConvertTaskCache taskCache = createMockTask(0, 0); // Status: Queue
        taskCache.setLocalCachePath(TEST_LOCAL_PATH);
        taskCache.setPageCount(TEST_PAGE_COUNT);
        taskCache.setEa(TEST_EA);

        List<ImagePathCache> imagePaths = Arrays.asList(createImagePathCache(1, "png", "path1", 1024L, 800, 600));

        when(convertTaskCacheDao.findTask(TEST_TASK_ID)).thenReturn(taskCache);
        when(pdfService.toPng(TEST_LOCAL_PATH, TEST_PAGE_COUNT, TEST_TASK_ID, taskCache.getImagePaths(), TEST_EA))
            .thenReturn(imagePaths);

        // When
        boolean result = convertTaskService.processTask(TEST_TASK_ID);

        // Then
        assertTrue(result);
        verify(convertTaskCacheDao).updateStatus(TEST_TASK_ID, Status.PROCESS, "");
        verify(pdfService).toPng(TEST_LOCAL_PATH, TEST_PAGE_COUNT, TEST_TASK_ID, taskCache.getImagePaths(), TEST_EA);
        verify(convertTaskCacheDao).updateStatus(TEST_TASK_ID, Status.SUCCESS, "");
        verify(convertProducer).send(TEST_EA, TEST_TASK_ID, imagePaths);
    }

    @Test
    void testProcessTask_RepeatedRequest_WithinRetryLimit_Retries() {
        // Given
        ConvertTaskCache taskCache = createMockTask(1, 1); // Status: Process, RetryCount: 1
        taskCache.setLocalCachePath(TEST_LOCAL_PATH);
        taskCache.setPageCount(TEST_PAGE_COUNT);
        taskCache.setEa(TEST_EA);

        List<ImagePathCache> imagePaths = Arrays.asList(createImagePathCache(1, "png", "path1", 1024L, 800, 600));

        when(convertTaskCacheDao.findTask(TEST_TASK_ID)).thenReturn(taskCache);
        when(pdfService.toPng(TEST_LOCAL_PATH, TEST_PAGE_COUNT, TEST_TASK_ID, taskCache.getImagePaths(), TEST_EA))
            .thenReturn(imagePaths);

        // When
        boolean result = convertTaskService.processTask(TEST_TASK_ID);

        // Then
        assertTrue(result);
        verify(convertTaskCacheDao).retryTask(TEST_TASK_ID, false);
        verify(pdfService).toPng(TEST_LOCAL_PATH, TEST_PAGE_COUNT, TEST_TASK_ID, taskCache.getImagePaths(), TEST_EA);
        verify(convertTaskCacheDao).updateStatus(TEST_TASK_ID, Status.SUCCESS, "");
        verify(convertProducer).send(TEST_EA, TEST_TASK_ID, imagePaths);
    }

    @Test
    void testProcessTask_RepeatedRequest_RetryLimitExceeded_UpdatesToFail() {
        // Given
        ConvertTaskCache taskCache = createMockTask(1, 2); // Status: Process, RetryCount: 2 (limit exceeded)
        when(convertTaskCacheDao.findTask(TEST_TASK_ID)).thenReturn(taskCache);

        // When
        boolean result = convertTaskService.processTask(TEST_TASK_ID);

        // Then
        assertTrue(result);
        verify(convertTaskCacheDao).updateStatus(TEST_TASK_ID, Status.FAIL, "重试次数超过限制");
        verify(pdfService, never()).toPng(anyString(), anyInt(), anyString(), anyList(), anyString());
    }

    @Test
    void testProcessTask_SuccessTask_SendsResult() {
        // Given
        ConvertTaskCache taskCache = createMockTask(2, 0); // Status: Success
        taskCache.setEa(TEST_EA);
        List<ImagePathCache> imagePaths = Arrays.asList(createImagePathCache(1, "png", "path1", 1024L, 800, 600));

        when(convertTaskCacheDao.findTask(TEST_TASK_ID)).thenReturn(taskCache);
        when(convertTaskCacheDao.findImagePathCache(TEST_TASK_ID)).thenReturn(imagePaths);

        // When
        boolean result = convertTaskService.processTask(TEST_TASK_ID);

        // Then
        assertTrue(result);
        verify(convertProducer).send(TEST_EA, TEST_TASK_ID, imagePaths);
    }

    @ParameterizedTest
    @ValueSource(strings = {"test.pdf", "test.PDF", "document.pdf", "file.PDF"})
    void testSubmitTask_DifferentFileExtensions_Success(String fileName) {
        // Given
        when(convertTaskCacheDao.findTask(eq(TEST_EA), anyString(), eq(TEST_SECURITY_GROUP))).thenReturn(null);
        when(stoneService.getSizeByPath(eq(TEST_EA), anyString(), eq(TEST_SECURITY_GROUP))).thenReturn(TEST_FILE_SIZE);
        when(stoneService.downloadToLocal(eq(TEST_EA), anyString(), eq(TEST_SECURITY_GROUP), anyString()))
            .thenReturn(TEST_LOCAL_PATH);
        when(pdfService.getPageCount(TEST_LOCAL_PATH)).thenReturn(TEST_PAGE_COUNT);
        when(convertTaskCacheDao.initTask(eq(TEST_EA), anyString(), eq(TEST_SECURITY_GROUP), eq(TEST_FILE_SIZE), eq("pdf"), eq(TEST_PAGE_COUNT), eq(TEST_LOCAL_PATH)))
            .thenReturn(TEST_TASK_ID);

        // When
        Pdf2Image.Result result = convertTaskService.submitTask(TEST_EA, fileName, TEST_SECURITY_GROUP);

        // Then
        assertNotNull(result);
        assertEquals(Status.QUEUE, result.status);
    }

    private ConvertTaskCache createMockTask(int status, int retryCount) {
        ConvertTaskCache task = new ConvertTaskCache();
        task.setId(Long.valueOf(TEST_TASK_ID.hashCode()));
        task.setStatus(status);
        task.setRetryCount(retryCount);
        task.setHeartbeatTime(new Date());
        return task;
    }

    private ImagePathCache createImagePathCache(int pageNumber, String extension, String path, Long size, int width, int height) {
        ImagePathCache imagePathCache = new ImagePathCache();
        imagePathCache.setPageNumber(pageNumber);
        imagePathCache.setExtension(extension);
        imagePathCache.setPath(path);
        imagePathCache.setSize(size);
        imagePathCache.setWidth(width);
        imagePathCache.setHeight(height);
        return imagePathCache;
    }
}
