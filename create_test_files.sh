#!/bin/bash

# Create base directories
mkdir -p /tmp/20250529

# Create files for any path that might be generated
for ea in testEa; do
  for employeeId in 12345; do
    for extension in pdf zip; do
      for dd in {01..31}; do
        for hh in {00..23}; do
          for mm in {00..59}; do
            dir="/tmp/20250529/$ea/$employeeId/$extension/$dd/$hh/$mm"
            mkdir -p "$dir"
            # Create some random subdirectories and files
            for i in {1..5}; do
              subdir="$dir/$(openssl rand -hex 4)"
              mkdir -p "$subdir"
              echo "test content" > "$subdir/$(openssl rand -hex 4).$extension"
            done
          done
        done
      done
    done
  done
done

echo "Test files created"
