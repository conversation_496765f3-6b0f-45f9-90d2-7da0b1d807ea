<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.fxiaoke</groupId>
    <artifactId>fs-document-converter</artifactId>
    <version>1.0-SNAPSHOT</version>
  </parent>
  <packaging>war</packaging>

  <artifactId>fs-document-convert-web</artifactId>

  <properties>
    <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
    <jave2.version>3.5.0</jave2.version>
  </properties>
  <dependencies>
    <!-- SpringBoot核心依赖 必须 start-->
    <dependency>
      <artifactId>spring-boot-starter-web</artifactId>
      <groupId>org.springframework.boot</groupId>
    </dependency>
    <dependency>
      <artifactId>spring-boot-starter-actuator</artifactId>
      <groupId>org.springframework.boot</groupId>
    </dependency>
    <dependency>
      <artifactId>spring-boot-starter-aop</artifactId>
      <groupId>org.springframework.boot</groupId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>

    <!-- Spring Boot AliOssTest Starter -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <!-- Mockito -->
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-launcher</artifactId>
      <version>1.8.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.vintage</groupId>
      <artifactId>junit-vintage-engine</artifactId>
      <version>5.8.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-params</artifactId>
      <version>5.10.0</version>
      <scope>test</scope>
    </dependency>
    <!--SpringBoot核心依赖 必须 end-->

    <!-- Maven -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>

    <!--日志自动上报-->
    <dependency>
      <groupId>com.fxiaoke.boot</groupId>
      <artifactId>metrics-spring-boot-starter</artifactId>
    </dependency>

    <!--组件-SpringBoot MongoDB-->
    <dependency>
      <artifactId>mongo-spring-boot-starter</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>error_prone_annotations</artifactId>
          <groupId>com.google.errorprone</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spotbugs-annotations</artifactId>
          <groupId>com.github.spotbugs</groupId>
        </exclusion>
      </exclusions>
      <groupId>com.fxiaoke.boot</groupId>
    </dependency>
    <!--组件-SpringBoot 配置中心-->
    <dependency>
      <groupId>com.fxiaoke.cloud</groupId>
      <artifactId>cms-spring-cloud-starter</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>spotbugs-annotations</artifactId>
          <groupId>com.github.spotbugs</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-stone-sdk</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <artifactId>fs-fsi-proxy</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>commons-lang</artifactId>
          <groupId>commons-lang</groupId>
        </exclusion>
        <exclusion>
          <artifactId>curator-client</artifactId>
          <groupId>org.apache.curator</groupId>
        </exclusion>
        <exclusion>
          <artifactId>curator-framework</artifactId>
          <groupId>org.apache.curator</groupId>
        </exclusion>
        <exclusion>
          <artifactId>error_prone_annotations</artifactId>
          <groupId>com.google.errorprone</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spotbugs-annotations</artifactId>
          <groupId>com.github.spotbugs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-collectionschema</artifactId>
          <groupId>io.protostuff</groupId>
        </exclusion>
      </exclusions>
      <groupId>com.facishare</groupId>
    </dependency>

    <!--组件-Spring RocketMQ-->
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-rocketmq-support</artifactId>
    </dependency>

    <!--Aspose 文档控件 start-->
    <dependency>
      <artifactId>aspose-slides</artifactId>
      <classifier>jdk16</classifier>
      <groupId>com.aspose</groupId>
    </dependency>
    <dependency>
      <artifactId>aspose-slides</artifactId>
      <classifier>javadoc</classifier>
      <groupId>com.aspose</groupId>
    </dependency>

    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-cells</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-cells</artifactId>
      <classifier>javadoc</classifier>
    </dependency>

    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-words</artifactId>
      <classifier>jdk17</classifier>
    </dependency>
    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-words</artifactId>
      <classifier>javadoc</classifier>
    </dependency>

    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-pdf</artifactId>
      <classifier>jdk17</classifier>
    </dependency>
    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-pdf</artifactId>
      <classifier>javadoc</classifier>
    </dependency>
    <!--Aspose 文档控件 end-->
    <!--POI系列控件 start-->
    <dependency>
      <artifactId>poi</artifactId>
      <groupId>org.apache.poi</groupId>
    </dependency>
    <dependency>
      <artifactId>poi-ooxml</artifactId>
      <groupId>org.apache.poi</groupId>
    </dependency>
    <dependency>
      <artifactId>poi-scratchpad</artifactId>
      <groupId>org.apache.poi</groupId>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml-schemas</artifactId>
    </dependency>
    <!--POI系列控件 end-->

    <!--PDFBox start-->
    <dependency>
      <groupId>org.apache.pdfbox</groupId>
      <artifactId>pdfbox</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.pdfbox</groupId>
      <artifactId>pdfbox-io</artifactId>
    </dependency>

    <!--Jai 高级图像转换 start-->
    <dependency>
      <groupId>com.github.jai-imageio</groupId>
      <artifactId>jai-imageio-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.jai-imageio</groupId>
      <artifactId>jai-imageio-jpeg2000</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.pdfbox</groupId>
      <artifactId>jbig2-imageio</artifactId>
    </dependency>

    <!--PDFBox end-->
    <!--common 通用工具类 start-->
    <dependency>
      <artifactId>commons-io</artifactId>
      <groupId>commons-io</groupId>
    </dependency>

    <!-- MapStruct -->
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
      <version>${org.mapstruct.version}</version>
    </dependency>
    <!--common 通用工具类 end-->

    <dependency>
      <groupId>com.github.ben-manes.caffeine</groupId>
      <artifactId>caffeine</artifactId>
    </dependency>

    <dependency>
      <groupId>org.jsoup</groupId>
      <artifactId>jsoup</artifactId>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>biz-log-client</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>protostuff-collectionschema</artifactId>
          <groupId>io.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>error_prone_annotations</artifactId>
          <groupId>com.google.errorprone</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-stone-commons-client</artifactId>
    </dependency>


    <dependency>
      <groupId>ws.schild</groupId>
      <artifactId>jave-core</artifactId>
      <version>${jave2.version}</version>
    </dependency>
    <dependency>
      <groupId>ws.schild</groupId>
      <artifactId>jave-nativebin-linux64</artifactId>
      <version>${jave2.version}</version>
      <scope>runtime</scope>
    </dependency>

    <dependency>
      <groupId>io.protostuff</groupId>
      <artifactId>protostuff-core</artifactId>
    </dependency>
    <dependency>
      <groupId>io.protostuff</groupId>
      <artifactId>protostuff-runtime</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>fs-job-core</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
    </dependency>

    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-core</artifactId>
      <version>5.8.11</version>
    </dependency>

    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-client</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-acl</artifactId>
    </dependency>

    <!-- ZXing 二维码生成 start -->
    <dependency>
      <groupId>com.google.zxing</groupId>
      <artifactId>core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.zxing</groupId>
      <artifactId>javase</artifactId>
    </dependency>
    <!-- ZXing 二维码生成 end -->
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>JaCoCo Report</id>
            <phase>test</phase>
            <goals>
              <goal>report-aggregate</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
          <annotationProcessorPaths>
            <path>
              <groupId>org.mapstruct</groupId>
              <artifactId>mapstruct-processor</artifactId>
              <version>${org.mapstruct.version}</version>
            </path>
            <!-- Lombok 配置 -->
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${lombok.version}</version>
            </path>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok-mapstruct-binding</artifactId>
              <version>0.2.0</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
