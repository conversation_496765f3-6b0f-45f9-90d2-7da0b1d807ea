package com.fxiaoke.document.convert.web;

import com.fxiaoke.document.convert.domain.Result;
import com.fxiaoke.document.convert.domain.open.DocPageQRCode;
import com.fxiaoke.document.convert.domain.open.GetMeatInfo;
import com.fxiaoke.document.convert.domain.open.MergerOffice;
import com.fxiaoke.document.convert.domain.open.OfficeToPdf;
import com.fxiaoke.document.convert.domain.open.ToPngZip;
import com.fxiaoke.document.convert.domain.open.Watermark;
import com.fxiaoke.document.convert.log.OpenGetWebLogger;
import com.fxiaoke.document.convert.log.OpenPostWebLogger;
import com.fxiaoke.document.convert.service.OpenApiService;
import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/Api/Open")
@Slf4j(topic = "OpenApiController")
public class OpenApiController {

  @Resource
  OpenApiService openApiService;

  @OpenGetWebLogger
  @GetMapping("/GetMetaInfo")
  public Result<GetMeatInfo.Result> getMetaInfo(
      @RequestParam("path") @Pattern(regexp = "^(N_|TN_|TA_|A_|G_).*\\.(doc|docx|ppt|pptx|xls|xlsx|pdf)$", message = "Invalid path") String path,
      @RequestParam("ea") @NotEmpty(message = "ea cannot be empty") String ea,
      @RequestParam("employeeId") @NotNull(message = "employeeId cannot be empty") Integer employeeId) {
    return Result.ok(openApiService.getMetaInfo(ea, employeeId, path));
  }

  // 兼容旧的调用
  @OpenGetWebLogger
  @GetMapping(value =  "/getPdfMetaInfo",params = {"npath", "ea", "employeeId"})
  public Result<GetMeatInfo.Result> getMetaInfoOld(
      @RequestParam("npath") @Pattern(regexp = "^(N_|TN_).*\\.(pdf)$", message = "Invalid npath") String npath,
      @RequestParam("ea") @NotEmpty(message = "ea cannot be empty") String ea,
      @RequestParam("employeeId") @NotNull(message = "employeeId cannot be empty") Integer employeeId) {
    return Result.ok(openApiService.getMetaInfo(ea, employeeId, npath));
  }

  @OpenGetWebLogger
  @GetMapping(value ={ "/officeToPdf"})
  public Result<OfficeToPdf.Result> officeToPdf(
      @RequestParam("path") @Pattern(regexp = "^(N_|TN_|TA_|A_|G_).*\\.(doc|docx|ppt|pptx|xls|xlsx|pdf)$", message = "Invalid path") String path,
      @RequestParam("ea") @NotEmpty(message = "ea cannot be empty") String ea,
      @RequestParam("employeeId") @NotNull(message = "employeeId cannot be empty") Integer employeeId,
      @RequestParam(name = "onePagePerSheet",defaultValue ="true")  boolean onePagePerSheet,
      @RequestParam(name = "allColumnsInOnePagePerSheet",defaultValue ="false")  boolean allColumnsInOnePagePerSheet){
    return Result.ok(openApiService.officeToPdf(ea, employeeId, path,onePagePerSheet,allColumnsInOnePagePerSheet));
  }

  @GetMapping(value = {"/wordToPdf"}, params = {"npath", "ea", "employeeId"})
  public Result<OfficeToPdf.Result> officeToPdf(
      @RequestParam("npath") @Pattern(regexp = "^(N_|TN_).*\\.(doc|docx)$", message = "Invalid npath") String npath,
      @RequestParam("ea") @NotEmpty(message = "ea cannot be empty") String ea,
      @RequestParam("employeeId") @NotNull(message = "employeeId cannot be empty") Integer employeeId) {
    return Result.ok(openApiService.officeToPdf(ea, employeeId, npath,true,false));
  }

  @OpenPostWebLogger
  @PostMapping("/mergerDocument")
  public Result<MergerOffice.Result> mergerOffice(@Validated @RequestBody MergerOffice.Arg arg) {
    return Result.ok(openApiService.mergerOffice(arg.getEa(), arg.getEmployeeId(), arg.getNpaths(), arg.getDocumentType()));
  }

  @PostMapping(value = "/mergerPdf")
  public Result<MergerOffice.Result> mergerPdf(@Validated @RequestBody MergerOffice.Arg arg) {
    return Result.ok(openApiService.mergerOffice(arg.getEa(), arg.getEmployeeId(), arg.getNpaths(), arg.getDocumentType()));
  }

  @OpenGetWebLogger
  @GetMapping("/toPngZip")
  public Result<ToPngZip.Result> toPngZip(
      @RequestParam("path") @Pattern(regexp = "^(N_|TN_|TA_|A_|G_).*\\.(doc|docx|ppt|pptx|xls|xlsx|pdf)$", message = "Invalid npath") String path,
      @RequestParam("ea") @NotEmpty(message = "ea cannot be empty") String ea,
      @RequestParam("employeeId") @NotNull(message = "employeeId cannot be empty") Integer employeeId) {
    return Result.ok(openApiService.toPngZip(ea, employeeId, path));
  }

  @OpenPostWebLogger
  @PostMapping("/watermark")
  public Result<Watermark.Result> watermark(@Validated @RequestBody Watermark.Arg arg) {
    return Result.ok(openApiService.watermark(arg));
  }

  @OpenPostWebLogger
  @PostMapping("/docPageQRCode")
  public Result<DocPageQRCode.Result> docPageQRCode(@Validated @RequestBody DocPageQRCode.Arg arg) {
    return Result.ok(openApiService.addPageQRCode(arg));
  }
}
