package com.fxiaoke.document.convert.utils;

import com.fxiaoke.document.convert.domain.constants.Constants;
import org.apache.commons.io.FilenameUtils;

import java.nio.file.Path;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class FileNameUtil {

  private static final String LINK =".";

  public static String createTempDir(String filePath) {
    return FilenameUtils.concat(FilenameUtils.getFullPath(filePath), FilenameUtils.getBaseName(filePath));
  }
  public static String createTempFileName(String filePath, String extension) {
    return FilenameUtils.removeExtension(filePath).concat(extension);
  }
  public static String createTempFileName(String filePath,int pageIndex,String extension) {
    return FilenameUtils.concat(FilenameUtils.getFullPath(filePath), pageIndex + LINK + extension);
  }

  /**
   * 获取文件扩展名
   * @param filename 文件名或路径
   * @return 文件扩展名
   */
  public static String getFileExtension(String filename) {
    return FilenameUtils.getExtension(filename);
  }

  /**
   * 替换文件扩展名
   * @param sourceFileLocalPath 源文件路径
   * @param ext 替换的扩展名
   */
  public static String replaceExtension(String sourceFileLocalPath, String ext) {
    return FilenameUtils.removeExtension(sourceFileLocalPath).concat("."+ext);
  }

  /**
   * 获取父文件路径
   * @param filePath 文件路径
   * @return 文件名
   */
  public static String getFullPath(String filePath) {
    return FilenameUtils.getFullPath(filePath);
  }


  private static String formatDate(String dateFormat) {
    return new SimpleDateFormat(dateFormat).format(new Date());
  }

  /**
   根据当前系统时间与ea生成临时文件夹路径
   * @param ea 用户ea
   * @return 临时文件夹
   */

  public static String generateDirAsEa(String ea,String rootDir) {
    String yyyyMM = formatDate("yyyyMM");
    String dd = formatDate("dd");
    String hh = formatDate("HH");
    return String.format("%s/%s/%s/%s/%s/%s/%s", rootDir, "dps", yyyyMM, dd, hh, ea, SampleUUID.getUUID());
  }


  public static String generateSimpFilePath(String prefix,String name, String suffix){
    StringBuilder fileName = new StringBuilder();
    if (prefix != null && !prefix.isEmpty()) {
      fileName.append(prefix).append("/");
    }
    fileName.append(name);
    if (suffix != null && !suffix.isEmpty()) {
      // 添加后缀
      fileName.append(".").append(suffix);
    }
    return fileName.toString();
  }

  /**
   * 生成文件名(带日期时间)
   * @param prefix 文件名前缀
   * @param name 主文件名
   * @param suffix 文件后缀（不包含点号）
   * @return 生成的完整文件名
   */
  public static String generateFilePath(String prefix,String name, String suffix) {
    // 获取当前时间
    LocalDateTime now = LocalDateTime.now();

    // 定义日期时间格式 (ddHH)
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/HH");

    // 格式化当前时间
    String dateTimeStr = now.format(formatter);

    // 构建文件名
    StringBuilder fileName = new StringBuilder();

    // 添加前缀（如果为空则使用默认缓存路径）
    if (prefix != null && !prefix.isEmpty()) {
      fileName.append(prefix).append("/");
    }else {
      fileName.append(Constants.DEFAULT_CACHE_PATH);
    }

    // 添加日期时间
    fileName.append(dateTimeStr).append("/");

    // 添加主文件名
    fileName.append(name);

    // 添加随机值防止高并发时文件名重复
    fileName.append("_").append(SampleUUID.getUUID());

    if (suffix != null && !suffix.isEmpty()) {
      // 添加后缀
      fileName.append(".").append(suffix);
    }

    return fileName.toString();
  }

  public static String getFileName(String filePath) {
    return FilenameUtils.getName(filePath);
  }

  // 生成清理后的文件路径
  public static Path generateCleanedMDFilePath(Path originalFile) {
    return originalFile.resolveSibling(originalFile.getFileName().toString().replace(".md", "_cleaned.md")
    );
  }

}
