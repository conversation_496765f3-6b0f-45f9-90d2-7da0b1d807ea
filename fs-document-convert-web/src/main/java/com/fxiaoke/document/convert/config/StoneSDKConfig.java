package com.fxiaoke.document.convert.config;

import com.facishare.restful.client.FRestApiProxyFactoryBean;
import com.facishare.stone.sdk.StoneProxyApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class StoneSDKConfig {
  @Bean(name = "stoneProxyApi")
  public FRestApiProxyFactoryBean<StoneProxyApi> stoneProxyApi() {
    FRestApiProxyFactoryBean<StoneProxyApi> factoryBean = new FRestApiProxyFactoryBean<>();
    factoryBean.setType(StoneProxyApi.class);
    return factoryBean;
  }
}
