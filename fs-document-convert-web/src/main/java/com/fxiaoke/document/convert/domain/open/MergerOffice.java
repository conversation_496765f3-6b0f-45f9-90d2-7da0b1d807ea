package com.fxiaoke.document.convert.domain.open;

import java.util.Collections;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

public interface MergerOffice {

  @Data
  @ToString
  class Arg {

    @NotBlank(message = "ea不能为空") String ea;

    @NotNull(message = "employeeId不能为空") Integer employeeId;

    @NotNull(message = "npaths不能为null")
    @NotEmpty(message = "npaths不能为空")
    @Size(min = 2, max = 30, message = "npaths的size不能小于2,大于30")
    List<String> npaths;

    @Pattern(regexp = "^(pdf)$", message = "documentType只能是pdf")
    String documentType;
  }

  @Data
  @EqualsAndHashCode(callSuper = true)
  @ToString(callSuper = true)
  class Result extends BaseResult {

    public Result(String ea,int employeeId,String path,String fileType){
      this.setEa(ea);
      this.setEmployeeId(employeeId);
      this.setNpaths(Collections.singletonList(path));
      this.setFileType(fileType);
    }
    public static Result of(String ea,int employeeId,String path,String fileType){
      return new Result(ea,employeeId,path,fileType);
    }
  }
}
