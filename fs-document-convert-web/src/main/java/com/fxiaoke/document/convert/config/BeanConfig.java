package com.fxiaoke.document.convert.config;

import com.facishare.fsi.proxy.FsiServiceProxyFactory;
import com.facishare.fsi.proxy.FsiServiceProxyFactoryBean;
import com.facishare.fsi.proxy.service.AFileStorageService;
import com.facishare.fsi.proxy.service.GFileStorageService;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.restful.client.FRestApiProxyFactoryBean;
import com.facishare.stone.sdk.StoneProxyApi;
import com.fxiaoke.common.http.spring.HttpSupportFactoryBean;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.stone.commons.SystemPresetClient;
import com.fxiaoke.stone.commons.impl.AppFrameworkExclusiveClient;
import com.fxiaoke.stone.commons.impl.MetadataExclusiveClient;
import com.xxl.job.core.executor.XxlJobExecutor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class BeanConfig {

    @Bean(initMethod = "start", destroyMethod = "destroy")
    public XxlJobExecutor xxlJobExecutor(CmsPropertiesConfig config) {
        XxlJobExecutor executor = new XxlJobExecutor();
        executor.setIp(config.getExecutorIp());
        executor.setPort(config.getExecutorPort());
        executor.setAppName(config.getAppName());
        executor.setAdminAddresses(config.getAdminAddresses());
        executor.setLogPath(config.getExecutorLogpath());
        executor.setAccessToken(config.getAccessToken());
        return executor;
    }

    @Bean
    public FRestApiProxyFactoryBean<StoneProxyApi> stoneProxyApi() {
        FRestApiProxyFactoryBean<StoneProxyApi> factoryBean = new FRestApiProxyFactoryBean<>();
        factoryBean.setType(StoneProxyApi.class);
        return factoryBean;
    }

    @Bean
    public FsiServiceProxyFactoryBean<GFileStorageService> gFileStorageService(FsiServiceProxyFactory fsiServiceProxyFactory) {
        FsiServiceProxyFactoryBean<GFileStorageService> factoryBean = new FsiServiceProxyFactoryBean<>();
        factoryBean.setType(GFileStorageService.class);
        factoryBean.setFactory(fsiServiceProxyFactory);
        return factoryBean;
    }

    @Bean
    public FsiServiceProxyFactoryBean<AFileStorageService> aFileStorageService(FsiServiceProxyFactory fsiServiceProxyFactory) {
        FsiServiceProxyFactoryBean<AFileStorageService> factoryBean = new FsiServiceProxyFactoryBean<>();
        factoryBean.setType(AFileStorageService.class);
        factoryBean.setFactory(fsiServiceProxyFactory);
        return factoryBean;
    }

    @Bean
    public FsiServiceProxyFactoryBean<NFileStorageService> nFileStorageService(FsiServiceProxyFactory fsiServiceProxyFactory) {
        FsiServiceProxyFactoryBean<NFileStorageService> factoryBean = new FsiServiceProxyFactoryBean<>();
        factoryBean.setType(NFileStorageService.class);
        factoryBean.setFactory(fsiServiceProxyFactory);
        return factoryBean;
    }


    @Bean
    public HttpSupportFactoryBean okHttpSupport() {
        HttpSupportFactoryBean factoryBean = new HttpSupportFactoryBean();
        factoryBean.init();
        return factoryBean;
    }


    @Bean
    public SystemPresetClient metadataExclusiveClient(OkHttpSupport okHttpClientSupport){
        return new MetadataExclusiveClient(okHttpClientSupport);
    }

    @Bean
    public SystemPresetClient appFrameworkExclusiveClient(OkHttpSupport okHttpClientSupport){
        return new AppFrameworkExclusiveClient(okHttpClientSupport);
    }
}
