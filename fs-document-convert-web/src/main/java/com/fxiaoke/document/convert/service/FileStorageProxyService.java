package com.fxiaoke.document.convert.service;

import com.facishare.fsi.proxy.model.warehouse.a.AGetFileMetaData;
import com.facishare.fsi.proxy.model.warehouse.a.User;
import com.facishare.fsi.proxy.model.warehouse.g.GFileDownload;
import com.facishare.fsi.proxy.model.warehouse.g.GFileDownload.Result;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NGetFileMetaData;
import com.facishare.fsi.proxy.service.AFileStorageService;
import com.facishare.fsi.proxy.service.GFileStorageService;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.fxiaoke.document.convert.domain.constants.Constants;
import com.fxiaoke.document.convert.domain.exception.BaseException;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j(topic = "fileStorageProxy")
public class FileStorageProxyService {
  public static final String MODULE = "FileStorageProxyService";
  public  static final String BUSINESS = "Preview";
  @Resource
  AFileStorageService aFileStorageService;
  @Resource
  NFileStorageService nFileStorageService;
  @Resource
  GFileStorageService gFileStorageService;

  public long getSizeByPath(String path, String ea, int employeeId) {
    long size = 0;
    try {
      if (path.startsWith("N_") || path.startsWith("TN_")){
        NGetFileMetaData.Arg arg = new NGetFileMetaData.Arg();
        arg.setEa(ea);
        arg.setFileName(path);
        arg.setDownUser("E." + employeeId);
        arg.setDownloadSecurityGroup(Constants.SECURITY_GROUP);
        size = nFileStorageService.nGetFileMetaData(arg, ea).getSize();
      }else if (path.startsWith("A_") || path.startsWith("TA_")) {
        AGetFileMetaData.Arg arg = new AGetFileMetaData.Arg();
        arg.setFileName(path);
        arg.setBusiness(BUSINESS);
        arg.setFileSecurityGroup(Constants.SECURITY_GROUP);
        User user = new User(ea, employeeId);
        arg.setUser(user);
        size = aFileStorageService.getFileMetaData(arg).getSize();
      } else{
        GFileDownload.Arg arg = new GFileDownload.Arg();
        arg.downloadUser = "E." +ea+"."+ employeeId;
        arg.gPath = path;
        arg.downloadSecurityGroup = Constants.SECURITY_GROUP;
        Result result = gFileStorageService.downloadFile(arg);
        size = result.data.length;
      }
    } catch (Exception e) {
      throw new BaseException(e, "获取文件大小失败,请检查文件后重试!", 400, MODULE + "-getSizeByPath", ea, "E." + ea+"."+ + employeeId,path);
    }
    log.info("Get File Size success fileSize:{} args: ea:{}, sourceUser:{},paths:{}",size,ea,"E." +ea+"."+employeeId, path);
    return size;
  }

  public long getSizeByPaths(List<String> paths, String ea, int employeeId) {
    long size = 0;
    try {
      for (String npath : paths) {
        if (npath.startsWith("A_") || npath.startsWith("TA_")) {
          AGetFileMetaData.Arg arg = new AGetFileMetaData.Arg();
          arg.setFileName(npath);
          arg.setBusiness(BUSINESS);
          arg.setFileSecurityGroup(Constants.SECURITY_GROUP);
          User user = new User(ea, employeeId);
          arg.setUser(user);
          size += aFileStorageService.getFileMetaData(arg).getSize();
        }else {
          NGetFileMetaData.Arg arg = new NGetFileMetaData.Arg();
          arg.setEa(ea);
          arg.setFileName(npath);
          arg.setDownUser("E."+ea+"."+ employeeId);
          arg.setDownloadSecurityGroup(Constants.SECURITY_GROUP);
          size += nFileStorageService.nGetFileMetaData(arg, ea).getSize();
        }
      }
    } catch (Exception e) {
      throw new BaseException(e, "获取文件大小失败,请检查文件后重试!", 400, MODULE + "-getSizeByPath", ea, "E." + ea + employeeId, paths);
    }
    log.info("Get File Size success fileSize:{} args: ea:{}, sourceUser:{},paths:{}",size,ea,"E." +ea+employeeId, paths);
    return size;
  }







}
