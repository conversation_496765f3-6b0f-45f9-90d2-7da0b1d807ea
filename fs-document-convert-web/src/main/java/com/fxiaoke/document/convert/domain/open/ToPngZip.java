package com.fxiaoke.document.convert.domain.open;

import com.fxiaoke.document.convert.domain.preview.BaseArg;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

public interface ToPngZip {

  @Data
  @EqualsAndHashCode(callSuper = true)
  @ToString(callSuper = true)
  class Arg extends BaseArg {
    @NotBlank(message = "filePath cannot be empty")
    @Pattern(regexp = ".+(.doc|.docx|.pdf)$", message = "This file format is not supported")
    private String filePath;
  }

  @Data
  @ToString
  class Result{
    String path;
    public Result(String path){
      this.path = path;
    }
    public static Result of(String path){
      return new Result(path);
    }
  }
}
