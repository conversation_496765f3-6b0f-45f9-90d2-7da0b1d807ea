package com.fxiaoke.document.convert.domain.preview.entity;

import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Field;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.Indexes;

@Entity(value = "PreviewInfo", noClassnameStored = true)
@Indexes({
    @Index(fields = {@Field("path")})}
)
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class PreviewInfo {

  @Id
  private ObjectId id;
  private String path;
  private Date createTime;
  private int createYYMMDD;
  private String ea;
  private int pageCount;
  private List<String> sheetNames;
  private String dirName;
  private String dataDir;
  private long employeeId;
  private long docSize;//原始大小
  private String originalFilePath;//原始文件
  private List<String> filePathList;
  private int width;//文档页码宽度
  private int pdfConvertType;//0表示html 1表示image
}
