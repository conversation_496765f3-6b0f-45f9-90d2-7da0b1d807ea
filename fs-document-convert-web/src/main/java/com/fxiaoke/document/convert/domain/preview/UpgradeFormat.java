package com.fxiaoke.document.convert.domain.preview;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

public interface UpgradeFormat {

  @Data
  @ToString(callSuper = true)
  @EqualsAndHashCode(callSuper = true)
  class Arg extends BaseArg{
  }

  @Data
  class Result{
    String filePath;

    public Result(String filePath) {
      this.filePath = filePath;
    }

    public static Result of(String filePath) {
      return new Result(filePath);
    }
  }
}
