package com.fxiaoke.document.convert.dao.impl;

import com.fxiaoke.document.convert.dao.IPreviewInfoDao;
import com.fxiaoke.document.convert.domain.preview.entity.PreviewInfo;
import com.fxiaoke.document.convert.utils.DataTimeFormatUtil;
import com.github.mongo.support.DatastoreExt;
import com.mongodb.WriteResult;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
public class PreviewInfoDao implements IPreviewInfoDao {

  @Resource
  private DatastoreExt dpsDataStore;


  /**
   * 将渲染完成的页码更新到预览信息中
   *
   * @param ea       企业账号
   * @param path     文件Path
   * @param filePath 预览宽度
   */
  @Override
  public void updateFilePathList(String ea, String path, String filePath) {
    String filePathList = FilenameUtils.getName(filePath);
    Query<PreviewInfo> query = dpsDataStore.createQuery(PreviewInfo.class);
    query.and(query.criteria("path").equal(path).criteria("ea").equal(ea));
    UpdateOperations<PreviewInfo> update = dpsDataStore.createUpdateOperations(PreviewInfo.class);
    update.add("filePathList", filePathList);
    dpsDataStore.update(query, update);
  }

  @Override
  public void updatePageCount(String ea,String path,int pageCount){
    Query<PreviewInfo> query = dpsDataStore.createQuery(PreviewInfo.class);
    query.and(query.criteria("path").equal(path).criteria("ea").equal(ea));
    UpdateOperations<PreviewInfo> update = dpsDataStore.createUpdateOperations(PreviewInfo.class);
    update.set("pageCount", pageCount);
    dpsDataStore.update(query, update);
  }

  @Override
  public void updateSheetNamesAndPageCount(String ea, String path,List<String> sheetNames,int pageCount) {
    Query<PreviewInfo> query = dpsDataStore.createQuery(PreviewInfo.class);
    query.and(query.criteria("path").equal(path).criteria("ea").equal(ea));
    UpdateOperations<PreviewInfo> update = dpsDataStore.createUpdateOperations(PreviewInfo.class);
    update.set("sheetNames", sheetNames);
    update.set("pageCount", pageCount);
    dpsDataStore.update(query, update);
  }
  @Override
  public void updateFilePath(String ea,String path,String filePath){
    Query<PreviewInfo> query = dpsDataStore.createQuery(PreviewInfo.class);
    query.and(query.criteria("path").equal(path).criteria("ea").equal(ea));
    UpdateOperations<PreviewInfo> update = dpsDataStore.createUpdateOperations(PreviewInfo.class);
    update.set("originalFilePath", filePath);
    dpsDataStore.update(query, update);
  }
  @Override
  public PreviewInfo queryByPath(String ea, String path) {
    Query<PreviewInfo> query = dpsDataStore.createQuery(PreviewInfo.class);
    query.and(query.criteria("path").equal(path).criteria("ea").equal(ea));
    return query.get();
  }

  @Override
  public void  delete(String ea, String path){
    Query<PreviewInfo> query = dpsDataStore.createQuery(PreviewInfo.class);
    query.and(query.criteria("path").equal(path).criteria("ea").equal(ea));
    WriteResult result = dpsDataStore.delete(query);
    log.warn("delete Preview MetaInfo args：ea={},path={},number={}",ea,path,result.getN());
  }
  @Override
  public void save(String ea, String path, long employeeId, String filePath, long size, int pageCount, List<String> sheetNames) {
    PreviewInfo info = new PreviewInfo();
    info.setEa(ea);
    info.setPath(path);
    info.setEmployeeId(employeeId);
    info.setOriginalFilePath(filePath);
    info.setDocSize(size);
    info.setPageCount(pageCount);
    info.setSheetNames(sheetNames);
    String fullPath = FilenameUtils.getFullPath(filePath);
    info.setDataDir(fullPath);
    info.setDirName(FilenameUtils.getBaseName(fullPath));
    info.setCreateTime(new Date());
    info.setCreateYYMMDD(Integer.parseInt(DataTimeFormatUtil.getCurrentDate("yyyyMMdd")));
    info.setWidth(1000);
    info.setPdfConvertType(0);
    dpsDataStore.insert("PreviewInfo",info);
    dpsDataStore.ensureIndexes();
  }

  @Override
  public void clean(List<String> pathList) {
    Query<PreviewInfo> query = dpsDataStore.createQuery(PreviewInfo.class);
    query.criteria("path").in(pathList);
    dpsDataStore.delete(query);
    log.info("paths:{}", pathList);
  }

  @Override
  public List<PreviewInfo> getPreviewInfoByPageV2(int limit, Date maxDate) {
    Query<PreviewInfo> query = dpsDataStore.createQuery(PreviewInfo.class);
    if (maxDate != null) {
      query.criteria("createTime").lessThan(maxDate);
    }
    return query.order("createTime").limit(limit).asList();
  }

  @Override
  public PreviewInfo queryByDataDir(String dataDir) {
    Query<PreviewInfo> query = dpsDataStore.createQuery(PreviewInfo.class);
    query.criteria("dataDir").contains(dataDir);
    return query.get();
  }
}
