package com.fxiaoke.document.convert.domain.mapper;


import com.fxiaoke.document.convert.domain.model.AudioConvertArg;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface AudioArgMapper {

  @Mapping(target = "ea", source = "audioConvertArg.ea")
  @Mapping(target = "employeeId", source = "employeeId")
  @Mapping(target = "securityGroup", source = "securityGroup")
  @Mapping(target = "path", source = "audioConvertArg.path")
  @Mapping(target = "sourceType", source = "audioConvertArg.sourceType")
  @Mapping(target = "targetType", source = "audioConvertArg.targetType")
  AudioConvertArg supplementAudioFormatConvert(AudioConvertArg audioConvertArg, Integer employeeId, String securityGroup);
}