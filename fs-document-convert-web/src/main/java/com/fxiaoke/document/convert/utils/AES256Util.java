package com.fxiaoke.document.convert.utils;

import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Objects;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

@Slf4j(topic = "AES256Util")
public class AES256Util {
  public static final String aesKey = "nirtHUNF/Ct8J7sf40VaIQui0N5r8gcbxGXKxRhu1C4=";
  public static final String aesIv = "jwNz4Ia8OHVpPyEXIQjJ2g==";

  private static final byte[] keyBytes;
  private static final byte[] ivBytes;

  public static Cipher decrypt_c;
  public static Cipher encrypt_c;

  static {
    try {
      keyBytes = Base64.decodeBase64(aesKey);
      ivBytes = Base64.decodeBase64(aesIv);

      IvParameterSpec iv = new IvParameterSpec(ivBytes);
      SecretKeySpec key = new SecretKeySpec(keyBytes, "AES");

      decrypt_c = Cipher.getInstance("AES/CBC/PKCS5Padding");
      encrypt_c = Cipher.getInstance("AES/CBC/PKCS5Padding");

      decrypt_c.init(Cipher.DECRYPT_MODE, key, iv, new SecureRandom(keyBytes));
      encrypt_c.init(Cipher.ENCRYPT_MODE, key, iv, new SecureRandom(keyBytes));
    } catch (Throwable e) {
      throw new RuntimeException(e);
    }
  }

  public static String encode(String source) {
    try {
      byte[] resultByte = encrypt_c.doFinal(source.getBytes(StandardCharsets.UTF_8));
      return parseByte2HexStr(resultByte);
    } catch (Throwable e) {
      throw new RuntimeException(e);
    }
  }

  public static String parseByte2HexStr(byte[] buf) {
    StringBuilder sb = new StringBuilder();
    for (byte b : buf) {
      String hex = Integer.toHexString(b & 0xFF);
      if (hex.length() == 1) {
        hex = '0' + hex;
      }
      sb.append(hex.toUpperCase());
    }

    return sb.toString();
  }

  public static synchronized String decode(String source) {
    String result;
    try {
      byte[] resultByte = decrypt_c.doFinal(Objects.requireNonNull(parseHexStr2Byte(source)));
      result = new String(resultByte, StandardCharsets.UTF_8);
    } catch (Throwable e) {
      log.warn("decode token error,source:{}", source, e);
      throw new RuntimeException(e);
    }
    return result;
  }

  public static byte[] parseHexStr2Byte(String hexStr) {
    if (hexStr.length() < 1) {
      return null;
    }
    byte[] result = new byte[hexStr.length() / 2];
    for (int i = 0; i < hexStr.length() / 2; i++) {
      int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
      int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
      result[i] = (byte) (high * 16 + low);
    }
    return result;
  }

}
