package com.fxiaoke.document.convert.utils;
import com.fxiaoke.document.convert.domain.model.ImageDimension;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import javax.imageio.ImageIO;

public class ImageUtil {

  public static ImageDimension getCompressingWH(int width, int height, double maxResolution) {
    if (width * height > maxResolution) {
      double ratio = Math.sqrt(width * height / maxResolution);
      int newWidth = (int) Math.round(width / ratio);
      int newHeight = (int) Math.round(height / ratio);
      return new ImageDimension(newWidth, newHeight);
    }
    return new ImageDimension(width, height);
  }

  public static boolean isSupportedConvertImageType(String contentType) {
    if (contentType!=null&&!contentType.isEmpty()) {
      return contentType.startsWith("image/gif");
    }
    return false;
  }

  public static boolean isSupportedCompressingImageType(String contentType) {
    if (contentType!=null&&!contentType.isEmpty()) {
      return contentType.startsWith("image/jpeg") ||
             contentType.startsWith("image/png") ||
             contentType.startsWith("image/gif");
    }
    return false;
  }


  public static byte[] gif2Png(byte[] gifData) throws IOException {
    ByteArrayInputStream bis = new ByteArrayInputStream(gifData);
    BufferedImage gifImage = ImageIO.read(bis);
    ByteArrayOutputStream bos = new ByteArrayOutputStream();
    ImageIO.write(gifImage, "png", bos);
    return bos.toByteArray();
  }
}
