package com.fxiaoke.document.convert.domain.model;

import lombok.Getter;

@Getter
public enum CorrectionLevel {
  L(0.07),   // 低级别，约7%纠错
  M(0.15),   // 中级别，约15%纠错
  Q(0.25),   // 高级别，约25%纠错
  H(0.30);   // 最高级别，约30%纠错

  private final double level;  // 容错百分比

  CorrectionLevel(double level) {
    this.level = level;
  }

  public static CorrectionLevel of(String level){
    for (CorrectionLevel correctionLevel : CorrectionLevel.values()) {
      if (correctionLevel.name().equalsIgnoreCase(level)) {
        return correctionLevel;
      }
    }
    throw new IllegalArgumentException("Invalid correction level: " + level);
  }
}
