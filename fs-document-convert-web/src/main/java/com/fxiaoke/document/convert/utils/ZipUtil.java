package com.fxiaoke.document.convert.utils;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class ZipUtil {

  /**
   * 压缩文件夹
   * @param zipDir       要压缩的文件夹路径
   * @param zipFile      压缩后的zip文件路径
   * @throws IOException IO异常
   */
  public static void zip(String zipDir, String zipFile) throws IOException {
    Path sourceDir = Paths.get(zipDir);
    try (FileOutputStream fos = new FileOutputStream(zipFile);
        BufferedOutputStream bos = new BufferedOutputStream(fos);
        ZipOutputStream zipOut = new ZipOutputStream(bos);
        Stream<Path> paths = Files.walk(sourceDir)) {

      paths.filter(path -> !Files.isDirectory(path)).forEach(path -> {
        try (FileInputStream fis = new FileInputStream(path.toFile());
            BufferedInputStream bis = new BufferedInputStream(fis)) {

          String filePath = sourceDir.relativize(path).toString();
          ZipEntry zipEntry = new ZipEntry(filePath);
          zipOut.putNextEntry(zipEntry);
          byte[] buffer = new byte[4096];
          int length;
          while ((length = bis.read(buffer)) > 0) {
            zipOut.write(buffer, 0, length);
          }

          zipOut.closeEntry();
        } catch (IOException e) {
          throw new RuntimeException("Error while zipping file: " + path, e);
        }
      });
    }
  }

}
