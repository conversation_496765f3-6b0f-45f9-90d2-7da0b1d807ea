package com.fxiaoke.document.convert.service;

import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.fxiaoke.common.Pair;
import com.fxiaoke.document.convert.config.CmsPropertiesConfig;
import com.fxiaoke.document.convert.domain.constants.FileType;
import com.fxiaoke.document.convert.domain.exception.BaseException;
import com.fxiaoke.document.convert.domain.open.DocPageQRCode;
import com.fxiaoke.document.convert.domain.open.GetMeatInfo;
import com.fxiaoke.document.convert.domain.open.MergerOffice;
import com.fxiaoke.document.convert.domain.open.OfficeToPdf;
import com.fxiaoke.document.convert.domain.open.ToPngZip;
import com.fxiaoke.document.convert.domain.open.Watermark;
import com.fxiaoke.document.convert.process.CellsProcess;
import com.fxiaoke.document.convert.process.PdfProcess;
import com.fxiaoke.document.convert.process.SlidesProcess;
import com.fxiaoke.document.convert.process.WordsProcess;
import com.fxiaoke.document.convert.utils.SimpFileUtil;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OpenApiService {

  @Resource
  StoneApiService stoneApiService;
  @Resource
  FileStorageProxyService fileStorageProxyService;
  @Resource
  CmsPropertiesConfig cmsPropertiesConfig;

  @Resource
  SlidesProcess slidesProcess;
  @Resource
  WordsProcess wordsProcess;
  @Resource
  PdfProcess pdfProcess;
  @Resource
  CellsProcess cellsProcess;

  @Resource
  IPDFBoxService pdfBoxService;

  @Resource
  FileCleanupManager fileCleanupManager;

  private static final String MODULE = "OpenApiService";

  /**
   * 获取文件大小
   *
   * @param ea         企业账号
   * @param employeeId 员工id
   * @param path       文件在文件系统中的唯一标识
   * @return 文件大小
   */
  private long getFileSize(String ea, int employeeId, String path) {
    long fileSize = fileStorageProxyService.getSizeByPath(path, ea, employeeId);
    verifyFileSize(fileSize);
    return fileSize;
  }

  /**
   * 验证文件总大小
   *
   * @param ea         企业账号
   * @param employeeId 员工id
   * @param paths      文件path集合
   */
  private void verifyFileSize(String ea, int employeeId, List<String> paths) {
    long fileSize = fileStorageProxyService.getSizeByPaths(paths, ea, employeeId);
    verifyFileSize(fileSize);
  }

  private void verifyFileSize(long fileSize) {
    if (fileSize > 104857600L || fileSize <= 0) {
      throw new BaseException(400, "文件不能为空且大小应在100M以内", MODULE + "-verifyFileSize",
          fileSize);
    }
  }

  /**
   * 文件处理前置动作     下载文件并保存到本地
   *
   * @param ea         企业账号
   * @param employeeId 员工id
   * @param path       文件在文件系统中的唯一标识
   * @param extension  文件扩展名
   * @return 本地文件路径
   */
  private String downloadSaveToLocal(String ea, int employeeId, String path, long fileSize,
      String extension) {
    // 创建临时文件 及路径
    Path tempFile = SimpFileUtil.createTempFile(cmsPropertiesConfig.getRootDir(), ea, employeeId,
        extension);
    // 下载文件并保存到本地
    try (InputStream stream = stoneApiService.downloadByStream(ea, employeeId, path)) {
      SimpFileUtil.saveFileToLocal(tempFile, stream, fileSize);
    } catch (IOException e) {
      throw new BaseException(500, "下载文件,返回流异常", MODULE + "-getMetaInfo", ea, employeeId,
          path);
    }
    return tempFile.toString();
  }

  private Pair<String, Long> uploadFileGetPathAndSize(String ea, int employeeId, String filePath,
      String extension) {
    Path file = Path.of(filePath);
    long fileSize = SimpFileUtil.getLocalFileSize(file);
    try (InputStream stream = Files.newInputStream(file)) {
      StoneFileUploadResponse stoneFileUploadResponse = stoneApiService.tempFileUploadByStream(ea,
          employeeId, Math.toIntExact(fileSize), "", extension, 3, stream);
      return Pair.of(stoneFileUploadResponse.getPath(), fileSize);
    } catch (IOException e) {
      throw new BaseException(500, "返回流异常", MODULE + "-getMetaInfo", ea, employeeId, filePath,
          fileSize);
    }
  }

  private String uploadFile(String ea, int employeeId, String filePath, String extension) {
    Path file = Path.of(filePath);
    long fileSize = SimpFileUtil.getLocalFileSize(file);
    try (InputStream stream = Files.newInputStream(file)) {
      StoneFileUploadResponse stoneFileUploadResponse = stoneApiService.tempFileUploadByStream(ea,
          employeeId, Math.toIntExact(fileSize), "", extension, 3, stream);
      return stoneFileUploadResponse.getPath();
    } catch (IOException e) {
      throw new BaseException(500, "返回流异常", MODULE + "-getMetaInfo", ea, employeeId, filePath,
          fileSize);
    }
  }

  public GetMeatInfo.Result getMetaInfo(String ea, int employeeId, String path) {
    // 获取文件类型
    FileType fileType = FileType.valueOfName(FilenameUtils.getExtension(path));
    long fileSize = getFileSize(ea, employeeId, path);
    String tempFilePath = downloadSaveToLocal(ea, employeeId, path, fileSize,
        fileType.getFileTypeName());
    // 根据文件类型
    return switch (fileType) {
      case DOC, DOCX -> GetMeatInfo.Result.of(wordsProcess.getMetaInfo(tempFilePath, ea));
      case XLS, XLSX -> GetMeatInfo.Result.of(cellsProcess.getMetaInfo(tempFilePath));
      case PPT, PPTX -> GetMeatInfo.Result.of(slidesProcess.getMetaInfo(tempFilePath, false));
      case PDF -> GetMeatInfo.Result.of(pdfProcess.getMetaInfo(tempFilePath));
      default ->
          throw new BaseException(400, "文件类型不支持", MODULE + "-getMetaInfo", ea, employeeId,
              path);
    };
  }

  public OfficeToPdf.Result officeToPdf(String ea, int employeeId, String path,
      boolean onePagePerSheet, boolean allColumnsInOnePagePerSheet) {
    // 获取文件类型
    FileType fileType = FileType.valueOfName(FilenameUtils.getExtension(path));
    long fileSize = getFileSize(ea, employeeId, path);
    String tempFilePath = downloadSaveToLocal(ea, employeeId, path, fileSize,
        fileType.getFileTypeName());

    String tempPdfLocalPath;
    // 根据文件类型 将文件转换为pdf
    switch (fileType) {
      case DOC, DOCX -> tempPdfLocalPath = wordsProcess.officeToPdf(tempFilePath);
      case XLS, XLSX -> tempPdfLocalPath = cellsProcess.officeToPdf(tempFilePath, onePagePerSheet,
          allColumnsInOnePagePerSheet);
      case PPT, PPTX -> tempPdfLocalPath = slidesProcess.officeToPdf(tempFilePath);
      default ->
          throw new BaseException(400, "文件类型不支持", MODULE + "-officeToPdf", ea, employeeId,
              path);
    }
    // 获取总页数
    int pageCount = pdfProcess.getMetaInfo(tempPdfLocalPath);
    // 上传pdf文件
    String pdfPath = uploadFile(ea, employeeId, tempPdfLocalPath, "pdf");
    log.info(
        "officeToPdf success,ea:{},employeeId:{},path:{},resultPath:{},pdfCachePath:{},pageCount:{}",
        ea, employeeId, path, pdfPath, tempPdfLocalPath, pageCount);
    return OfficeToPdf.Result.of(ea, employeeId, pdfPath, pageCount, "pdf");
  }


  public MergerOffice.Result mergerOffice(String ea, Integer employeeId, List<String> paths,
      String type) {
    FileType fileType = FileType.valueOfName(type);
    // 校验合并前文件总大小
    verifyFileSize(ea, employeeId, paths);
    List<String> filePaths = new ArrayList<>();
    for (String path : paths) {
      // 获取文件类型
      long fileSize = getFileSize(ea, employeeId, path);
      String tempFilePath = downloadSaveToLocal(ea, employeeId, path, fileSize,
          fileType.getFileTypeName());
      filePaths.add(tempFilePath);
    }
    // 创建主文件及路径
    String masterFilePath = SimpFileUtil.createTempFile(cmsPropertiesConfig.getRootDir(), ea,
        employeeId, type).toString();
    // 根据文件类型 合并文件
    if (fileType == FileType.PDF) {
      pdfProcess.merge(filePaths, masterFilePath);
    } else {
      throw new BaseException(400, "文件类型不支持", MODULE + "-getMetaInfo", ea, employeeId,
          paths);
    }
    // 上传合并后的文件
    String tempPath = uploadFile(ea, employeeId, masterFilePath, type);

    return MergerOffice.Result.of(ea, employeeId, tempPath, type);
  }

  public ToPngZip.Result toPngZip(String ea, Integer employeeId, String path) {
    FileType fileType = FileType.valueOfName(FilenameUtils.getExtension(path));
    long fileSize = getFileSize(ea, employeeId, path);
    String filePath = downloadSaveToLocal(ea, employeeId, path, fileSize,
        fileType.getFileTypeName());
    String pngZipPath = switch (fileType) {
      case PDF -> pdfProcess.toPngZip(filePath);
      case DOC, DOCX -> wordsProcess.toPngZip(filePath);
      default ->
          throw new BaseException(400, "文件类型不支持", MODULE + "-toPngZip", ea, employeeId,
              path);
    };
    // 上传合并后的文件
    String tempPath = uploadFile(ea, employeeId, pngZipPath, "zip");
    return ToPngZip.Result.of(tempPath);
  }


  public Watermark.Result watermark(Watermark.Arg arg) {
    // 获取文件类型
    FileType fileType = FileType.valueOfName(arg.getExtension());
    // 校验文件大小
    int fileSize = Math.toIntExact(getFileSize(arg.getEa(), arg.getEmployeeId(), arg.getPath()));
    try (InputStream stream = stoneApiService.downloadByStream(arg.getEa(), arg.getEmployeeId(),
        arg.getPath())) {
      // 添加水印
      Path watermarkPath = pdfBoxService.addTextWatermark(stream,
          cmsPropertiesConfig.getRootDir(), arg);
      int watermarkFileSize = Math.toIntExact(Files.size(watermarkPath));
      try (InputStream watermarkStream = Files.newInputStream(watermarkPath)) {
        StoneFileUploadResponse stoneFileUploadResponse = stoneApiService.tempFileUploadByStream(
            arg.getEa(), arg.getEmployeeId(), watermarkFileSize, arg.getFileName(),
            fileType.getFileTypeName(), 3, watermarkStream);
        String path = stoneFileUploadResponse.getPath();
        Long size = stoneFileUploadResponse.getSize();
        String extensionName = stoneFileUploadResponse.getExtensionName();
        fileCleanupManager.addFileToCleanup(watermarkPath);
        return Watermark.Result.of(size, path, extensionName);
      }
    } catch (IOException e) {
      throw new BaseException(500, "返回流异常", MODULE + "-toWatermark", arg.getEa(),
          arg.getEmployeeId(), arg.getPath(), fileSize);
    }
  }


  public DocPageQRCode.Result addPageQRCode(DocPageQRCode.Arg arg) {
    // 获取文件类型
    FileType fileType = FileType.valueOfName(arg.getExtension());
    // 校验文件大小
    int fileSize = Math.toIntExact(getFileSize(arg.getEa(), arg.getEmployeeId(), arg.getPath()));
    try {

      // 下载文件并保存到本地
      String tempFilePath = downloadSaveToLocal(arg.getEa(), arg.getEmployeeId(), arg.getPath(),
          fileSize, fileType.getFileTypeName());

      // Word 文件需要进行类型转换
      if (fileType == FileType.DOC||fileType==FileType.DOCX) {
        tempFilePath = wordsProcess.officeToPdf(tempFilePath);
      }

      // 添加二维码
      Path docPageQRCodePath = pdfBoxService.addQRCode(tempFilePath, arg.getPageQRCodes(),
          arg.getOwnerPassword(),
          arg.getAccessPassword());
      int docPageQRCodeFileSize = Math.toIntExact(Files.size(docPageQRCodePath));

      // 上传文件
      try (InputStream fileStream = Files.newInputStream(docPageQRCodePath)) {
        StoneFileUploadResponse stoneFileUploadResponse = stoneApiService.tempFileUploadByStream(
            arg.getEa(), arg.getEmployeeId(), docPageQRCodeFileSize, arg.getFileName(),
            FileType.PDF.getFileTypeName(), 3, fileStream);
        String path = stoneFileUploadResponse.getPath();
        Long size = stoneFileUploadResponse.getSize();
        String extensionName = stoneFileUploadResponse.getExtensionName();
        // 清理临时文件
        fileCleanupManager.addFileToCleanup(docPageQRCodePath.getParent());
        return DocPageQRCode.Result.of(size, path, extensionName);
      }
    } catch (IOException e) {
      throw new BaseException(500, "返回流异常", MODULE + "-addPageQRCode", arg.getEa(),
          arg.getEmployeeId(), arg.getPath(), fileSize);
    }
  }
}
