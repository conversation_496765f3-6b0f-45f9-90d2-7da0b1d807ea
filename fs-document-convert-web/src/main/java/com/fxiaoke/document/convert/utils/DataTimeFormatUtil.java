package com.fxiaoke.document.convert.utils;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

public class DataTimeFormatUtil {

    private static final String yyyyMMdd = "yyyyMMdd";

    private DataTimeFormatUtil(){
    }

  public static String getCurrentDate(String format) {
    LocalDate currentDate = LocalDate.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
    return currentDate.format(formatter);
  }

  public static String getCurrentTime(String format) {
    LocalTime currentTime = LocalTime.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
    return currentTime.format(formatter);
  }

  public static String getMinusDays(int days) {
    LocalDate currentDate = LocalDate.now();
    LocalDate clearDate = currentDate.minusDays(days);
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(yyyyMMdd);
    return clearDate.format(formatter);
  }
}
