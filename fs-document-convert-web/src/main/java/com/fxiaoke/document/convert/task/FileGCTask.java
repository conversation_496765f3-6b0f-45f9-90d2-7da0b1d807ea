package com.fxiaoke.document.convert.task;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import com.github.autoconf.ConfigFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.fxiaoke.document.convert.config.CmsPropertiesConfig;
import com.fxiaoke.document.convert.dao.IPreviewInfoDao;
import com.fxiaoke.document.convert.domain.preview.entity.PreviewInfo;
import com.fxiaoke.document.convert.utils.FSFileUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Component
@Slf4j
@JobHander(value = "fileConvertGCHandler")
public class FileGCTask extends IJobHandler {

    private int allowGcDay;

    private int timeOut;

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-dps-config", config -> {
            allowGcDay = config.getInt("allow.gc.day");
            timeOut = config.getInt("timeOut", 18000); // 默认5小时超时(5*3600=18000秒)
        });
    }

    @Autowired
    private IPreviewInfoDao previewInfoDao;
    @Resource
    private CmsPropertiesConfig cmsPropertiesConfig;

    private final ExecutorService executorService = Executors.newSingleThreadExecutor();

    @Override
    public ReturnT execute(TriggerParam params) throws Exception {
        log.info("开始执行文件缓存清理任务");
        int limit = 1000;
        int skip = 0;
        log.info("gc 开始");

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        String clearDir = cmsPropertiesConfig.getRootDir();
        // 异步清理没有meta元数据的文件
        executorService.execute(() -> {
            cleanNoMetaInfoCache(startTime, clearDir);
        });

        do {
            List<PreviewInfo> infos = previewInfoDao.getPreviewInfoByPageV2(limit,
                    new Date(System.currentTimeMillis() - (long) allowGcDay * 24 * 3600 * 1000));
            if (CollectionUtils.isNotEmpty(infos)) {
                // 先删除meta元数据
                previewInfoDao.clean(infos.stream().map(PreviewInfo::getPath).collect(Collectors.toList()));
                // 再删除smb文件
                infos.forEach(info -> FSFileUtil.delete(info.getDataDir()));
                log.info("gc size :{}", infos.size());
            }
            skip = skip + infos.size();

            if (infos.isEmpty()) {
                break;
            }
            // 检查是否超时
            if (System.currentTimeMillis() - startTime > timeOut * 1000L) {
                log.warn("gc任务执行超时，已运行{}小时，强制退出", timeOut / 3600);
                break;
            }
        } while (true);

        log.info("gc 结束！总共处理文件数: {}", skip);
        FSFileUtil.deleteEmptyDir(clearDir); // 清除空文件夹
        return ReturnT.SUCCESS;
    }

  private void cleanNoMetaInfoCache(long startTime, String clearDir) {
    long GcCurrentTime = (long) allowGcDay * 24 * 3600 * 1000;
    Date expires = new Date(startTime - GcCurrentTime);
    String yearsDay = DateUtil.format(expires, "yyyyMMdd");
    File rootDir = new File(clearDir);
    log.info("清理文件夹:{}", rootDir.getAbsoluteFile());
    File[] files = rootDir.listFiles();
    log.info("清理文件夹大小:{}", files.length);
    for (File yyyyMMddFile : files) {
      if (yyyyMMddFile.length() == 0) { // 如果文件夹为空，则删除
        try {
          log.info("删除文件夹:{}", yyyyMMddFile.getAbsolutePath());
          FileUtils.forceDelete(yyyyMMddFile);
        } catch (IOException e) {
          log.error("删除目录:{}失败,错误原因:{}", yyyyMMddFile.getAbsolutePath(), e.getMessage());
        }
      } else {
        if (System.currentTimeMillis() - startTime > timeOut * 1000L) { // 如果超过了执行时间，退出
          executorService.shutdown();
        }
        if (Objects.nonNull(previewInfoDao.queryByDataDir(yyyyMMddFile.getAbsolutePath()))) { // 如果meta信息存在，证明有其他线程在清理，本方法只做无meta信息的文件清理
          continue;
        }
        try {

          // 如果gc截止时间大于文件夹的时间，干掉这个文件夹 yyyyMM
          if (yearsDay.compareTo(yyyyMMddFile.getName()) > 0) {
            log.info("删除目录:{}", yyyyMMddFile.getAbsolutePath());
            FileUtils.forceDelete(new File(yyyyMMddFile.getAbsolutePath()));
          }
        } catch (IOException e) {
          log.error("删除目录:{}失败,错误原因:{}", yyyyMMddFile.getAbsolutePath(), e.getMessage());
        }

      }
    }
    log.info("清理文件夹完成：{}", clearDir);
  }
}
