package com.fxiaoke.document.convert.config;

import com.github.autoconf.ConfigFactory;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

@Data
@Configuration
@ConfigurationProperties
public class CmsPropertiesConfig {
  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("spring-cloud-fs-document-converter", config -> {
      this.executorIp = config.get("xxl.job.executor.ip");
      this.executorPort = config.getInt("xxl.job.executor.port");
      this.adminAddresses = config.get("xxl.job.admin.addresses");
      this.appName = config.get("xxl.job.app.name");
      this.executorLogpath = config.get("xxl.job.executor.logpath");
      this.accessToken = config.get("xxl.job.accessToken");
    });
  }


  // Excel 最大行数
  private int cellsMaxRow = 3000;
  // Excel 最大列数
  private int cellsMaxColumn = 100;
  // 文件预览服务文件存储根目录
  private String rootDir = "/smbshare/preview";
  // Excel转HTML时 是否仅导出表格数据（CSS会被单独保存为文件）
  private boolean onlyExportTable = false;

  private boolean pptPreprocessing = false;



  private String executorIp;
  private Integer executorPort;
  private String adminAddresses;
  private String appName;
  private String executorLogpath;
  private String accessToken;


  private String localCachePath;
}
