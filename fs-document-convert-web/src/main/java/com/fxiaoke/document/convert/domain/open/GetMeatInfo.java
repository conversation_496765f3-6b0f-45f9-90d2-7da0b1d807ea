package com.fxiaoke.document.convert.domain.open;

import com.fxiaoke.document.convert.domain.preview.GetMateInfo;
import java.util.Collections;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

public interface GetMeatInfo {
  @Data
  @EqualsAndHashCode(callSuper = true)
  @ToString(callSuper = true)
  class Result extends BaseResult{
    public Result(int pageCount){
      this.setPageCount(pageCount);
    }
    public Result(GetMateInfo.Result result){
      this.setPageCount(result.getPageCount());
      this.setSheetNames(result.getSheetNames());
    }
    public static Result of(int pageCount){
      return new Result(pageCount);
    }
    public static Result of(GetMateInfo.Result result){
      return new Result(result);
    }

  }
}
