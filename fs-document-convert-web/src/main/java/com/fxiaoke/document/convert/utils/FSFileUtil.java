package com.fxiaoke.document.convert.utils;

import java.io.File;
import java.io.IOException;

import org.apache.commons.io.FileUtils;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

@UtilityClass
@Slf4j
public class FSFileUtil {

    public void delete(String dir) {
        File file = new File(dir);
        if (file.exists()) {
            try {
                log.info("删除文件:{}", file.getAbsolutePath());
                FileUtils.forceDelete(file);
            } catch (IOException e) {
                log.error("删除文件失败, 文件路径:{} ,错误原因:{}", file.getAbsolutePath(), e.getMessage());
            }
        }
    }

    public void deleteEmptyDir(String dirName) {
        File dir = new File(dirName);
        File[] files = dir.listFiles();
        for (File file : files) {
            if (file.isDirectory()) {
                if (file.list().length == 0) {
                    file.delete();
                } else {
                    deleteEmptyDir(file.getPath());
                    if (file.list().length == 0) {
                        file.delete();
                    }
                }
            }
        }
    }

}
