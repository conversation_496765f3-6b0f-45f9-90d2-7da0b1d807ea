package com.fxiaoke.document.convert.utils;

import com.fxiaoke.document.convert.domain.exception.BaseException;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.channels.Channels;
import java.nio.channels.FileChannel;
import java.nio.channels.ReadableByteChannel;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;

public class SimpFileUtil {

  private static final String FILE_SEPARATOR = "/";
  private static final String DATE_FORMAT = "yyyyMMdd";
  private static final String TIME_FORMAT = "HH";
  private static final int DEFAULT_EMPLOYEE_ID = 10000;
  private static final String MODULE = "SimpFileUtils";
  private static final long TRANSFER_SIZE = 1024 * 1024 * 150;

  /**
   * 创建一个临时文件路径
   * @param rootDir    根目录
   * @param ea         企业账号
   * @param employeeId 员工id
   * @param extension  文件后缀 (不带.例如：pdf)
   * @return           文件路径
   */
  public static Path createTempFile(String rootDir,String ea,int employeeId,String extension) {
    String yyyyMMdd = DataTimeFormatUtil.getCurrentDate(DATE_FORMAT);
    String dd = DataTimeFormatUtil.getCurrentDate("dd");
    String hh = DataTimeFormatUtil.getCurrentTime(TIME_FORMAT);
    String mm = DataTimeFormatUtil.getCurrentTime("mm");
    String fileName = SampleUUID.getUUID() + "." + extension;

    if (employeeId == 0) {
      employeeId = DEFAULT_EMPLOYEE_ID;
    }
    StringBuilder fileDirBuilder = new StringBuilder();
    fileDirBuilder.append(rootDir).append(FILE_SEPARATOR)
        .append(yyyyMMdd).append(FILE_SEPARATOR)
        .append(ea).append(FILE_SEPARATOR)
        .append(employeeId).append(FILE_SEPARATOR)
        .append(extension).append(FILE_SEPARATOR)
        .append(dd).append(FILE_SEPARATOR)
        .append(hh).append(FILE_SEPARATOR)
        .append(mm).append(FILE_SEPARATOR)
        .append(SampleUUID.getUUID());
    String fileDir = fileDirBuilder.toString();
    return Paths.get(fileDir, fileName);
  }

  /**
   * 创建文件及其父目录
   *
   * @param path 文件路径
   */
  public static void createFile(Path path) {
    try {
      Files.createDirectories(path.getParent());
      Files.createFile(path);
    } catch (IOException e) {
      throw new BaseException(e,"create file error,Lack of authority",500,MODULE+"-createFile",path);
    }
  }

  /**
   * 将文件保存到本地,最大支持150M
   *
   * @param path        保存路径
   * @param inputStream 文件流流
   */
  public static void saveFileToLocal(Path path, InputStream inputStream) {
    createFile(path);
    try (FileChannel tempChannel = FileChannel.open(path, StandardOpenOption.CREATE, StandardOpenOption.WRITE)) {
      try (ReadableByteChannel inputChannel = Channels.newChannel(inputStream)) {
        tempChannel.transferFrom(inputChannel, 0, TRANSFER_SIZE);
      }
    } catch (IOException e) {
      throw new BaseException(e,"create file error,Lack of authority",500,MODULE+"-saveObjectToLocal",path);
    }
  }

  /**
   * 将指定大小的文件保存到本地
   *
   * @param path        保存路径
   * @param inputStream 文件流
   * @param fileSize    文件大小
   */
  public static void saveFileToLocal(Path path, InputStream inputStream, long fileSize) {
    createFile(path);
    try (FileChannel tempChannel = FileChannel.open(path, StandardOpenOption.CREATE, StandardOpenOption.WRITE)) {
      try (ReadableByteChannel inputChannel = Channels.newChannel(inputStream)) {
        tempChannel.transferFrom(inputChannel, 0, fileSize);
      }
    } catch (IOException e) {
      throw new BaseException(e,"saveFileToLocal error",500,MODULE+"-saveObjectToLocal",path,fileSize);
    }
  }

  /**
   * 将指定大小的文件保存到本地
   *
   * @param path        保存路径
   * @param data        文件数据
   * @param fileSize    文件大小
   */
  public static void saveFileToLocal(Path path, byte[] data, long fileSize) {
    createFile(path);
    try(ByteArrayInputStream inputStream=new ByteArrayInputStream(data)){
      try (FileChannel tempChannel = FileChannel.open(path, StandardOpenOption.CREATE, StandardOpenOption.WRITE)) {
        try (ReadableByteChannel inputChannel = Channels.newChannel(inputStream)) {
          tempChannel.transferFrom(inputChannel, 0, fileSize);
        }
      }
    }
    catch (IOException e) {
      throw new BaseException(e,"saveFileToLocal error",500,MODULE+"-saveObjectToLocal",path,fileSize);
    }
  }

  public static long getLocalFileSize(Path filePath) {
    try {
      return Files.size(filePath);
    } catch (IOException e) {
      throw new BaseException(e,"getLocalFileSize error",500,MODULE+"-getLocalFileSize",filePath);
    }
  }
}
