package com.fxiaoke.document.convert.service;

import com.fxiaoke.document.convert.domain.model.FileClearEntry;
import java.io.IOException;
import java.nio.file.DirectoryNotEmptyException;
import java.nio.file.Files;
import java.nio.file.NoSuchFileException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.Instant;
import java.util.Comparator;
import java.util.concurrent.ConcurrentLinkedQueue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
@Slf4j(topic = "FileCleanupManager")
public class FileCleanupManager {

  private final ConcurrentLinkedQueue<FileClearEntry> cleanupQueue;
  private final Duration cleanupDelay;
  private final Duration maxExecutionTime;

  public FileCleanupManager() {
    cleanupQueue = new ConcurrentLinkedQueue<>();
    cleanupDelay = Duration.ofSeconds(300);
    maxExecutionTime = Duration.ofSeconds(300);
  }

  public void addFileToCleanup(Path file) {
    addFileToCleanup(file.toAbsolutePath().toString());
  }

  public void addFileToCleanup(String filePath) {
    if (filePath != null && !filePath.isEmpty()) {
      cleanupQueue.offer(new FileClearEntry(filePath));
      log.info("Add file to cleanup queue: {}", filePath);
    }
  }

  @Scheduled(cron = "${corn}")
  private void cleanupFiles() {
    log.info("Cleanup task start...");
    Instant taskStartTime = Instant.now();
    Instant now;
    int processedFiles = 0;
    while (!cleanupQueue.isEmpty()) {
      // 检查任务执行时间是否超过最大执行时间,防止任务执行时间过长导致多个任务同时执行
      now = Instant.now();
      if (Duration.between(taskStartTime, now).compareTo(maxExecutionTime) >= 0) {
        log.info("Cleanup task reached maximum execution time of 8 minutes. Exiting...");
        break;
      }
      // 取出队首元素,检查是否需要清理
      FileClearEntry entry = cleanupQueue.peek();
      if (entry != null
          && Duration.between(entry.getTimestamp(), now).compareTo(cleanupDelay) >= 0) {
        // 符合清理条件,从队列中移除
        cleanupQueue.poll();
        try {
          // 删除文件及文件夹
          deleteRecursively(Paths.get(entry.getFilePath()));
          log.info("File deleted success: {}", entry.getFilePath());
        } catch (NoSuchFileException e) {
          log.warn("File not found, could not delete: {}", entry.getFilePath());
        } catch (DirectoryNotEmptyException e) {
          log.warn("Directory is not empty, could not delete: {}", entry.getFilePath());
        } catch (Exception e) {
          log.error("Failed to delete file: {}. Error: {}", entry.getFilePath(), e.getMessage());
        }
        processedFiles++;
      }
    }
    log.info("Cleanup task end. Processed {} files.", processedFiles);
  }


  private void deleteRecursively(Path path) throws IOException {
    if (Files.isDirectory(path)) {
      try (var entries = Files.walk(path)) {
        // 先删除子文件和子目录，再删除父目录
        entries.sorted(Comparator.reverseOrder()) // 反向排序，确保子文件/目录先被删除
            .forEach(p -> {
              try {
                Files.delete(p);
                log.debug("Deleted: {}", p);
              } catch (NoSuchFileException e) {
                log.warn("File not found while deleting: {}", p);
              } catch (IOException e) {
                log.error("Error deleting {}: {}", p, e.getMessage());
              }
            });
      }
    } else {
      Files.delete(path);
    }
  }
}
