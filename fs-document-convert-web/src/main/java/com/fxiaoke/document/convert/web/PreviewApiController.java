package com.fxiaoke.document.convert.web;


import com.fxiaoke.document.convert.domain.Cause;
import com.fxiaoke.document.convert.domain.Result;
import com.fxiaoke.document.convert.domain.exception.BaseException;
import com.fxiaoke.document.convert.domain.exception.PreviewException;
import com.fxiaoke.document.convert.domain.preview.ConvertFormat;
import com.fxiaoke.document.convert.domain.preview.GetMateInfo;
import com.fxiaoke.document.convert.domain.preview.ToPngZip;
import com.fxiaoke.document.convert.domain.preview.UpgradeFormat;
import com.fxiaoke.document.convert.log.WebLogger;
import com.fxiaoke.document.convert.service.impl.ConvertFormatService;
import com.fxiaoke.document.convert.service.impl.MateInfoService;
import com.fxiaoke.document.convert.service.impl.ToPngZipService;
import com.fxiaoke.document.convert.service.impl.UpgradeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j(topic = "PreviewApiController")
@RestController
@RequestMapping("/preview")
public class PreviewApiController {

  UpgradeService upgradeService;
  MateInfoService mateInfoService;
  ToPngZipService toPngZipService;
  ConvertFormatService convertFormatService;
  public PreviewApiController(UpgradeService upgradeService, MateInfoService mateInfoService, ConvertFormatService convertFormatService, ToPngZipService toPngZipService) {
    this.upgradeService = upgradeService;
    this.mateInfoService = mateInfoService;
    this.toPngZipService = toPngZipService;
    this.convertFormatService = convertFormatService;
  }

  @WebLogger
  @PostMapping(value = "/getMateInfo")
  public Result<GetMateInfo.Result> getMateInfo(@Validated @RequestBody Cause<GetMateInfo.Arg> arg) {
    try {
      return Result.ok(mateInfoService.getMetaInfo(arg.getData().getFilePath(), arg.getData().getEa()));
    } catch (BaseException e) {
      throw new PreviewException(e);
    }
  }

  @WebLogger
  @PostMapping(value = "/upgradeFormat")
  public Result<UpgradeFormat.Result> upgradeFormat(@Validated @RequestBody Cause<UpgradeFormat.Arg> arg) {
    try {
      return Result.ok(upgradeService.upgrade(arg.getData().getFilePath()));
    } catch (BaseException e) {
      throw new PreviewException(e);
    }
  }

  @WebLogger
  @PostMapping(value = "/convertFormat/one")
  public Result<ConvertFormat.Result> convertFormatOne(@Validated @RequestBody Cause<ConvertFormat.OneArg> arg) {
    try {
      return Result.ok(convertFormatService.convertFormat(arg.getData()));
    } catch (BaseException e) {
      throw new PreviewException(e);
    }
  }

  @WebLogger
  @PostMapping(value = "/convertFormat/range")
  public Result<ConvertFormat.Result> convertFormatRange(@Validated @RequestBody Cause<ConvertFormat.RangeArg> arg) {
    try {
      return Result.ok(convertFormatService.convertFormat(arg.getData()));
    } catch (BaseException e) {
      throw new PreviewException(e);
    }
  }

  @WebLogger
  @PostMapping(value = "/convertFormat/all")
  public Result<ConvertFormat.Result> convertFormat(@Validated @RequestBody Cause<ConvertFormat.AllArg> arg) {
    try {
      return Result.ok(convertFormatService.convertFormat(arg.getData()));
    } catch (BaseException e) {
      throw new PreviewException(e);
    }
  }

  @WebLogger
  @PostMapping("/toPngZip")
  public Result<ToPngZip.Result> toPngZip(@Validated @RequestBody Cause<ToPngZip.Arg> arg) {
    try {
      return Result.ok(toPngZipService.toPngZip(arg.getData().getFilePath()));
    } catch (BaseException e) {
      throw new PreviewException(e);
    }
  }

}
