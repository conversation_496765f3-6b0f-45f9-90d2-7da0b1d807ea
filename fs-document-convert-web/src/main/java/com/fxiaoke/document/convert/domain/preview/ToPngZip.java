package com.fxiaoke.document.convert.domain.preview;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

public interface ToPngZip {

  @Data
  @EqualsAndHashCode(callSuper = true)
  @ToString(callSuper = true)
  class Arg extends BaseArg {
  }

  @Data
  @ToString
  class Result{
    String filePath;
    public Result(String filePath){
      this.filePath = filePath;
    }
    public static Result of(String filePath){
      return new Result(filePath);
    }
  }
}
