package com.fxiaoke.document.convert.domain.model;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
/**
 * 页面二维码参数
 * PDF使用点（point）作为标准单位，1点 = 1/72英寸
 * 如果在遍历文档时,存在有文档页码未定义二维码,则使用默认二维码
 * 如果既不存在默认二维码,也不存在指定了文档页码的二维码,则该页不添加二维码
 */
@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class PageQRCode {
  // x坐标
  @NotNull(message = "x Coordinate不能为空")
  @Min(value = 0, message = "x Coordinate不能小于0")
  private Integer x;
  // y坐标
  @NotNull(message = "y Coordinate不能为空")
  @Min(value = 0, message = "y Coordinate不能小于0")
  private Integer y;

  // 文档页码
  @NotNull(message = "pageNumber不能为空")
  @Min(value = 1, message = "pageNumber不能小于1")
  private Integer pageNumber;

  // 是否默认二维码
  private boolean defaultQRCode;

  // 二维码参数（使用QRCodeUtil生成二维码）

  @Valid
  @NotNull(message = "qrCodeParams不能为空")
  private QrCodeParams qrCodeParams;
}
