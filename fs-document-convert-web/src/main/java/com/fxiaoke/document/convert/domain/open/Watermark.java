package com.fxiaoke.document.convert.domain.open;

import java.util.List;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.ToString;

public interface Watermark {

  @Data
  @ToString
  class Arg {

    @NotBlank(message = "ea不能为空")
    String ea;

    @NotNull(message = "employeeId不能为空")
    Integer employeeId;

    @NotBlank(message = "npath不能为空")
    @Pattern(
        regexp = "^(N_|TN_|C_|TC_).{0,48}$",
        message = "Invalid path"
    )
    String path;

    @NotBlank(message = "fileName不能为空")
    @Size(max = 120, message = "fileName长度不能超过120")
    String fileName;

    @Pattern(regexp = "^(pdf)$", message = "extension只能是pdf")
    String extension;

    @NotNull
    @Size(min = 1, max = 12, message = "watermarkTexts长度必须在1-12之间")
    private List<String> watermarkTexts;

    @NotBlank(message = "ownerPassword不能为空")
    private String ownerPassword;

    private String accessPassword;

    @Min(value = 5, message = "fontSize不能小于5")
    @Max(value = 38, message = "fontSize不能超过38")
    private int fontSize;

    @DecimalMin(value = "0.1", message = "opacity不能小于0.1")
    @DecimalMax(value = "1.0", message = "opacity不能大于1")
    @Digits(integer = 1, fraction = 1, message = "opacity最多支持1位小数")
    private float opacity;

    private boolean addTimeWatermark;
    private boolean random;
    private boolean topToBottom;
  }


  @Data
  @ToString
  class Result{
    long size;
    String path;
    String extension;

    public static Result of(Long size, String path, String extensionName) {
      Result result = new Result();
      result.setSize(size);
      result.setPath(path);
      result.setExtension(extensionName);
      return result;
    }
  }
}
