package com.fxiaoke.document.convert.exception;

import com.fxiaoke.document.convert.domain.Result;
import com.fxiaoke.document.convert.domain.exception.BaseException;
import com.fxiaoke.document.convert.domain.exception.PreviewException;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@Slf4j(topic = "GlobalExceptionHandler")
@RestControllerAdvice
public class GlobalExceptionHandler {

  @ExceptionHandler(value = Exception.class)
  public Result<String> handler(HttpServletResponse response, Exception e) {
    logError(e.getMessage(), e);
    response.setStatus(500);
    return Result.error(500,"An unknown exception has occurred. Please contact the administrator");
  }

  @ExceptionHandler(value = MissingServletRequestParameterException.class)
  public Result<String> handler(HttpServletResponse response, MissingServletRequestParameterException e) {
    logWarn(e.getMessage(),e);
    response.setStatus(400);
    return Result.error(400, e.getMessage());
  }

  @ExceptionHandler(value = IllegalArgumentException.class)
  public Result<String> handler(HttpServletResponse response, IllegalArgumentException e) {
    logWarn(e.getMessage(), e);
    response.setStatus(400);
    return Result.error(400, e.getMessage());
  }

  @ExceptionHandler(value = ConstraintViolationException.class)
  public Result<String> handler(HttpServletResponse response, ConstraintViolationException e) {
    logWarn(e.getMessage(),e);
    response.setStatus(400);
    return Result.error(400, e.getMessage());
  }

  @ExceptionHandler(value = MethodArgumentNotValidException.class)
  public Result<String> handler(HttpServletResponse response, MethodArgumentNotValidException e) {
    logWarn(e.getMessage(), e);
    response.setStatus(400);
    return Result.error(400, Objects.requireNonNull(e.getFieldError()).getDefaultMessage());
  }

  @ExceptionHandler(value = HttpMessageNotReadableException.class)
  public Result<String> handler(HttpServletResponse response, HttpMessageNotReadableException e) {
    logWarn(e.getMessage(), e);
    response.setStatus(400);
    return Result.error(400, "json parse error");
  }

  @ExceptionHandler(value = BaseException.class)
  public Result<String> handler(HttpServletResponse response, BaseException e) {
    logWarn(e.getMessage(), e);
    response.setStatus(400);
    return Result.error(400, e.getMessage());
  }

  @ExceptionHandler(value = PreviewException.class)
  public Result<String> handler(HttpServletResponse response, PreviewException e) {
    if (e.getCode() == 400) {
      logWarn(e.getMessage(), e);
    } else {
      logError(e.getMessage(), e);
    }
    response.setStatus(e.getCode());
    return Result.error(e.getCode(), e.getMessage());
  }

  private void logWarn(String message, Exception e) {
    log.warn("Client exception: {}",message,e);
  }

  private void logError(String message, Exception e) {
    log.error("Server exception: {}",message,e);
  }
}
