package com.fxiaoke.document.convert.domain.model;

import com.fxiaoke.document.convert.domain.constants.AudioConvertTypeEnum;
import lombok.*;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class AudioConvertArg {

  @NotBlank(message = "ea is required")
  private String ea;

  private Integer employeeId;
  private String securityGroup;
  @NotBlank
  @Pattern(
      regexp = "^(N_|TN_|C_|TC_|TA_|A_|G_).{0,48}$",
      message = "Invalid path"
  )
  private String path;

  @NotBlank(message = "sourceType is required")
  @Pattern(
      regexp = "^(mp3|wav|amr|opus)$",
      message = "Invalid sourceType"
  )
  private String sourceType;
  @Pattern(
      regexp = "^(mp3|wav)$",
      message = "Invalid targetType"
  )
  private String targetType;

  @AssertTrue(message = "Unsupported file conversion types")
  public boolean verify(){
    AudioConvertTypeEnum audioConvertTypeEnum = AudioConvertTypeEnum.of(sourceType, targetType);
    return audioConvertTypeEnum!=AudioConvertTypeEnum.OTHER;
  }
}
