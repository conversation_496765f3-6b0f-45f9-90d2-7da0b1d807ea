package com.fxiaoke.document.convert.domain.open;

import com.fxiaoke.document.convert.domain.model.PageQRCode;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

public interface DocPageQRCode {

  @Data
  @ToString
  @EqualsAndHashCode
  @NoArgsConstructor
  @AllArgsConstructor
  class Arg {

    @NotBlank(message = "ea不能为空")
    String ea;

    @NotNull(message = "employeeId不能为空")
    Integer employeeId;

    @NotBlank(message = "npath不能为空")
    @Pattern(
        regexp = "^(N_|TN_|C_|TC_).{0,48}$",
        message = "Invalid path"
    )
    String path;

    @NotBlank(message = "fileName不能为空")
    @Size(max = 120, message = "fileName长度不能超过120")
    String fileName;

    @Pattern(regexp = "^(doc|docx|pdf)$", message = "extension只能是doc、docx、pdf")
    String extension;

    @Valid
    @NotNull(message = "pageQRCodes不能为空")
    @Size(min = 1, max = 10, message = "pageQRCodes的数量必须小于10")
    private List<PageQRCode> pageQRCodes;

    // 文档修改密码
    @NotBlank(message = "ownerPassword不能为空")
    private String ownerPassword;

    // 文档打开密码
    private String accessPassword;
  }

  @Data
  @ToString
  class Result{
    long size;
    String path;
    String extension;

    public static Result of(Long size, String path, String extensionName) {
      Result result = new Result();
      result.setSize(size);
      result.setPath(path);
      result.setExtension(extensionName);
      return result;
    }
  }

}
