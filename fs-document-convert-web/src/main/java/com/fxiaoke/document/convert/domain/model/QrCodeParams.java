package com.fxiaoke.document.convert.domain.model;

import com.fxiaoke.document.convert.utils.ImageBaseUtils;
import java.util.Base64;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class QrCodeParams {

  /**
   * 二维码内容
   */

  @NotBlank(message = "The content of the QR code cannot be empty")
  @Size(max = 1500, message = "The content of the QR code cannot exceed 1500 characters")
  private String content;

  /**
   * 二维码尺寸（点，1点 = 1/72英寸）
   */
  @NotNull(message = "The size of the QR code cannot be null")
  @Min(value = 30, message = "The size of the QR code cannot be less than 30 pixels")
  @Max(value = 1000, message = "The size of the QR code cannot exceed 1000 pixels")
  private Integer size;

  /**
   * 纠错级别
   */
  private CorrectionLevel correctionLevel;

  /**
   * Logo的Base64编码
   */
  @Size(max = 66560 , message = "The size of the logo cannot exceed 66560")
  private String logoBase64;


  public void setCorrectionLevel(String correctionLevel) {
    this.correctionLevel = CorrectionLevel.of(correctionLevel);
  }

  /**
   * 验证二维码内容是否符合标准
   */
  @AssertTrue(message = "The content, error correction level and size of the QR code do not meet the standards")
  public boolean isContentValid() {
    if (content == null || content.isEmpty()) {
      return false;
    }

    // 根据纠错级别计算最大字符数
    int maxLength = calculateMaxContentLength();
    return content.length() <= maxLength;
  }

  /**
   * 验证Logo是否符合标准
   */

  @AssertTrue(message = "The size of the logo does not match the error correction level and QR code size")
  public boolean isLogoValid() {
    if (logoBase64 == null || logoBase64.isEmpty()) {
      return true; // Logo是可选的
    }

    double maxLogoSideLength = size * correctionLevel.getLevel();
    double maxLogoPixel = maxLogoSideLength * maxLogoSideLength;
    try {
      // 解码Base64并验证图片大小
      byte[] logoBytes = Base64.getDecoder().decode(logoBase64);
      ImageExifInfo imageDimensions = ImageBaseUtils.getImageDimensions(logoBytes);
      int width = imageDimensions.getWidth();
      int height = imageDimensions.getHeight();
      // Logo大小不应超过在当前纠错级别下最大Logo大小
      return width * height < maxLogoPixel;
    } catch (Exception e) {
      return false;
    }
  }

  /**
   * 根据纠错级别计算最大内容长度
   */
  private int calculateMaxContentLength() {
    // 根据二维码尺寸计算版本号（N×N模块）
    // 推荐图片大小为（N × 4）x（N × 4）像素
    int modules = size / 4; // 从像素尺寸反推模块数

    // 根据模块数确定版本号（1-40）
    int version = 1;
    if (modules >= 21) {
      version = (modules - 21) / 4 + 1;
      if (version > 40) {
        version = 40; // 最大版本为40
      }
    }

    // 根据版本号和纠错级别确定最大字符数
    // 这里使用混合数据类型（Data bits (mixed)）作为基准
    return getMaxLengthForVersionAndLevel(version, correctionLevel);
  }

  private record ContentIndex(int levelIndex, int rowIndex, int colIndex) {}

  private ContentIndex getContentIndex(int version, CorrectionLevel level){
    // 纠错级别对应的索引
    int levelIndex = switch (level) {
      case L -> 0;
      case M -> 1;
      case Q -> 2;
      case H -> 3;
    };

    // 计算数组索引
    int rowIndex = (version - 1) / 10;
    int colIndex = (version - 1) % 10;

    return new ContentIndex(levelIndex, rowIndex, colIndex);
  }


  /**
   * 根据版本号和纠错级别获取最大字符数
   */
  private int getMaxLengthForVersionAndLevel(int version, CorrectionLevel level) {
    if (version < 1 || version > 40) {
      return 152; // 默认值，版本1-L
    }

    // 版本1-40的混合数据类型最大字符数，按纠错级别分组
    int[][][] maxLengths = {
        // L级别 (0)
        {
            {152, 272, 440, 640, 864, 1088, 1248, 1552, 1856, 2192},
            {2592, 2960, 3424, 3688, 4184, 4712, 5176, 5768, 6360, 6888},
            {7456, 8048, 8752, 9392, 10208, 10960, 11744, 12248, 13048, 13880},
            {14744, 15640, 16568, 17528, 18448, 19472, 20528, 21616, 22496, 23648}
        },
        // M级别 (1)
        {
            {128, 224, 352, 512, 688, 864, 992, 1232, 1456, 1728},
            {2032, 2320, 2672, 2920, 3320, 3624, 4056, 4504, 5016, 5352},
            {5712, 6256, 6880, 7312, 8000, 8496, 9024, 9544, 10136, 10984},
            {11640, 12328, 13048, 13800, 14496, 15312, 15936, 16816, 17728, 18672}
        },
        // Q级别 (2)
        {
            {104, 176, 272, 384, 496, 608, 704, 880, 1056, 1232},
            {1440, 1648, 1952, 2088, 2360, 2600, 2936, 3176, 3568, 3848},
            {4096, 4544, 4912, 5312, 5744, 6032, 6464, 6968, 7288, 7880},
            {8264, 8920, 9368, 9848, 10288, 10832, 11408, 12016, 12656, 13328}
        },
        // H级别 (3)
        {
            {72, 128, 208, 288, 368, 480, 528, 688, 800, 976},
            {1120, 1264, 1440, 1576, 1784, 2024, 2264, 2504, 2728, 3088},
            {3248, 3536, 3712, 4112, 4304, 4768, 5024, 5288, 5608, 5960},
            {6344, 6760, 7208, 7688, 7888, 8432, 8768, 9136, 9776, 10208}
        }
    };

    ContentIndex contentIndex = getContentIndex(version, level);

    // 返回对应的最大字符数
    return maxLengths[contentIndex.levelIndex][contentIndex.rowIndex][contentIndex.colIndex];
  }

  /**
   * 获取当前版本号
   */
  public int getVersion() {
    int modules = size / 4; // 从像素尺寸反推模块数

    // 根据模块数确定版本号（1-40）
    int version = 1;
    if (modules >= 21) {
      version = (modules - 21) / 4 + 1;
      if (version > 40) {
        version = 40; // 最大版本为40
      }
    }

    return version;
  }

  /**
   * 获取当前模块数
   */
  public int getModules() {
    return size / 4;
  }

  /**
   * 获取推荐图片尺寸
   */
  public int getRecommendedSize() {
    int modules = getModules();
    return modules * 4;
  }

  /**
   * 根据数据类型获取最大字符数
   *
   * @param dataType 数据类型：0-混合，1-数字，2-字母数字，3-二进制，4-日文汉字
   */
  public int getMaxLengthByDataType(int dataType) {
    int version = getVersion();
    ContentIndex contentIndex = getContentIndex(version, CorrectionLevel.Q);

    // 根据数据类型返回对应的最大字符数
    return switch (dataType) {
      case 0 -> // 混合
          getMaxLengthForVersionAndLevel(version, correctionLevel);
      case 1 -> // 数字
          getNumericMaxLength(contentIndex.levelIndex, contentIndex.rowIndex, contentIndex.colIndex);
      case 2 -> // 字母数字
          getAlphanumericMaxLength(contentIndex.levelIndex, contentIndex.rowIndex, contentIndex.colIndex);
      case 3 -> // 二进制
          getBinaryMaxLength(contentIndex.levelIndex, contentIndex.rowIndex, contentIndex.colIndex);
      case 4 -> // 日文汉字
          getKanjiMaxLength(contentIndex.levelIndex, contentIndex.rowIndex, contentIndex.colIndex);
      default -> getMaxLengthForVersionAndLevel(version, correctionLevel);
    };
  }

  /**
   * 获取数字编码的最大字符数
   */
  private int getNumericMaxLength(int levelIndex, int rowIndex, int colIndex) {
    int[][][] maxLengths = {
        // L级别 (0)
        {
            {41, 77, 127, 187, 255, 322, 370, 461, 552, 652},
            {772, 883, 1022, 1101, 1250, 1408, 1548, 1725, 1903, 2061},
            {2232, 2409, 2609, 2812, 3005, 3186, 3391, 3589, 3791, 3993},
            {4236, 4456, 4686, 4916, 5134, 5358, 5596, 5846, 6092, 6340}
        },
        // M级别 (1)
        {
            {34, 63, 101, 149, 202, 255, 293, 365, 432, 513},
            {604, 691, 796, 871, 991, 1082, 1212, 1346, 1500, 1600},
            {1708, 1872, 2059, 2188, 2354, 2546, 2701, 2857, 3005, 3185},
            {3339, 3509, 3674, 3840, 4009, 4218, 4420, 4622, 4826, 5034}
        },
        // Q级别 (2)
        {
            {27, 48, 77, 111, 144, 178, 207, 259, 312, 364},
            {427, 489, 580, 621, 703, 775, 876, 948, 1063, 1159},
            {1224, 1358, 1468, 1588, 1718, 1802, 1933, 2085, 2181, 2358},
            {2473, 2670, 2805, 2949, 3081, 3244, 3407, 3571, 3739, 3909}
        },
        // H级别 (3)
        {
            {17, 34, 58, 82, 106, 139, 154, 202, 235, 288},
            {331, 374, 427, 468, 530, 602, 674, 746, 813, 919},
            {969, 1056, 1108, 1228, 1286, 1425, 1501, 1581, 1737, 1800},
            {1869, 1990, 2102, 2218, 2334, 2450, 2571, 2692, 2809, 2953}
        }
    };

    return maxLengths[levelIndex][rowIndex][colIndex];
  }

  /**
   * 获取字母数字编码的最大字符数
   */
  private int getAlphanumericMaxLength(int levelIndex, int rowIndex, int colIndex) {
    int[][][] maxLengths = {
        // L级别 (0)
        {
            {25, 47, 77, 114, 154, 195, 224, 279, 335, 395},
            {468, 535, 619, 667, 758, 854, 938, 1046, 1153, 1249},
            {1352, 1460, 1588, 1704, 1853, 1990, 2132, 2223, 2369, 2506},
            {2632, 2780, 2894, 3054, 3202, 3334, 3486, 3627, 3791, 3993}
        },
        // M级别 (1)
        {
            {20, 38, 61, 90, 122, 154, 178, 221, 262, 311},
            {367, 419, 483, 528, 600, 656, 734, 816, 909, 970},
            {1035, 1134, 1248, 1326, 1451, 1542, 1637, 1732, 1839, 1994},
            {2113, 2238, 2369, 2506, 2632, 2780, 2894, 3054, 3202, 3334}
        },
        // Q级别 (2)
        {
            {16, 29, 47, 67, 87, 108, 125, 157, 189, 221},
            {259, 296, 352, 376, 426, 470, 531, 574, 644, 702},
            {742, 823, 890, 963, 1041, 1094, 1172, 1263, 1322, 1429},
            {1499, 1618, 1700, 1787, 1867, 1966, 2071, 2181, 2298, 2420}
        },
        // H级别 (3)
        {
            {10, 20, 35, 50, 64, 84, 93, 122, 143, 174},
            {200, 227, 259, 283, 321, 365, 408, 452, 493, 557},
            {587, 640, 672, 744, 779, 883, 958, 1016, 1080, 1150},
            {1224, 1298, 1362, 1435, 1509, 1581, 1666, 1754, 1844, 1940}
        }
    };

    return maxLengths[levelIndex][rowIndex][colIndex];
  }

  /**
   * 获取二进制编码的最大字符数
   */
  private int getBinaryMaxLength(int levelIndex, int rowIndex, int colIndex) {
    int[][][] maxLengths = {
        // L级别 (0)
        {
            {17, 32, 53, 78, 106, 134, 154, 192, 230, 271},
            {321, 367, 425, 458, 520, 586, 644, 718, 792, 858},
            {929, 1003, 1091, 1171, 1273, 1367, 1465, 1528, 1628, 1732},
            {1840, 1952, 2068, 2188, 2308, 2432, 2560, 2692, 2828, 2968}
        },
        // M级别 (1)
        {
            {14, 26, 42, 62, 84, 106, 122, 152, 180, 213},
            {251, 287, 331, 362, 412, 450, 504, 560, 624, 666},
            {711, 779, 857, 911, 997, 1059, 1125, 1190, 1264, 1370},
            {1452, 1538, 1628, 1732, 1840, 1952, 2068, 2188, 2308, 2432}
        },
        // Q级别 (2)
        {
            {11, 20, 32, 46, 60, 74, 86, 108, 130, 151},
            {177, 203, 241, 258, 292, 322, 364, 394, 442, 482},
            {509, 565, 611, 661, 715, 751, 805, 868, 908, 982},
            {1030, 1112, 1168, 1228, 1283, 1351, 1423, 1499, 1579, 1663}
        },
        // H级别 (3)
        {
            {7, 14, 24, 34, 44, 58, 64, 84, 98, 119},
            {137, 155, 177, 194, 220, 250, 280, 310, 338, 382},
            {403, 439, 461, 511, 535, 593, 625, 658, 698, 742},
            {790, 842, 898, 958, 1018, 1082, 1150, 1222, 1298, 1378}
        }
    };

    return maxLengths[levelIndex][rowIndex][colIndex];
  }

  /**
   * 获取日文汉字编码的最大字符数
   */
  private int getKanjiMaxLength(int levelIndex, int rowIndex, int colIndex) {
    int[][][] maxLengths = {
        // L级别 (0)
        {
            {10, 20, 32, 48, 65, 82, 95, 118, 141, 167},
            {198, 226, 262, 282, 320, 361, 397, 442, 488, 528},
            {572, 618, 672, 721, 784, 842, 902, 940, 1002, 1066},
            {1132, 1200, 1270, 1342, 1417, 1494, 1573, 1654, 1737, 1822}
        },
        // M级别 (1)
        {
            {8, 16, 26, 38, 52, 65, 75, 93, 111, 131},
            {155, 177, 204, 223, 254, 277, 310, 345, 384, 410},
            {438, 480, 528, 561, 614, 652, 692, 732, 774, 830},
            {871, 911, 985, 1033, 1115, 1174, 1236, 1300, 1366, 1434}
        },
        // Q级别 (2)
        {
            {7, 12, 20, 28, 37, 45, 53, 66, 79, 93},
            {109, 125, 149, 159, 180, 198, 224, 248, 276, 297},
            {315, 348, 376, 407, 442, 468, 496, 528, 560, 614},
            {645, 677, 713, 751, 791, 833, 877, 923, 971, 1021}
        },
        // H级别 (3)
        {
            {4, 8, 15, 21, 27, 36, 39, 52, 60, 74},
            {85, 96, 109, 120, 136, 154, 173, 191, 208, 235},
            {248, 270, 284, 315, 330, 365, 385, 405, 430, 457},
            {486, 518, 553, 590, 625, 658, 694, 733, 774, 817}
        }
    };

    return maxLengths[levelIndex][rowIndex][colIndex];
  }
}