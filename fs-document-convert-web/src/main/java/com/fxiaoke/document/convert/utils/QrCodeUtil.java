package com.fxiaoke.document.convert.utils;

import com.fxiaoke.document.convert.domain.exception.BaseException;
import com.fxiaoke.document.convert.domain.model.CorrectionLevel;
import com.fxiaoke.document.convert.domain.model.ImageExifInfo;
import com.fxiaoke.document.convert.domain.model.QrCodeParams;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.EnumMap;
import java.util.Map;
import javax.imageio.ImageIO;

/**
 * 二维码生成工具类
 */
public class QrCodeUtil {

  private static final String MODULE = "QrCodeUtil";

  private QrCodeUtil() {
    // 私有构造函数，禁止实例化
  }

  /**
   * 生成二维码
   *
   * @param params 二维码参数
   * @return 二维码图片的字节数组
   * @throws BaseException  如果处理图片失败
   */
  public static byte[] generateQrCode(QrCodeParams params) throws BaseException {

    // 设置二维码参数
    Map<EncodeHintType, Object> hints = new EnumMap<>(EncodeHintType.class);
    hints.put(EncodeHintType.ERROR_CORRECTION, convertErrorCorrectionLevel(params.getCorrectionLevel()));
    hints.put(EncodeHintType.CHARACTER_SET, StandardCharsets.UTF_8);
    hints.put(EncodeHintType.MARGIN, 1);

    try {
      // 创建二维码
      QRCodeWriter qrCodeWriter = new QRCodeWriter();
      BitMatrix bitMatrix = qrCodeWriter.encode(params.getContent(), BarcodeFormat.QR_CODE,
          params.getSize(), params.getSize(), hints);

      // 直接创建最终的BufferedImage，使用ARGB类型以支持颜色和透明度
      BufferedImage qrImage = new BufferedImage(params.getSize(), params.getSize(),
          BufferedImage.TYPE_INT_ARGB);

      // 创建Graphics2D对象并设置渲染提示
      Graphics2D graphics = qrImage.createGraphics();
      graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
      graphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION,
          RenderingHints.VALUE_INTERPOLATION_BILINEAR);
      graphics.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
      graphics.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL,
          RenderingHints.VALUE_STROKE_PURE);
      graphics.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING,
          RenderingHints.VALUE_COLOR_RENDER_QUALITY);

      // 将二维码绘制到图像上
      for (int x = 0; x < params.getSize(); x++) {
        for (int y = 0; y < params.getSize(); y++) {
          qrImage.setRGB(x, y, bitMatrix.get(x, y) ? 0xFF000000 : 0xFFFFFFFF);
        }
      }

      // 如果有Logo，直接添加到二维码上
      if (params.getLogoBase64() != null && !params.getLogoBase64().isEmpty()) {
        // 解码Logo
        byte[] logoBytes = Base64.getDecoder().decode(params.getLogoBase64());
        ImageExifInfo imageDimensions = ImageBaseUtils.getImageDimensions(logoBytes);
        BufferedImage logoImage = ImageIO.read(new ByteArrayInputStream(logoBytes));

        // 计算Logo大小和位置
        int logoWidth = imageDimensions.getWidth();
        int logoHeight = imageDimensions.getHeight();
        int x = (params.getSize() - logoWidth) / 2;
        int y = (params.getSize() - logoHeight) / 2;

        // 创建白色背景
        graphics.setColor(Color.WHITE);
        graphics.fillRoundRect(x - 5, y - 5, logoWidth + 10, logoHeight + 10, 10, 10);

        // 绘制Logo
        graphics.drawImage(logoImage, x, y, logoWidth, logoHeight, null);
      }

      // 释放资源
      graphics.dispose();

      // 转换为字节数组
      try (ByteArrayOutputStream stream = new ByteArrayOutputStream()) {
        ImageIO.write(qrImage, "png", stream);
        return stream.toByteArray();
      }
    } catch (Exception e) {
      throw new BaseException(e, 400, MODULE + ".generateQrCode", params);
    }
  }

  /**
   * 生成二维码并返回Base64编码
   *
   * @param params 二维码参数
   * @return Base64编码的二维码图片
   * @throws WriterException 如果生成二维码失败
   * @throws IOException     如果处理图片失败
   */
  public static String generateQrCodeBase64(QrCodeParams params)
      throws WriterException, IOException {
    byte[] imageBytes = generateQrCode(params);
    return Base64.getEncoder().encodeToString(imageBytes);
  }

  /**
   * 将ErrorCorrectionLevel转换为ZXing的ErrorCorrectionLevel
   */
  private static ErrorCorrectionLevel convertErrorCorrectionLevel(CorrectionLevel level) {
    return switch (level) {
      case L -> ErrorCorrectionLevel.L;
      case M -> ErrorCorrectionLevel.M;
      case Q -> ErrorCorrectionLevel.Q;
      case H -> ErrorCorrectionLevel.H;
    };
  }
} 