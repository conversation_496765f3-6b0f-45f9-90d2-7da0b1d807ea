package com.fxiaoke.document.convert.domain.open;

import java.util.Collections;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

public interface OfficeToPdf {
  @Data
  @EqualsAndHashCode(callSuper = true)
  @ToString(callSuper = true)
  class Result extends BaseResult{
    public Result(String ea,int employeeId,List<String> npaths,int pageCount,String fileType){
      this.setEa(ea);
      this.setEmployeeId(employeeId);
      this.setNpaths(npaths);
      this.setPageCount(pageCount);
      this.setFileType(fileType);
    }

    public static Result of(String ea,int employeeId,String path,int pageCount,String fileType){
      return new Result(ea,employeeId, Collections.singletonList(path),pageCount,fileType);
    }

    public static Result of(String ea,int employeeId,List<String> npaths,int pageCount,String fileType){
      return new Result(ea,employeeId,npaths,pageCount,fileType);
    }
  }
}
