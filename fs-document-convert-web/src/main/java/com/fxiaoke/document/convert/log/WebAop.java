package com.fxiaoke.document.convert.log;

import com.fxiaoke.document.convert.domain.Cause;
import com.fxiaoke.document.convert.domain.open.MergerOffice;
import com.fxiaoke.document.convert.domain.preview.BaseArg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

@Slf4j(topic = "WebLogger")
@Aspect
@Component
public class WebAop {

  @Pointcut(value ="@annotation(com.fxiaoke.document.convert.log.WebLogger)&&args(cause)",argNames = "cause")
  public void previewTimeWait(Cause<BaseArg> cause) {
  }
  @Around(value = "previewTimeWait(cause)", argNames = "joinPoint,cause")
  public Object previewTimeWait(ProceedingJoinPoint joinPoint,Cause<BaseArg> cause) throws Throwable {
    Object arg = cause.getData();
    StopWatch watch = new StopWatch();
    watch.start();
    String methodName = joinPoint.getSignature().getName();
    log.info("Start ,Method:{},arg={}", methodName,arg);
    Object object;
    object = joinPoint.proceed();
    watch.stop();
    log.info("End ,Method:{},arg={},cost={}", methodName,arg, watch.getTime());
    return object;
  }

  @Around("@annotation(com.fxiaoke.document.convert.log.OpenGetWebLogger)")
  public Object openGetTimeWait(ProceedingJoinPoint joinPoint) throws Throwable {
    StopWatch watch = new StopWatch();
    watch.start();
    String methodName = joinPoint.getSignature().getName();
    Object[] args = joinPoint.getArgs();
    log.info("Start ,Method:{},arg={}", methodName,args);
    Object object;
    object = joinPoint.proceed();
    watch.stop();
    log.info("End ,Method:{},arg={},cost={}", methodName,args, watch.getTime());
    return object;
  }
  @Pointcut(value ="@annotation(com.fxiaoke.document.convert.log.OpenPostWebLogger)&&args(arg)",argNames = "arg")
  public void mergerOfficeTimeWait(MergerOffice.Arg arg) {
  }
  @Around(value = "mergerOfficeTimeWait(arg)", argNames = "joinPoint,arg")
  public Object mergerOfficeTimeWait(ProceedingJoinPoint joinPoint,MergerOffice.Arg arg) throws Throwable {
    StopWatch watch = new StopWatch();
    watch.start();
    String methodName = joinPoint.getSignature().getName();
    log.info("Start ,Method:{},arg:{}", methodName,arg);
    Object object;
    object = joinPoint.proceed();
    watch.stop();
    log.info("End ,Method:{},arg={},cost={}", methodName,arg, watch.getTime());
    return object;
  }







}
