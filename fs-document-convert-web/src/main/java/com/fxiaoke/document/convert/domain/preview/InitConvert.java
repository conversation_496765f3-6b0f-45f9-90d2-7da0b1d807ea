package com.fxiaoke.document.convert.domain.preview;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

public interface InitConvert {

  @Data
  @ToString(callSuper = true)
  @EqualsAndHashCode(callSuper = true)
  class Arg extends BaseArg{
  }

  @Getter
  @ToString
  class Result{
    private final int pageCount;
    private final String upgradeFormatFilePath;
    private final List<String> sheetNames;
    private final List<String> convertFilePaths;

    public Result(int pageCount,String upgradeFormatFilePath,List<String> convertFilePaths){
      this.pageCount = pageCount;
      this.upgradeFormatFilePath = upgradeFormatFilePath;
      this.convertFilePaths = convertFilePaths;
      this.sheetNames = new ArrayList<>();
    }

    public Result(int pageCount,List<String> sheetNames,String upgradeFormatFilePath, List<String> convertFilePaths) {
      this.pageCount = pageCount;
      this.sheetNames = sheetNames;
      this.upgradeFormatFilePath = upgradeFormatFilePath;
      this.convertFilePaths = convertFilePaths;
    }

    public static Result of(int pageCount,String upgradeFormatFilePath,List<String> convertFilePaths){
      return new Result(pageCount,upgradeFormatFilePath,convertFilePaths);
    }

    public static Result of(int pageCount, List<String> sheetNames,String upgradeFormatFilePath, List<String> convertFilePaths){
      return new Result(pageCount,sheetNames,upgradeFormatFilePath,convertFilePaths);
    }
  }


}
