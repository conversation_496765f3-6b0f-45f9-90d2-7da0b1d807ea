package com.fxiaoke.document.convert.dao;

import com.fxiaoke.document.convert.domain.preview.entity.PreviewInfo;

import java.util.Date;
import java.util.List;

public interface IPreviewInfoDao {

  /**
   * 将渲染完成的页码更新到预览信息中
   *
   * @param ea       企业账号
   * @param path     文件Path
   * @param filePath 文件在缓存盘中的路径
   */
  void updateFilePathList(String ea, String path, String filePath);

  void updatePageCount(String ea, String path, int pageCount);

  /**
   * 将Excel文件的sheet名称、页数信息、当前活动的sheet索引更新到预览信息中
   *
   * @param ea         企业账号
   * @param path       文件Path
   * @param sheetNames Excel活动工作表的索引
   */
  void updateSheetNamesAndPageCount(String ea, String path, List<String> sheetNames, int pageCount);

  void updateFilePath(String ea, String path, String filePath);

  /**
   * 根据path查询预览信息
   *
   * @param ea   企业账号
   * @param path 文件Path
   * @return 预览信息
   */
  PreviewInfo queryByPath(String ea, String path);

  void delete(String ea, String path);

  void save(String ea, String path, long employeeId, String filePath, long size, int pageCount,
      List<String> sheetNames);

  public void clean(List<String> pathList);

  public List<PreviewInfo> getPreviewInfoByPageV2(int limit, Date maxDate);

  PreviewInfo queryByDataDir(String dataDir);
}
