package com.fxiaoke.document.convert.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class ImageExifInfo {
  // 图片水平像素数
  private int width;
  // 图片垂直像素数
  private int height;
  // 图片类型 如 png
  private String type;
  // 色彩空间 如 srgb
  private String space;
  // 是否有透明通道
  private boolean hasAlpha;
  // 是否有颜色配置文件
  private boolean hasProfile;
  // 色彩通道数
  private long channels;
  // 图片方向 0表示没有旋转，默认方向
  private long orientation;

  public ImageExifInfo(int width, int height, String type) {
    this.width = width;
    this.height = height;
    this.type = type;
  }

  public int getImageResolution(){
    return this.width * this.height;
  }

  public static ImageExifInfo of(int width, int height, String type) {
    return new ImageExifInfo(width, height, type);
  }
}
