package com.fxiaoke.document.convert.web;

import com.fxiaoke.document.convert.domain.mapper.AudioArgMapper;
import com.fxiaoke.document.convert.domain.model.AudioConvertArg;
import com.fxiaoke.document.convert.service.AudioConvertService;
import com.fxiaoke.document.convert.service.impl.AudioConvertServiceImpl;
import com.fxiaoke.document.convert.utils.BrowserUtils;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * creator: liuys
 * CreateTime: 2025-03-19
 * Description:
 */

@Controller
@RequestMapping("/audioConvert/Audio")
public class AudioConvertController {

  private final AudioArgMapper audioArgMapper;

  private final AudioConvertService audioConvertService;

  public AudioConvertController(AudioArgMapper audioArgMapper, AudioConvertServiceImpl audioConvertService) {
    this.audioArgMapper = audioArgMapper;
    this.audioConvertService = audioConvertService;
  }

  @GetMapping(path = "/FormatConvert", params = {"ea", "path", "sourceType", "targetType"})
  public ResponseEntity<FileSystemResource> convert(
      @RequestHeader(value = "X-fs-Employee-Id", defaultValue = "-10000") Integer employeeId,
      @RequestHeader(value = "X-fs-File-SecurityGroup", defaultValue = "") String securityGroup,
      @ModelAttribute @Validated AudioConvertArg audioConvertArg) {
    AudioConvertArg convertArg = audioArgMapper.supplementAudioFormatConvert(audioConvertArg, employeeId, securityGroup);
    String outFilePath = audioConvertService.convert(convertArg);
    return ResponseEntity.ok().headers(BrowserUtils.getHttpHeaders(outFilePath)).body(new FileSystemResource(outFilePath));
  }
}
