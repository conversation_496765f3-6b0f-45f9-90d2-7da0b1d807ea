package com.fxiaoke.document.convert.utils;

import com.fxiaoke.document.convert.domain.constants.Constants;
import com.fxiaoke.stone.commons.domain.utils.FileInfoUtil;
import com.google.common.base.Strings;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

public class BrowserUtils {
  private BrowserUtils() {
  }

  public static HttpHeaders getHttpHeaders( String filePath){
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
    File file = new File(filePath);
    ContentDisposition contentDisposition = ContentDisposition.builder("attachment")
        .filename(file.getName()).build();
    headers.setContentDisposition(contentDisposition);
    headers.setContentLength(file.length());
    return headers;
  }

  public static void setHead(HttpServletResponse response, String acModel, String extensions, String userAgent, String filename){
    response.setHeader(HttpHeaders.CONTENT_DISPOSITION, getDispositionName(userAgent,acModel,filename));
    response.setHeader(HttpHeaders.CONTENT_ENCODING, Constants.DEFAULT_CONTENT_ENCODING);
    response.setHeader(HttpHeaders.CACHE_CONTROL, Constants.DEFAULT_CACHE_CONTROL);
    response.setHeader(HttpHeaders.CONTENT_TYPE, FileInfoUtil.getMimeTypeByExtension(extensions));
  }

  public static String getDispositionName(String userAgent,String acModel,String filename){
    boolean isSafari = isSafari(userAgent);
    String encodeUTF8Filename = encodeURIComponent(filename);
    String encodedFilename = isSafari?encodeURIComponentForSafari(filename): encodeUTF8Filename;
    return acModel + ";filename=\"" + encodedFilename + "\";"
        + "filename*=utf-8''" + encodeUTF8Filename;
  }

  private static boolean isSafari(String userAgent) {
    if (!Strings.isNullOrEmpty(userAgent)) {
      String userAgentLowerCase = userAgent.toLowerCase();
      return userAgentLowerCase.contains("safari") && !userAgentLowerCase.contains("chrome");
    }
    return false;
  }

  private static String encodeURIComponent(String value) {
    return URLEncoder.encode(value, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
  }

  private static String encodeURIComponentForSafari(String value) {
    return new String(value.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
  }

}

