package com.fxiaoke.document.convert.service;

import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileDownloadRequest;
import com.facishare.stone.sdk.request.StoneFileUploadRequest;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.fxiaoke.document.convert.domain.exception.BaseException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import org.mockito.Mockito;
@ExtendWith(MockitoExtension.class)
class StoneApiServiceTest {

    @Mock
    private StoneProxyApi stoneProxyApi;

    @InjectMocks
    private StoneApiService stoneApiService;

    private static final String TEST_EA = "testEa";
    private static final int TEST_EMPLOYEE_ID = 12345;
    private static final String TEST_PATH = "test/document.pdf";
    private static final String TEST_FILENAME = "test.pdf";
    private static final String TEST_EXTENSION = "pdf";
    private static final int TEST_FILE_SIZE = 1024;
    private static final int TEST_EXPIRE_DAY = 3;

    @BeforeEach
    void setUp() {
        // Mock is injected via @InjectMocks
    }

    @Test
    void testUploadByStream_Success() throws FRestClientException {
        // Given
        InputStream testStream = new ByteArrayInputStream("test content".getBytes());
        StoneFileUploadResponse expectedResponse = new StoneFileUploadResponse();
        expectedResponse.setPath("uploaded/path");
        expectedResponse.setSize(1024L);

        Mockito.lenient().when(stoneProxyApi.uploadByStream(eq("n"), any(StoneFileUploadRequest.class), eq(testStream)))
            .thenReturn(expectedResponse);

        // When
        StoneFileUploadResponse result = stoneApiService.uploadByStream(
            TEST_EA, TEST_EMPLOYEE_ID, TEST_FILE_SIZE, TEST_FILENAME, TEST_EXTENSION, testStream);

        // Then
        assertNotNull(result);
        assertEquals("uploaded/path", result.getPath());
        assertEquals(1024L, result.getSize());

        ArgumentCaptor<StoneFileUploadRequest> requestCaptor =
            ArgumentCaptor.forClass(StoneFileUploadRequest.class);
        verify(stoneProxyApi).uploadByStream(eq("n"), requestCaptor.capture(), eq(testStream));

        StoneFileUploadRequest capturedRequest = requestCaptor.getValue();
        assertEquals(TEST_EA, capturedRequest.getEa());
        assertEquals(TEST_EMPLOYEE_ID, capturedRequest.getEmployeeId());
        assertEquals(TEST_FILE_SIZE, capturedRequest.getFileSize());
        assertEquals(TEST_FILENAME, capturedRequest.getOriginName());
        assertEquals(TEST_EXTENSION, capturedRequest.getExtensionName());
        assertEquals("FileProcess", capturedRequest.getBusiness());
        assertFalse(capturedRequest.getNeedCdn());
        assertFalse(capturedRequest.getNeedThumbnail());
    }

    @Test
    void testUploadByStream_ThrowsException() throws FRestClientException {
        // Given
        InputStream testStream = new ByteArrayInputStream("test content".getBytes());
        Mockito.lenient().when(stoneProxyApi.uploadByStream(eq("n"), any(StoneFileUploadRequest.class), eq(testStream)))
            .thenThrow(new RuntimeException("Test Error"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class,
            () -> stoneApiService.uploadByStream(
                TEST_EA, TEST_EMPLOYEE_ID, TEST_FILE_SIZE, TEST_FILENAME, TEST_EXTENSION, testStream));

        assertEquals("Test Error", exception.getMessage());
        verify(stoneProxyApi).uploadByStream(eq("n"), any(StoneFileUploadRequest.class), eq(testStream));
    }

    @Test
    void testTempFileUploadByStream_Success() throws FRestClientException {
        // Given
        InputStream testStream = new ByteArrayInputStream("test content".getBytes());
        StoneFileUploadResponse expectedResponse = new StoneFileUploadResponse();
        expectedResponse.setPath("temp/uploaded/path");
        expectedResponse.setSize(1024L);

        Mockito.lenient().when(stoneProxyApi.tempFileUploadByStream(eq("n"), any(StoneFileUploadRequest.class), eq(testStream)))
            .thenReturn(expectedResponse);

        // When
        StoneFileUploadResponse result = stoneApiService.tempFileUploadByStream(
            TEST_EA, TEST_EMPLOYEE_ID, TEST_FILE_SIZE, TEST_FILENAME, TEST_EXTENSION, TEST_EXPIRE_DAY, testStream);

        // Then
        assertNotNull(result);
        assertEquals("temp/uploaded/path", result.getPath());
        assertEquals(1024L, result.getSize());

        ArgumentCaptor<StoneFileUploadRequest> requestCaptor =
            ArgumentCaptor.forClass(StoneFileUploadRequest.class);
        verify(stoneProxyApi).tempFileUploadByStream(eq("n"), requestCaptor.capture(), eq(testStream));

        StoneFileUploadRequest capturedRequest = requestCaptor.getValue();
        assertEquals(TEST_EA, capturedRequest.getEa());
        assertEquals(TEST_EMPLOYEE_ID, capturedRequest.getEmployeeId());
        assertEquals(TEST_FILE_SIZE, capturedRequest.getFileSize());
        assertEquals(TEST_FILENAME, capturedRequest.getOriginName());
        assertEquals(TEST_EXTENSION, capturedRequest.getExtensionName());
        assertEquals("FileProcess", capturedRequest.getBusiness());
        assertEquals(TEST_EXPIRE_DAY, capturedRequest.getExpireDay());
        assertFalse(capturedRequest.getNeedCdn());
        assertFalse(capturedRequest.getNeedThumbnail());
    }

    @Test
    void testTempFileUploadByStream_ThrowsException() throws FRestClientException {
        // Given
        InputStream testStream = new ByteArrayInputStream("test content".getBytes());
        Mockito.lenient().when(stoneProxyApi.tempFileUploadByStream(eq("n"), any(StoneFileUploadRequest.class), eq(testStream)))
            .thenThrow(new RuntimeException("Test Error"));

        // When & Then
        BaseException exception = assertThrows(BaseException.class,
            () -> stoneApiService.tempFileUploadByStream(
                TEST_EA, TEST_EMPLOYEE_ID, TEST_FILE_SIZE, TEST_FILENAME, TEST_EXTENSION, TEST_EXPIRE_DAY, testStream));

        assertEquals(500, exception.getCode());
        assertTrue(exception.getMessage().contains("文件上传失败"));
//        assertTrue(exception.getModule().contains("StoneApiService-tempFileUploadByStream"));
        verify(stoneProxyApi).tempFileUploadByStream(eq("n"), any(StoneFileUploadRequest.class), eq(testStream));
    }

    @Test
    void testDownloadByStream_Success() throws FRestClientException {
        // Given
        InputStream expectedStream = new ByteArrayInputStream("downloaded content".getBytes());
        Mockito.lenient().when(stoneProxyApi.downloadStream(any(StoneFileDownloadRequest.class)))
            .thenReturn(expectedStream);

        // When
        InputStream result = stoneApiService.downloadByStream(TEST_EA, TEST_EMPLOYEE_ID, TEST_PATH);

        // Then
        assertNotNull(result);
        assertEquals(expectedStream, result);

        ArgumentCaptor<StoneFileDownloadRequest> requestCaptor =
            ArgumentCaptor.forClass(StoneFileDownloadRequest.class);
        verify(stoneProxyApi).downloadStream(requestCaptor.capture());

        StoneFileDownloadRequest capturedRequest = requestCaptor.getValue();
        assertEquals(TEST_EA, capturedRequest.getEa());
        assertEquals(TEST_EMPLOYEE_ID, capturedRequest.getEmployeeId());
        assertEquals(TEST_PATH, capturedRequest.getPath());
        assertEquals("XiaoKeNetDisk", capturedRequest.getSecurityGroup());
        assertEquals("FileProcess", capturedRequest.getBusiness());
    }

    @Test
    void testDownloadByStream_ThrowsException() throws FRestClientException {
        // Given
        Mockito.lenient().when(stoneProxyApi.downloadStream(any(StoneFileDownloadRequest.class)))
            .thenThrow(new RuntimeException("Test Error"));

        // When & Then
        BaseException exception = assertThrows(BaseException.class,
            () -> stoneApiService.downloadByStream(TEST_EA, TEST_EMPLOYEE_ID, TEST_PATH));

        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("文件下载失败"));
//        assertTrue(exception.getModule().contains("StoneApiService-downloadByStream"));
        verify(stoneProxyApi).downloadStream(any(StoneFileDownloadRequest.class));
    }

    @Test
    void testUploadByStream_WithNullValues_Success() throws FRestClientException {
        // Given
        InputStream testStream = new ByteArrayInputStream("test content".getBytes());
        StoneFileUploadResponse expectedResponse = new StoneFileUploadResponse();

        Mockito.lenient().when(stoneProxyApi.uploadByStream(eq("n"), any(StoneFileUploadRequest.class), eq(testStream)))
            .thenReturn(expectedResponse);

        // When
        StoneFileUploadResponse result = stoneApiService.uploadByStream(
            TEST_EA, TEST_EMPLOYEE_ID, null, null, null, testStream);

        // Then
        assertNotNull(result);

        ArgumentCaptor<StoneFileUploadRequest> requestCaptor =
            ArgumentCaptor.forClass(StoneFileUploadRequest.class);
        verify(stoneProxyApi).uploadByStream(eq("n"), requestCaptor.capture(), eq(testStream));

        StoneFileUploadRequest capturedRequest = requestCaptor.getValue();
        assertNull(capturedRequest.getFileSize());
        assertNull(capturedRequest.getOriginName());
        assertNull(capturedRequest.getExtensionName());
    }

    @Test
    void testTempFileUploadByStream_WithZeroExpireDay_Success() throws FRestClientException {
        // Given
        InputStream testStream = new ByteArrayInputStream("test content".getBytes());
        StoneFileUploadResponse expectedResponse = new StoneFileUploadResponse();

        Mockito.lenient().when(stoneProxyApi.tempFileUploadByStream(eq("n"), any(StoneFileUploadRequest.class), eq(testStream)))
            .thenReturn(expectedResponse);

        // When
        StoneFileUploadResponse result = stoneApiService.tempFileUploadByStream(
            TEST_EA, TEST_EMPLOYEE_ID, TEST_FILE_SIZE, TEST_FILENAME, TEST_EXTENSION, 0, testStream);

        // Then
        assertNotNull(result);

        ArgumentCaptor<StoneFileUploadRequest> requestCaptor =
            ArgumentCaptor.forClass(StoneFileUploadRequest.class);
        verify(stoneProxyApi).tempFileUploadByStream(eq("n"), requestCaptor.capture(), eq(testStream));

        StoneFileUploadRequest capturedRequest = requestCaptor.getValue();
        assertEquals(0, capturedRequest.getExpireDay());
    }

    @Test
    void testDownloadByStream_WithEmptyPath_Success() throws FRestClientException {
        // Given
        String emptyPath = "";
        InputStream expectedStream = new ByteArrayInputStream("content".getBytes());
        Mockito.lenient().when(stoneProxyApi.downloadStream(any(StoneFileDownloadRequest.class)))
            .thenReturn(expectedStream);

        // When
        InputStream result = stoneApiService.downloadByStream(TEST_EA, TEST_EMPLOYEE_ID, emptyPath);

        // Then
        assertNotNull(result);

        ArgumentCaptor<StoneFileDownloadRequest> requestCaptor =
            ArgumentCaptor.forClass(StoneFileDownloadRequest.class);
        verify(stoneProxyApi).downloadStream(requestCaptor.capture());

        StoneFileDownloadRequest capturedRequest = requestCaptor.getValue();
        assertEquals(emptyPath, capturedRequest.getPath());
    }

    @Test
    void testUploadByStream_WithLargeFileSize_Success() throws FRestClientException {
        // Given
        int largeFileSize = Integer.MAX_VALUE;
        InputStream testStream = new ByteArrayInputStream("large file content".getBytes());
        StoneFileUploadResponse expectedResponse = new StoneFileUploadResponse();

        Mockito.lenient().when(stoneProxyApi.uploadByStream(eq("n"), any(StoneFileUploadRequest.class), eq(testStream)))
            .thenReturn(expectedResponse);

        // When
        StoneFileUploadResponse result = stoneApiService.uploadByStream(
            TEST_EA, TEST_EMPLOYEE_ID, largeFileSize, TEST_FILENAME, TEST_EXTENSION, testStream);

        // Then
        assertNotNull(result);

        ArgumentCaptor<StoneFileUploadRequest> requestCaptor =
            ArgumentCaptor.forClass(StoneFileUploadRequest.class);
        verify(stoneProxyApi).uploadByStream(eq("n"), requestCaptor.capture(), eq(testStream));

        StoneFileUploadRequest capturedRequest = requestCaptor.getValue();
        assertEquals(largeFileSize, capturedRequest.getFileSize());
    }

    @Test
    void testTempFileUploadByStream_WithNegativeEmployeeId_Success() throws FRestClientException {
        // Given
        int negativeEmployeeId = -1;
        InputStream testStream = new ByteArrayInputStream("test content".getBytes());
        StoneFileUploadResponse expectedResponse = new StoneFileUploadResponse();

        Mockito.lenient().when(stoneProxyApi.tempFileUploadByStream(eq("n"), any(StoneFileUploadRequest.class), eq(testStream)))
            .thenReturn(expectedResponse);

        // When
        StoneFileUploadResponse result = stoneApiService.tempFileUploadByStream(
            TEST_EA, negativeEmployeeId, TEST_FILE_SIZE, TEST_FILENAME, TEST_EXTENSION, TEST_EXPIRE_DAY, testStream);

        // Then
        assertNotNull(result);

        ArgumentCaptor<StoneFileUploadRequest> requestCaptor =
            ArgumentCaptor.forClass(StoneFileUploadRequest.class);
        verify(stoneProxyApi).tempFileUploadByStream(eq("n"), requestCaptor.capture(), eq(testStream));

        StoneFileUploadRequest capturedRequest = requestCaptor.getValue();
        assertEquals(negativeEmployeeId, capturedRequest.getEmployeeId());
    }

    @Test
    void testDownloadByStream_WithNegativeEmployeeId_Success() throws FRestClientException {
        // Given
        int negativeEmployeeId = -1;
        InputStream expectedStream = new ByteArrayInputStream("content".getBytes());
        Mockito.lenient().when(stoneProxyApi.downloadStream(any(StoneFileDownloadRequest.class)))
            .thenReturn(expectedStream);

        // When
        InputStream result = stoneApiService.downloadByStream(TEST_EA, negativeEmployeeId, TEST_PATH);

        // Then
        assertNotNull(result);

        ArgumentCaptor<StoneFileDownloadRequest> requestCaptor =
            ArgumentCaptor.forClass(StoneFileDownloadRequest.class);
        verify(stoneProxyApi).downloadStream(requestCaptor.capture());

        StoneFileDownloadRequest capturedRequest = requestCaptor.getValue();
        assertEquals(negativeEmployeeId, capturedRequest.getEmployeeId());
    }

    @Test
    void testUploadByStream_VerifyDefaultValues() throws FRestClientException {
        // Given
        InputStream testStream = new ByteArrayInputStream("test content".getBytes());
        StoneFileUploadResponse expectedResponse = new StoneFileUploadResponse();

        Mockito.lenient().when(stoneProxyApi.uploadByStream(eq("n"), any(StoneFileUploadRequest.class), eq(testStream)))
            .thenReturn(expectedResponse);

        // When
        stoneApiService.uploadByStream(TEST_EA, TEST_EMPLOYEE_ID, TEST_FILE_SIZE, TEST_FILENAME, TEST_EXTENSION, testStream);

        // Then
        ArgumentCaptor<StoneFileUploadRequest> requestCaptor =
            ArgumentCaptor.forClass(StoneFileUploadRequest.class);
        verify(stoneProxyApi).uploadByStream(eq("n"), requestCaptor.capture(), eq(testStream));

        StoneFileUploadRequest capturedRequest = requestCaptor.getValue();
        assertEquals("FileProcess", capturedRequest.getBusiness());
        assertFalse(capturedRequest.getNeedCdn());
        assertFalse(capturedRequest.getNeedThumbnail());
    }

    @Test
    void testTempFileUploadByStream_VerifyDefaultValues() throws FRestClientException {
        // Given
        InputStream testStream = new ByteArrayInputStream("test content".getBytes());
        StoneFileUploadResponse expectedResponse = new StoneFileUploadResponse();

        Mockito.lenient().when(stoneProxyApi.tempFileUploadByStream(eq("n"), any(StoneFileUploadRequest.class), eq(testStream)))
            .thenReturn(expectedResponse);

        // When
        stoneApiService.tempFileUploadByStream(
            TEST_EA, TEST_EMPLOYEE_ID, TEST_FILE_SIZE, TEST_FILENAME, TEST_EXTENSION, TEST_EXPIRE_DAY, testStream);

        // Then
        ArgumentCaptor<StoneFileUploadRequest> requestCaptor =
            ArgumentCaptor.forClass(StoneFileUploadRequest.class);
        verify(stoneProxyApi).tempFileUploadByStream(eq("n"), requestCaptor.capture(), eq(testStream));

        StoneFileUploadRequest capturedRequest = requestCaptor.getValue();
        assertEquals("FileProcess", capturedRequest.getBusiness());
        assertFalse(capturedRequest.getNeedCdn());
        assertFalse(capturedRequest.getNeedThumbnail());
    }

    @Test
    void testDownloadByStream_VerifyDefaultValues() throws FRestClientException {
        // Given
        InputStream expectedStream = new ByteArrayInputStream("content".getBytes());
        Mockito.lenient().when(stoneProxyApi.downloadStream(any(StoneFileDownloadRequest.class)))
            .thenReturn(expectedStream);

        // When
        stoneApiService.downloadByStream(TEST_EA, TEST_EMPLOYEE_ID, TEST_PATH);

        // Then
        ArgumentCaptor<StoneFileDownloadRequest> requestCaptor =
            ArgumentCaptor.forClass(StoneFileDownloadRequest.class);
        verify(stoneProxyApi).downloadStream(requestCaptor.capture());

        StoneFileDownloadRequest capturedRequest = requestCaptor.getValue();
        assertEquals("XiaoKeNetDisk", capturedRequest.getSecurityGroup());
        assertEquals("FileProcess", capturedRequest.getBusiness());
    }
}
