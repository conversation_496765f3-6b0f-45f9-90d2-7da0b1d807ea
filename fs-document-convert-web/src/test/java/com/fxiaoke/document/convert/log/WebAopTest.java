package com.fxiaoke.document.convert.log;

import com.fxiaoke.document.convert.domain.Cause;
import com.fxiaoke.document.convert.domain.open.MergerOffice;
import com.fxiaoke.document.convert.domain.preview.BaseArg;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class WebAopTest {

    @Mock
    private ProceedingJoinPoint proceedingJoinPoint;

    @Mock
    private Signature signature;

    @Mock
    private Cause<BaseArg> cause;

    @Mock
    private BaseArg baseArg;

    @Mock
    private MergerOffice.Arg mergerOfficeArg;

    @InjectMocks
    private WebAop webAop;

    @BeforeEach
    void setUp() {
        when(proceedingJoinPoint.getSignature()).thenReturn(signature);
        when(signature.getName()).thenReturn("testMethod");
    }

    @Test
    void testPreviewTimeWait_Success() throws Throwable {
        // Given
        Object expectedResult = "test result";
        when(cause.getData()).thenReturn(baseArg);
        when(proceedingJoinPoint.proceed()).thenReturn(expectedResult);

        // When
        Object result = webAop.previewTimeWait(proceedingJoinPoint, cause);

        // Then
        assertEquals(expectedResult, result);
        verify(proceedingJoinPoint).proceed();
        verify(cause).getData();
        verify(signature).getName();
    }

    @Test
    void testPreviewTimeWait_ThrowsException() throws Throwable {
        // Given
        RuntimeException expectedException = new RuntimeException("Test exception");
        when(cause.getData()).thenReturn(baseArg);
        when(proceedingJoinPoint.proceed()).thenThrow(expectedException);

        // When & Then
        RuntimeException thrownException = assertThrows(RuntimeException.class, () ->
            webAop.previewTimeWait(proceedingJoinPoint, cause));

        assertEquals(expectedException, thrownException);
        verify(proceedingJoinPoint).proceed();
        verify(cause).getData();
        verify(signature).getName();
    }

    @Test
    void testOpenGetTimeWait_Success() throws Throwable {
        // Given
        Object expectedResult = "open api result";
        Object[] args = {"arg1", "arg2"};
        when(proceedingJoinPoint.getArgs()).thenReturn(args);
        when(proceedingJoinPoint.proceed()).thenReturn(expectedResult);

        // When
        Object result = webAop.openGetTimeWait(proceedingJoinPoint);

        // Then
        assertEquals(expectedResult, result);
        verify(proceedingJoinPoint).proceed();
        verify(proceedingJoinPoint).getArgs();
        verify(signature).getName();
    }

    @Test
    void testOpenGetTimeWait_ThrowsException() throws Throwable {
        // Given
        RuntimeException expectedException = new RuntimeException("Open API exception");
        Object[] args = {"arg1", "arg2"};
        when(proceedingJoinPoint.getArgs()).thenReturn(args);
        when(proceedingJoinPoint.proceed()).thenThrow(expectedException);

        // When & Then
        RuntimeException thrownException = assertThrows(RuntimeException.class, () ->
            webAop.openGetTimeWait(proceedingJoinPoint));

        assertEquals(expectedException, thrownException);
        verify(proceedingJoinPoint).proceed();
        verify(proceedingJoinPoint).getArgs();
        verify(signature).getName();
    }

    @Test
    void testMergerOfficeTimeWait_Success() throws Throwable {
        // Given
        Object expectedResult = "merger result";
        when(proceedingJoinPoint.proceed()).thenReturn(expectedResult);

        // When
        Object result = webAop.mergerOfficeTimeWait(proceedingJoinPoint, mergerOfficeArg);

        // Then
        assertEquals(expectedResult, result);
        verify(proceedingJoinPoint).proceed();
        verify(signature).getName();
    }

    @Test
    void testMergerOfficeTimeWait_ThrowsException() throws Throwable {
        // Given
        RuntimeException expectedException = new RuntimeException("Merger exception");
        when(proceedingJoinPoint.proceed()).thenThrow(expectedException);

        // When & Then
        RuntimeException thrownException = assertThrows(RuntimeException.class, () ->
            webAop.mergerOfficeTimeWait(proceedingJoinPoint, mergerOfficeArg));

        assertEquals(expectedException, thrownException);
        verify(proceedingJoinPoint).proceed();
        verify(signature).getName();
    }

    @Test
    void testPreviewTimeWait_WithNullData() throws Throwable {
        // Given
        Object expectedResult = "result with null data";
        when(cause.getData()).thenReturn(null);
        when(proceedingJoinPoint.proceed()).thenReturn(expectedResult);

        // When
        Object result = webAop.previewTimeWait(proceedingJoinPoint, cause);

        // Then
        assertEquals(expectedResult, result);
        verify(proceedingJoinPoint).proceed();
        verify(cause).getData();
        verify(signature).getName();
    }

    @Test
    void testOpenGetTimeWait_WithEmptyArgs() throws Throwable {
        // Given
        Object expectedResult = "result with empty args";
        Object[] emptyArgs = {};
        when(proceedingJoinPoint.getArgs()).thenReturn(emptyArgs);
        when(proceedingJoinPoint.proceed()).thenReturn(expectedResult);

        // When
        Object result = webAop.openGetTimeWait(proceedingJoinPoint);

        // Then
        assertEquals(expectedResult, result);
        verify(proceedingJoinPoint).proceed();
        verify(proceedingJoinPoint).getArgs();
        verify(signature).getName();
    }

    @Test
    void testOpenGetTimeWait_WithNullArgs() throws Throwable {
        // Given
        Object expectedResult = "result with null args";
        when(proceedingJoinPoint.getArgs()).thenReturn(null);
        when(proceedingJoinPoint.proceed()).thenReturn(expectedResult);

        // When
        Object result = webAop.openGetTimeWait(proceedingJoinPoint);

        // Then
        assertEquals(expectedResult, result);
        verify(proceedingJoinPoint).proceed();
        verify(proceedingJoinPoint).getArgs();
        verify(signature).getName();
    }

    @Test
    void testMergerOfficeTimeWait_WithNullArg() throws Throwable {
        // Given
        Object expectedResult = "result with null merger arg";
        when(proceedingJoinPoint.proceed()).thenReturn(expectedResult);

        // When
        Object result = webAop.mergerOfficeTimeWait(proceedingJoinPoint, null);

        // Then
        assertEquals(expectedResult, result);
        verify(proceedingJoinPoint).proceed();
        verify(signature).getName();
    }

    @Test
    void testPreviewTimeWait_VerifyTimingBehavior() throws Throwable {
        // Given
        Object expectedResult = "timing test result";
        when(cause.getData()).thenReturn(baseArg);
        when(proceedingJoinPoint.proceed()).thenAnswer(invocation -> {
            // Simulate some processing time
            Thread.sleep(10);
            return expectedResult;
        });

        // When
        long startTime = System.currentTimeMillis();
        Object result = webAop.previewTimeWait(proceedingJoinPoint, cause);
        long endTime = System.currentTimeMillis();

        // Then
        assertEquals(expectedResult, result);
        assertTrue(endTime - startTime >= 10); // At least 10ms elapsed
        verify(proceedingJoinPoint).proceed();
        verify(cause).getData();
        verify(signature).getName();
    }
}
