package com.fxiaoke.document.convert.process;

import com.fxiaoke.document.convert.domain.exception.BaseException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

class HtmlProcessTest {

    @TempDir
    Path tempDir;

    private HtmlProcess htmlProcess;

    @BeforeEach
    void setUp() {
        htmlProcess = new HtmlProcess();
    }

    @Test
    void testExcelToHtmlPostprocessor_WithImagesAndCss() throws IOException {
        // Given
        String htmlContent = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Test</title>
            </head>
            <body>
                <img src="image1.png" alt="Image 1">
                <img src="image2.jpg" alt="Image 2">
                <p>Some content</p>
            </body>
            </html>
            """;
        
        Path htmlFile = tempDir.resolve("test.html");
        Files.write(htmlFile, htmlContent.getBytes());
        
        String cssLink = "styles.css";
        boolean ableProcessCss = true;

        // When
        htmlProcess.excelToHtmlPostprocessor(htmlFile.toString(), cssLink, ableProcessCss);

        // Then
        String processedContent = Files.readString(htmlFile);
        
        // Check that images have loading="lazy" attribute
        assertTrue(processedContent.contains("loading=\"lazy\""));
        
        // Check that CSS link is added
        assertTrue(processedContent.contains("<link rel=\"stylesheet\" type=\"text/css\" href=\"styles.css\">"));
        
        // Verify all img tags have the lazy loading attribute
        long imgCount = processedContent.lines()
                .filter(line -> line.contains("<img"))
                .count();
        long lazyImgCount = processedContent.lines()
                .filter(line -> line.contains("loading=\"lazy\""))
                .count();
        assertEquals(imgCount, lazyImgCount);
    }

    @Test
    void testExcelToHtmlPostprocessor_WithoutCssProcessing() throws IOException {
        // Given
        String htmlContent = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Test</title>
            </head>
            <body>
                <img src="image1.png" alt="Image 1">
                <p>Some content</p>
            </body>
            </html>
            """;
        
        Path htmlFile = tempDir.resolve("test.html");
        Files.write(htmlFile, htmlContent.getBytes());
        
        String cssLink = "styles.css";
        boolean ableProcessCss = false;

        // When
        htmlProcess.excelToHtmlPostprocessor(htmlFile.toString(), cssLink, ableProcessCss);

        // Then
        String processedContent = Files.readString(htmlFile);
        
        // Check that images have loading="lazy" attribute
        assertTrue(processedContent.contains("loading=\"lazy\""));
        
        // Check that CSS link is NOT added
        assertFalse(processedContent.contains("<link rel=\"stylesheet\" type=\"text/css\" href=\"styles.css\">"));
    }

    @Test
    void testExcelToHtmlPostprocessor_NoImages() throws IOException {
        // Given
        String htmlContent = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Test</title>
            </head>
            <body>
                <p>Some content without images</p>
                <div>More content</div>
            </body>
            </html>
            """;
        
        Path htmlFile = tempDir.resolve("test.html");
        Files.write(htmlFile, htmlContent.getBytes());
        
        String cssLink = "styles.css";
        boolean ableProcessCss = true;

        // When
        htmlProcess.excelToHtmlPostprocessor(htmlFile.toString(), cssLink, ableProcessCss);

        // Then
        String processedContent = Files.readString(htmlFile);
        
        // Check that CSS link is added
        assertTrue(processedContent.contains("<link rel=\"stylesheet\" type=\"text/css\" href=\"styles.css\">"));
        
        // Check that no loading="lazy" attributes are added (no images)
        assertFalse(processedContent.contains("loading=\"lazy\""));
    }

    @Test
    void testExcelToHtmlPostprocessor_MultipleImages() throws IOException {
        // Given
        String htmlContent = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Test</title>
            </head>
            <body>
                <img src="image1.png" alt="Image 1">
                <img src="image2.jpg" alt="Image 2">
                <img src="image3.gif" alt="Image 3">
                <img src="image4.svg" alt="Image 4">
            </body>
            </html>
            """;
        
        Path htmlFile = tempDir.resolve("test.html");
        Files.write(htmlFile, htmlContent.getBytes());
        
        String cssLink = "custom.css";
        boolean ableProcessCss = true;

        // When
        htmlProcess.excelToHtmlPostprocessor(htmlFile.toString(), cssLink, ableProcessCss);

        // Then
        String processedContent = Files.readString(htmlFile);
        
        // Count occurrences of loading="lazy"
        long lazyCount = processedContent.lines()
                .mapToLong(line -> line.split("loading=\"lazy\"", -1).length - 1)
                .sum();
        assertEquals(4, lazyCount);
        
        // Check CSS link
        assertTrue(processedContent.contains("<link rel=\"stylesheet\" type=\"text/css\" href=\"custom.css\">"));
    }

    @Test
    void testExcelToHtmlPostprocessor_ExistingCssLinks() throws IOException {
        // Given
        String htmlContent = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Test</title>
                <link rel="stylesheet" href="existing.css">
            </head>
            <body>
                <img src="image1.png" alt="Image 1">
            </body>
            </html>
            """;
        
        Path htmlFile = tempDir.resolve("test.html");
        Files.write(htmlFile, htmlContent.getBytes());
        
        String cssLink = "new.css";
        boolean ableProcessCss = true;

        // When
        htmlProcess.excelToHtmlPostprocessor(htmlFile.toString(), cssLink, ableProcessCss);

        // Then
        String processedContent = Files.readString(htmlFile);
        
        // Check that both CSS links exist
        assertTrue(processedContent.contains("existing.css"));
        assertTrue(processedContent.contains("<link rel=\"stylesheet\" type=\"text/css\" href=\"new.css\">"));
        
        // Check image processing
        assertTrue(processedContent.contains("loading=\"lazy\""));
    }

    @Test
    void testExcelToHtmlPostprocessor_EmptyHtml() throws IOException {
        // Given
        String htmlContent = """
            <!DOCTYPE html>
            <html>
            <head></head>
            <body></body>
            </html>
            """;
        
        Path htmlFile = tempDir.resolve("test.html");
        Files.write(htmlFile, htmlContent.getBytes());
        
        String cssLink = "styles.css";
        boolean ableProcessCss = true;

        // When
        htmlProcess.excelToHtmlPostprocessor(htmlFile.toString(), cssLink, ableProcessCss);

        // Then
        String processedContent = Files.readString(htmlFile);
        
        // Check that CSS link is added
        assertTrue(processedContent.contains("<link rel=\"stylesheet\" type=\"text/css\" href=\"styles.css\">"));
        
        // No images to process
        assertFalse(processedContent.contains("loading=\"lazy\""));
    }

    @Test
    void testExcelToHtmlPostprocessor_NonExistentFile() {
        // Given
        String nonExistentFile = tempDir.resolve("nonexistent.html").toString();
        String cssLink = "styles.css";
        boolean ableProcessCss = true;

        // When & Then
        BaseException exception = assertThrows(BaseException.class, () ->
            htmlProcess.excelToHtmlPostprocessor(nonExistentFile, cssLink, ableProcessCss));

        assertEquals(500, exception.getCode());
        assertTrue(exception.getMessage().contains("Html文件处理失败"));
    }

    @Test
    void testExcelToHtmlPostprocessor_InvalidHtml() throws IOException {
        // Given
        String invalidHtmlContent = "This is not valid HTML content";
        
        Path htmlFile = tempDir.resolve("invalid.html");
        Files.write(htmlFile, invalidHtmlContent.getBytes());
        
        String cssLink = "styles.css";
        boolean ableProcessCss = true;

        // When & Then - Should not throw exception, Jsoup is forgiving
        assertDoesNotThrow(() ->
            htmlProcess.excelToHtmlPostprocessor(htmlFile.toString(), cssLink, ableProcessCss));

        // Verify file was processed (Jsoup will create valid HTML structure)
        String processedContent = Files.readString(htmlFile);
        assertTrue(processedContent.contains("<html>"));
        assertTrue(processedContent.contains("<head>"));
        assertTrue(processedContent.contains("<body>"));
    }

    @Test
    void testExcelToHtmlPostprocessor_SpecialCharactersInCssLink() throws IOException {
        // Given
        String htmlContent = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Test</title>
            </head>
            <body>
                <img src="image.png" alt="Image">
            </body>
            </html>
            """;
        
        Path htmlFile = tempDir.resolve("test.html");
        Files.write(htmlFile, htmlContent.getBytes());
        
        String cssLink = "path/to/styles with spaces & symbols.css";
        boolean ableProcessCss = true;

        // When
        htmlProcess.excelToHtmlPostprocessor(htmlFile.toString(), cssLink, ableProcessCss);

        // Then
        String processedContent = Files.readString(htmlFile);
        
        // Check that CSS link with special characters is properly handled
        assertTrue(processedContent.contains("href=\"path/to/styles with spaces &amp; symbols.css\""));
        assertTrue(processedContent.contains("loading=\"lazy\""));
    }

    @Test
    void testExcelToHtmlPostprocessor_ImagesWithExistingAttributes() throws IOException {
        // Given
        String htmlContent = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Test</title>
            </head>
            <body>
                <img src="image1.png" alt="Image 1" class="existing-class">
                <img src="image2.jpg" alt="Image 2" style="width: 100px;" loading="eager">
            </body>
            </html>
            """;
        
        Path htmlFile = tempDir.resolve("test.html");
        Files.write(htmlFile, htmlContent.getBytes());
        
        String cssLink = "styles.css";
        boolean ableProcessCss = true;

        // When
        htmlProcess.excelToHtmlPostprocessor(htmlFile.toString(), cssLink, ableProcessCss);

        // Then
        String processedContent = Files.readString(htmlFile);
        
        // Check that loading="lazy" is added/overrides existing loading attribute
        assertTrue(processedContent.contains("loading=\"lazy\""));
        
        // Check that other attributes are preserved
        assertTrue(processedContent.contains("class=\"existing-class\""));
        assertTrue(processedContent.contains("style=\"width: 100px;\""));
        
        // The second image should have loading="lazy" instead of loading="eager"
        assertFalse(processedContent.contains("loading=\"eager\""));
    }
}
