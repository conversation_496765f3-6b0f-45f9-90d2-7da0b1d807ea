package com.fxiaoke.document.convert.exception;

import com.fxiaoke.document.convert.domain.Result;
import com.fxiaoke.document.convert.domain.exception.BaseException;
import com.fxiaoke.document.convert.domain.exception.PreviewException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;

import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GlobalExceptionHandlerTest {

    @Mock
    private HttpServletResponse response;

    @InjectMocks
    private GlobalExceptionHandler globalExceptionHandler;

    @BeforeEach
    void setUp() {
        // Reset response mock before each test
        reset(response);
    }

    @Test
    void testHandleGenericException() {
        // Given
        Exception exception = new RuntimeException("Test exception");

        // When
        Result<String> result = globalExceptionHandler.handler(response, exception);

        // Then
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("An unknown exception has occurred. Please contact the administrator", result.getMessage());
        verify(response).setStatus(500);
    }

    @Test
    void testHandleMissingServletRequestParameterException() {
        // Given
        MissingServletRequestParameterException exception =
            new MissingServletRequestParameterException("param", "String");

        // When
        Result<String> result = globalExceptionHandler.handler(response, exception);

        // Then
        assertNotNull(result);
        assertEquals(400, result.getCode());
        assertTrue(result.getMessage().contains("param"));
        verify(response).setStatus(400);
    }

    @Test
    void testHandleIllegalArgumentException() {
        // Given
        IllegalArgumentException exception = new IllegalArgumentException("Invalid argument");

        // When
        Result<String> result = globalExceptionHandler.handler(response, exception);

        // Then
        assertNotNull(result);
        assertEquals(400, result.getCode());
        assertEquals("Invalid argument", result.getMessage());
        verify(response).setStatus(400);
    }

    @Test
    void testHandleConstraintViolationException() {
        // Given
        ConstraintViolation<?> violation = mock(ConstraintViolation.class);
        when(violation.getMessage()).thenReturn("Validation failed");
        ConstraintViolationException exception =
            new ConstraintViolationException("Constraint violation", Collections.singleton(violation));

        // When
        Result<String> result = globalExceptionHandler.handler(response, exception);

        // Then
        assertNotNull(result);
        assertEquals(400, result.getCode());
        assertEquals("Constraint violation", result.getMessage());
        verify(response).setStatus(400);
    }

    @Test
    void testHandleMethodArgumentNotValidException() {
        // Given
        FieldError fieldError = new FieldError("objectName", "field", "Field is required");
        MethodArgumentNotValidException exception = mock(MethodArgumentNotValidException.class);
        when(exception.getFieldError()).thenReturn(fieldError);
        when(exception.getMessage()).thenReturn("Validation failed");

        // When
        Result<String> result = globalExceptionHandler.handler(response, exception);

        // Then
        assertNotNull(result);
        assertEquals(400, result.getCode());
        assertEquals("Field is required", result.getMessage());
        verify(response).setStatus(400);
    }

    @Test
    void testHandleHttpMessageNotReadableException() {
        // Given
        HttpMessageNotReadableException exception =
            new HttpMessageNotReadableException("JSON parse error");

        // When
        Result<String> result = globalExceptionHandler.handler(response, exception);

        // Then
        assertNotNull(result);
        assertEquals(400, result.getCode());
        assertEquals("json parse error", result.getMessage());
        verify(response).setStatus(400);
    }

    @Test
    void testHandleBaseException() {
        // Given
        BaseException exception = new BaseException(400, "Base exception message", "TestModule");

        // When
        Result<String> result = globalExceptionHandler.handler(response, exception);

        // Then
        assertNotNull(result);
        assertEquals(400, result.getCode());
        assertEquals("Base exception message", result.getMessage());
        verify(response).setStatus(400);
    }

    @Test
    void testHandlePreviewException_ClientError() {
        // Given
        BaseException baseException = new BaseException(400, "Preview error", "TestModule");
        PreviewException exception = new PreviewException(baseException);

        // When
        Result<String> result = globalExceptionHandler.handler(response, exception);

        // Then
        assertNotNull(result);
        assertEquals(400, result.getCode());
        assertEquals("Preview error", result.getMessage());
        verify(response).setStatus(400);
    }

    @Test
    void testHandlePreviewException_ServerError() {
        // Given
        BaseException baseException = new BaseException(500, "Server preview error", "TestModule");
        PreviewException exception = new PreviewException(baseException);

        // When
        Result<String> result = globalExceptionHandler.handler(response, exception);

        // Then
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("非常抱歉,该文件暂不支持预览,请您检查格式后重试。", result.getMessage()); // PreviewException changes message for 500 errors
        verify(response).setStatus(500);
    }

    @Test
    void testHandleMethodArgumentNotValidException_NullFieldError() {
        // Given
        MethodArgumentNotValidException exception = mock(MethodArgumentNotValidException.class);
        when(exception.getFieldError()).thenReturn(null);
        when(exception.getMessage()).thenReturn("Validation failed");

        // When & Then
        assertThrows(NullPointerException.class, () ->
            globalExceptionHandler.handler(response, exception));
    }

    @Test
    void testHandleNullPointerException() {
        // Given
        NullPointerException exception = new NullPointerException("Null pointer");

        // When
        Result<String> result = globalExceptionHandler.handler(response, exception);

        // Then
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("An unknown exception has occurred. Please contact the administrator", result.getMessage());
        verify(response).setStatus(500);
    }

    @Test
    void testHandleRuntimeException() {
        // Given
        RuntimeException exception = new RuntimeException("Runtime error");

        // When
        Result<String> result = globalExceptionHandler.handler(response, exception);

        // Then
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("An unknown exception has occurred. Please contact the administrator", result.getMessage());
        verify(response).setStatus(500);
    }

    @Test
    void testHandleExceptionWithNullMessage() {
        // Given
        Exception exception = new RuntimeException((String) null);

        // When
        Result<String> result = globalExceptionHandler.handler(response, exception);

        // Then
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("An unknown exception has occurred. Please contact the administrator", result.getMessage());
        verify(response).setStatus(500);
    }

    @Test
    void testHandleBaseExceptionWithDifferentCode() {
        // Given
        BaseException exception = new BaseException(422, "Unprocessable entity", "TestModule");

        // When
        Result<String> result = globalExceptionHandler.handler(response, exception);

        // Then
        assertNotNull(result);
        assertEquals(400, result.getCode()); // GlobalExceptionHandler always returns 400 for BaseException
        assertEquals("Unprocessable entity", result.getMessage());
        verify(response).setStatus(400);
    }
}
