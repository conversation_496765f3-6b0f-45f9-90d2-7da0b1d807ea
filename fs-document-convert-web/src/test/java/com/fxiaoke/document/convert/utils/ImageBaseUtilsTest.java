package com.fxiaoke.document.convert.utils;

import com.fxiaoke.document.convert.domain.model.ImageDimension;
import com.fxiaoke.document.convert.domain.model.ImageExifInfo;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ImageBaseUtilsTest {

    @Test
    void testGetCompressingWH_NoCompressionNeeded() {
        // Given
        int width = 800;
        int height = 600;
        double maxResolution = 1000000; // 1M pixels

        // When
        ImageDimension result = ImageBaseUtils.getCompressingWH(width, height, maxResolution);

        // Then
        assertEquals(800, result.getWidth());
        assertEquals(600, result.getHeight());
    }

    @Test
    void testGetCompressingWH_CompressionNeeded() {
        // Given
        int width = 2000;
        int height = 2000;
        double maxResolution = 1000000; // 1M pixels

        // When
        ImageDimension result = ImageBaseUtils.getCompressingWH(width, height, maxResolution);

        // Then
        assertTrue(result.getWidth() < width);
        assertTrue(result.getHeight() < height);
        assertTrue(result.getWidth() * result.getHeight() <= maxResolution);
    }

    @Test
    void testGetCompressingWH_ExactMaxResolution() {
        // Given
        int width = 1000;
        int height = 1000;
        double maxResolution = 1000000; // Exactly 1M pixels

        // When
        ImageDimension result = ImageBaseUtils.getCompressingWH(width, height, maxResolution);

        // Then
        assertEquals(1000, result.getWidth());
        assertEquals(1000, result.getHeight());
    }

    @Test
    void testGetCompressingWH_SmallImage() {
        // Given
        int width = 100;
        int height = 100;
        double maxResolution = 1000000;

        // When
        ImageDimension result = ImageBaseUtils.getCompressingWH(width, height, maxResolution);

        // Then
        assertEquals(100, result.getWidth());
        assertEquals(100, result.getHeight());
    }

    @Test
    void testGetImageDimensions_NullData() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> 
            ImageBaseUtils.getImageDimensions(null));
    }

    @Test
    void testGetImageDimensions_InsufficientData() {
        // Given
        byte[] shortData = new byte[10];

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> 
            ImageBaseUtils.getImageDimensions(shortData));
    }

    @Test
    void testGetImageDimensions_UnsupportedFormat() {
        // Given
        byte[] unsupportedData = new byte[30];
        // Fill with random data that doesn't match any format
        for (int i = 0; i < unsupportedData.length; i++) {
            unsupportedData[i] = (byte) (i % 256);
        }

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> 
            ImageBaseUtils.getImageDimensions(unsupportedData));
    }

    @Test
    void testGetImageDimensions_PNG() {
        // Given - Create a minimal PNG header
        byte[] pngData = new byte[24];
        // PNG signature
        pngData[0] = (byte) 0x89;
        pngData[1] = (byte) 0x50;
        pngData[2] = (byte) 0x4E;
        pngData[3] = (byte) 0x47;
        pngData[4] = (byte) 0x0D;
        pngData[5] = (byte) 0x0A;
        pngData[6] = (byte) 0x1A;
        pngData[7] = (byte) 0x0A;
        // IHDR chunk length (8 bytes)
        pngData[8] = 0x00;
        pngData[9] = 0x00;
        pngData[10] = 0x00;
        pngData[11] = 0x08;
        // IHDR chunk type
        pngData[12] = 0x49;
        pngData[13] = 0x48;
        pngData[14] = 0x44;
        pngData[15] = 0x52;
        // Width: 800 (0x320)
        pngData[16] = 0x00;
        pngData[17] = 0x00;
        pngData[18] = 0x03;
        pngData[19] = 0x20;
        // Height: 600 (0x258)
        pngData[20] = 0x00;
        pngData[21] = 0x00;
        pngData[22] = 0x02;
        pngData[23] = 0x58;

        // When
        ImageExifInfo result = ImageBaseUtils.getImageDimensions(pngData);

        // Then
        assertEquals(800, result.getWidth());
        assertEquals(600, result.getHeight());
        assertEquals("png", result.getType());
    }

    @Test
    void testGetImageDimensions_JPEG() {
        // Given - Create a minimal JPEG header with SOF0 marker
        byte[] jpegData = new byte[30];
        // JPEG signature
        jpegData[0] = (byte) 0xFF;
        jpegData[1] = (byte) 0xD8;
        jpegData[2] = (byte) 0xFF;
        // SOF0 marker
        jpegData[3] = (byte) 0xFF;
        jpegData[4] = (byte) 0xC0;
        // Length
        jpegData[5] = 0x00;
        jpegData[6] = 0x11;
        // Precision
        jpegData[7] = 0x08;
        // Height: 600 (0x258)
        jpegData[8] = 0x02;
        jpegData[9] = 0x58;
        // Width: 800 (0x320)
        jpegData[10] = 0x03;
        jpegData[11] = 0x20;

        // When
        ImageExifInfo result = ImageBaseUtils.getImageDimensions(jpegData);

        // Then
        assertEquals(800, result.getWidth());
        assertEquals(600, result.getHeight());
        assertEquals("jpeg", result.getType());
    }

    @Test
    void testGetImageDimensions_GIF() {
        // Given - Create a minimal GIF header
        byte[] gifData = new byte[24];
        // GIF signature
        gifData[0] = (byte) 0x47; // G
        gifData[1] = (byte) 0x49; // I
        gifData[2] = (byte) 0x46; // F
        gifData[3] = (byte) 0x38; // 8
        gifData[4] = (byte) 0x39; // 9
        gifData[5] = (byte) 0x61; // a
        // Width: 800 (little endian: 0x20 0x03)
        gifData[6] = 0x20;
        gifData[7] = 0x03;
        // Height: 600 (little endian: 0x58 0x02)
        gifData[8] = 0x58;
        gifData[9] = 0x02;

        // When
        ImageExifInfo result = ImageBaseUtils.getImageDimensions(gifData);

        // Then
        assertEquals(800, result.getWidth());
        assertEquals(600, result.getHeight());
        assertEquals("gif", result.getType());
    }

    @Test
    void testGetImageDimensions_BMP() {
        // Given - Create a minimal BMP header
        byte[] bmpData = new byte[30];
        // BMP signature
        bmpData[0] = (byte) 0x42; // B
        bmpData[1] = (byte) 0x4D; // M
        // Skip file size and reserved fields (bytes 2-17)
        // Width: 800 (little endian at offset 18)
        bmpData[18] = 0x20;
        bmpData[19] = 0x03;
        bmpData[20] = 0x00;
        bmpData[21] = 0x00;
        // Height: 600 (little endian at offset 22)
        bmpData[22] = 0x58;
        bmpData[23] = 0x02;
        bmpData[24] = 0x00;
        bmpData[25] = 0x00;

        // When
        ImageExifInfo result = ImageBaseUtils.getImageDimensions(bmpData);

        // Then
        assertEquals(800, result.getWidth());
        assertEquals(600, result.getHeight());
        assertEquals("bmp", result.getType());
    }

    @Test
    void testGetImageDimensions_WebP_VP8() {
        // Given - Create a minimal WebP VP8 header
        byte[] webpData = new byte[30];
        // RIFF signature
        webpData[0] = (byte) 0x52; // R
        webpData[1] = (byte) 0x49; // I
        webpData[2] = (byte) 0x46; // F
        webpData[3] = (byte) 0x46; // F
        // File size (skip 4 bytes)
        // WEBP signature
        webpData[8] = (byte) 0x57;  // W
        webpData[9] = (byte) 0x45;  // E
        webpData[10] = (byte) 0x42; // B
        webpData[11] = (byte) 0x50; // P
        // VP8 chunk
        webpData[12] = (byte) 0x56; // V
        webpData[13] = (byte) 0x50; // P
        webpData[14] = (byte) 0x38; // 8
        webpData[15] = (byte) 0x20; // space
        // Skip chunk size (4 bytes)
        // Skip frame tag (3 bytes)
        // Skip sync code (3 bytes)
        // Width and height at offset 26-29 (14 bits each)
        webpData[26] = 0x1F; // Width: 800 & 0x3FFF = 0x31F
        webpData[27] = 0x03;
        webpData[28] = 0x57; // Height: 600 & 0x3FFF = 0x257
        webpData[29] = 0x02;

        // When
        ImageExifInfo result = ImageBaseUtils.getImageDimensions(webpData);

        // Then
        assertEquals(799, result.getWidth()); // VP8 format stores width-1
        assertEquals(599, result.getHeight()); // VP8 format stores height-1
        assertEquals("webp", result.getType());
    }

    @Test
    void testGetImageDimensions_TIFF_BigEndian() {
        // Given - Create a minimal TIFF header (big endian)
        byte[] tiffData = new byte[30];
        // TIFF signature (big endian)
        tiffData[0] = (byte) 0x4D; // M
        tiffData[1] = (byte) 0x4D; // M
        tiffData[2] = (byte) 0x00;
        tiffData[3] = (byte) 0x2A;
        // IFD offset: 8
        tiffData[4] = 0x00;
        tiffData[5] = 0x00;
        tiffData[6] = 0x00;
        tiffData[7] = 0x08;
        // Number of IFD entries: 2
        tiffData[8] = 0x00;
        tiffData[9] = 0x02;
        // First entry: ImageWidth (0x100)
        tiffData[10] = 0x01; // tag high
        tiffData[11] = 0x00; // tag low
        // Skip type and count (4 bytes)
        // Width value: 800
        tiffData[18] = 0x00;
        tiffData[19] = 0x00;
        tiffData[20] = 0x03;
        tiffData[21] = 0x20;
        // Second entry: ImageLength (0x101)
        tiffData[22] = 0x01; // tag high
        tiffData[23] = 0x01; // tag low
        // Skip type and count (4 bytes)
        // Height value: 600 (at offset 30, but we need to extend array)

        // Extend array to accommodate height value
        byte[] extendedTiffData = new byte[34];
        System.arraycopy(tiffData, 0, extendedTiffData, 0, tiffData.length);
        extendedTiffData[30] = 0x00;
        extendedTiffData[31] = 0x00;
        extendedTiffData[32] = 0x02;
        extendedTiffData[33] = 0x58;

        // When
        ImageExifInfo result = ImageBaseUtils.getImageDimensions(extendedTiffData);

        // Then
        assertEquals(800, result.getWidth());
        assertEquals(600, result.getHeight());
        assertEquals("tiff", result.getType());
    }
}
