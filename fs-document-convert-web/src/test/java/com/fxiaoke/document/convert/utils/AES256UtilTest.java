package com.fxiaoke.document.convert.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.*;

class AES256UtilTest {

    @Test
    void testEncode_SimpleString() {
        // Given
        String source = "Hello World";

        // When
        String encoded = AES256Util.encode(source);

        // Then
        assertNotNull(encoded);
        assertFalse(encoded.isEmpty());
        assertTrue(encoded.matches("[0-9A-F]+"));
    }

    @Test
    void testEncode_EmptyString() {
        // Given
        String source = "";

        // When
        String encoded = AES256Util.encode(source);

        // Then
        assertNotNull(encoded);
        assertTrue(encoded.matches("[0-9A-F]+"));
    }

    @Test
    void testDecode_ValidHexString() {
        // Given
        String source = "Hello World";
        String encoded = AES256Util.encode(source);

        // When
        String decoded = AES256Util.decode(encoded);

        // Then
        assertEquals(source, decoded);
    }

    @Test
    void testDecode_InvalidHexString() {
        // Given
        String invalidHex = "INVALID_HEX_STRING";

        // When & Then
        assertThrows(RuntimeException.class, () -> AES256Util.decode(invalidHex));
    }

    @Test
    void testParseByte2HexStr_ValidBytes() {
        // Given
        byte[] bytes = {0x01, 0x23, 0x45, 0x67, (byte) 0x89, (byte) 0xAB};

        // When
        String hexStr = AES256Util.parseByte2HexStr(bytes);

        // Then
        assertEquals("0123456789AB", hexStr);
    }

    @Test
    void testParseByte2HexStr_EmptyBytes() {
        // Given
        byte[] bytes = {};

        // When
        String hexStr = AES256Util.parseByte2HexStr(bytes);

        // Then
        assertEquals("", hexStr);
    }

    @Test
    void testParseHexStr2Byte_ValidHexString() {
        // Given
        String hexStr = "0123456789AB";

        // When
        byte[] bytes = AES256Util.parseHexStr2Byte(hexStr);

        // Then
        assertNotNull(bytes);
        assertEquals(6, bytes.length);
        assertEquals(0x01, bytes[0] & 0xFF);
        assertEquals(0x23, bytes[1] & 0xFF);
        assertEquals(0x45, bytes[2] & 0xFF);
        assertEquals(0x67, bytes[3] & 0xFF);
        assertEquals(0x89, bytes[4] & 0xFF);
        assertEquals(0xAB, bytes[5] & 0xFF);
    }

    @Test
    void testParseHexStr2Byte_EmptyString() {
        // Given
        String hexStr = "";

        // When
        byte[] bytes = AES256Util.parseHexStr2Byte(hexStr);

        // Then
        assertNull(bytes);
    }

    @ParameterizedTest
    @ValueSource(strings = {"test", "hello world", "123456", "测试中文"})
    void testEncodeDecodeRoundTrip(String original) {
        // When
        String encoded = AES256Util.encode(original);
        String decoded = AES256Util.decode(encoded);

        // Then
        assertEquals(original, decoded);
    }

    @Test
    void testStaticFieldsInitialization() {
        // Test that static fields are properly initialized
        assertNotNull(AES256Util.aesKey);
        assertNotNull(AES256Util.aesIv);
        assertNotNull(AES256Util.decrypt_c);
        assertNotNull(AES256Util.encrypt_c);
        
        assertEquals("nirtHUNF/Ct8J7sf40VaIQui0N5r8gcbxGXKxRhu1C4=", AES256Util.aesKey);
        assertEquals("jwNz4Ia8OHVpPyEXIQjJ2g==", AES256Util.aesIv);
    }
}
