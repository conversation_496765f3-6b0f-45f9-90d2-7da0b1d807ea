package com.fxiaoke.document.convert.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.*;

class BrowserUtilsTest {

    @ParameterizedTest
    @CsvSource({
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15, attachment, test.pdf",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36, attachment, test.pdf",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0, attachment, test.pdf"
    })
    void testGetDispositionName_DifferentBrowsers(String userAgent, String acModel, String filename) {
        // When
        String result = BrowserUtils.getDispositionName(userAgent, acModel, filename);

        // Then
        assertNotNull(result);
        assertTrue(result.startsWith(acModel));
        assertTrue(result.contains("filename="));
        assertTrue(result.contains("filename*=utf-8''"));
    }

    @Test
    void testGetDispositionName_SafariBrowser() {
        // Given
        String safariUserAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15";
        String acModel = "attachment";
        String filename = "测试文件.pdf";

        // When
        String result = BrowserUtils.getDispositionName(safariUserAgent, acModel, filename);

        // Then
        assertNotNull(result);
        assertTrue(result.startsWith("attachment"));
        assertTrue(result.contains("filename="));
        assertTrue(result.contains("filename*=utf-8''"));
    }

    @Test
    void testGetDispositionName_ChromeBrowser() {
        // Given
        String chromeUserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";
        String acModel = "attachment";
        String filename = "测试文件.pdf";

        // When
        String result = BrowserUtils.getDispositionName(chromeUserAgent, acModel, filename);

        // Then
        assertNotNull(result);
        assertTrue(result.startsWith("attachment"));
        assertTrue(result.contains("filename="));
        assertTrue(result.contains("filename*=utf-8''"));
    }

    @Test
    void testGetDispositionName_FirefoxBrowser() {
        // Given
        String firefoxUserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0";
        String acModel = "inline";
        String filename = "document.pdf";

        // When
        String result = BrowserUtils.getDispositionName(firefoxUserAgent, acModel, filename);

        // Then
        assertNotNull(result);
        assertTrue(result.startsWith("inline"));
        assertTrue(result.contains("filename="));
        assertTrue(result.contains("filename*=utf-8''"));
    }

    @Test
    void testGetDispositionName_NullUserAgent() {
        // Given
        String userAgent = null;
        String acModel = "attachment";
        String filename = "test.pdf";

        // When
        String result = BrowserUtils.getDispositionName(userAgent, acModel, filename);

        // Then
        assertNotNull(result);
        assertTrue(result.startsWith("attachment"));
        assertTrue(result.contains("filename="));
        assertTrue(result.contains("filename*=utf-8''"));
    }

    @Test
    void testGetDispositionName_EmptyUserAgent() {
        // Given
        String userAgent = "";
        String acModel = "attachment";
        String filename = "test.pdf";

        // When
        String result = BrowserUtils.getDispositionName(userAgent, acModel, filename);

        // Then
        assertNotNull(result);
        assertTrue(result.startsWith("attachment"));
        assertTrue(result.contains("filename="));
        assertTrue(result.contains("filename*=utf-8''"));
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "test.pdf",
        "document with spaces.docx",
        "文件名.txt",
        "file@#$%^&*().png",
        "very-long-filename-with-many-characters-and-numbers-123456789.xlsx"
    })
    void testGetDispositionName_VariousFilenames(String filename) {
        // Given
        String userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
        String acModel = "attachment";

        // When
        String result = BrowserUtils.getDispositionName(userAgent, acModel, filename);

        // Then
        assertNotNull(result);
        assertTrue(result.startsWith("attachment"));
        assertTrue(result.contains("filename="));
        assertTrue(result.contains("filename*=utf-8''"));
    }

    @Test
    void testGetDispositionName_SafariVsChrome_DifferentEncoding() {
        // Given
        String safariUserAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15";
        String chromeUserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";
        String acModel = "attachment";
        String filename = "测试文件.pdf";

        // When
        String safariResult = BrowserUtils.getDispositionName(safariUserAgent, acModel, filename);
        String chromeResult = BrowserUtils.getDispositionName(chromeUserAgent, acModel, filename);

        // Then
        assertNotNull(safariResult);
        assertNotNull(chromeResult);
        // Both should contain the basic structure but might have different encoding
        assertTrue(safariResult.contains("filename="));
        assertTrue(chromeResult.contains("filename="));
        assertTrue(safariResult.contains("filename*=utf-8''"));
        assertTrue(chromeResult.contains("filename*=utf-8''"));
    }

    @Test
    void testGetDispositionName_EdgeBrowser() {
        // Given
        String edgeUserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59";
        String acModel = "attachment";
        String filename = "test.pdf";

        // When
        String result = BrowserUtils.getDispositionName(edgeUserAgent, acModel, filename);

        // Then
        assertNotNull(result);
        assertTrue(result.startsWith("attachment"));
        assertTrue(result.contains("filename="));
        assertTrue(result.contains("filename*=utf-8''"));
    }

    @Test
    void testGetDispositionName_MobileUserAgent() {
        // Given
        String mobileUserAgent = "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1";
        String acModel = "attachment";
        String filename = "mobile-test.pdf";

        // When
        String result = BrowserUtils.getDispositionName(mobileUserAgent, acModel, filename);

        // Then
        assertNotNull(result);
        assertTrue(result.startsWith("attachment"));
        assertTrue(result.contains("filename="));
        assertTrue(result.contains("filename*=utf-8''"));
    }
}
