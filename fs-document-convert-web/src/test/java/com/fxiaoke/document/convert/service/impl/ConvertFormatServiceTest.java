package com.fxiaoke.document.convert.service.impl;

import com.fxiaoke.document.convert.domain.exception.BaseException;
import com.fxiaoke.document.convert.domain.preview.ConvertFormat;
import com.fxiaoke.document.convert.process.CellsProcess;
import com.fxiaoke.document.convert.process.PdfProcess;
import com.fxiaoke.document.convert.process.SlidesProcess;
import com.fxiaoke.document.convert.process.WordsProcess;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ConvertFormatServiceTest {

    @Mock
    private CellsProcess cellsProcess;

    @Mock
    private SlidesProcess slidesProcess;

    @Mock
    private WordsProcess wordsProcess;

    @Mock
    private PdfProcess pdfProcess;

    private ConvertFormatService convertFormatService;

    private static final String TEST_EA = "testEa";
    private static final String TEST_PATH = "test/document.pdf";
    private static final String TEST_FILE_PATH = "/tmp/test.pdf";
    private static final int TEST_PAGE_INDEX = 1;
    private static final int TEST_BEGIN_PAGE = 1;
    private static final int TEST_END_PAGE = 5;
    private static final int TEST_PAGE_COUNT = 10;
    private static final List<String> TEST_RESULT = Arrays.asList("converted_result");

    @BeforeEach
    void setUp() {
        convertFormatService = new ConvertFormatService(cellsProcess, slidesProcess, wordsProcess, pdfProcess);
    }

    // Test OneArg conversions
    @Test
    void testConvertFormat_OneArg_PDF_TO_PNG_Success() {
        // Given
        ConvertFormat.OneArg arg = new ConvertFormat.OneArg();
        arg.setTargetFormat(ConvertFormat.Operation.PDF_TO_PNG);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setPageIndex(TEST_PAGE_INDEX);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(pdfProcess.toPng(TEST_FILE_PATH, TEST_PAGE_INDEX, TEST_EA, TEST_PATH))
            .thenReturn(TEST_RESULT);

        // When
        ConvertFormat.Result result = convertFormatService.convertFormat(arg);

        // Then
        assertNotNull(result);
        assertEquals(TEST_RESULT, result.getResult());
        verify(pdfProcess).toPng(TEST_FILE_PATH, TEST_PAGE_INDEX, TEST_EA, TEST_PATH);
    }

    @Test
    void testConvertFormat_OneArg_PDF_TO_HTML_Success() {
        // Given
        ConvertFormat.OneArg arg = new ConvertFormat.OneArg();
        arg.setTargetFormat(ConvertFormat.Operation.PDF_TO_HTML);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setPageIndex(TEST_PAGE_INDEX);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(pdfProcess.toHtml(TEST_FILE_PATH, TEST_PAGE_INDEX, TEST_EA, TEST_PATH))
            .thenReturn(TEST_RESULT);

        // When
        ConvertFormat.Result result = convertFormatService.convertFormat(arg);

        // Then
        assertNotNull(result);
        assertEquals(TEST_RESULT, result.getResult());
        verify(pdfProcess).toHtml(TEST_FILE_PATH, TEST_PAGE_INDEX, TEST_EA, TEST_PATH);
    }

    @Test
    void testConvertFormat_OneArg_WORDS_TO_PNG_Success() {
        // Given
        ConvertFormat.OneArg arg = new ConvertFormat.OneArg();
        arg.setTargetFormat(ConvertFormat.Operation.WORDS_TO_PNG);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setPageIndex(TEST_PAGE_INDEX);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(wordsProcess.toPng(TEST_FILE_PATH, TEST_PAGE_INDEX, TEST_EA, TEST_PATH))
            .thenReturn(TEST_RESULT);

        // When
        ConvertFormat.Result result = convertFormatService.convertFormat(arg);

        // Then
        assertNotNull(result);
        assertEquals(TEST_RESULT, result.getResult());
        verify(wordsProcess).toPng(TEST_FILE_PATH, TEST_PAGE_INDEX, TEST_EA, TEST_PATH);
    }

    @Test
    void testConvertFormat_OneArg_WORDS_TO_HTML_Success() {
        // Given
        ConvertFormat.OneArg arg = new ConvertFormat.OneArg();
        arg.setTargetFormat(ConvertFormat.Operation.WORDS_TO_HTML);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setPageIndex(TEST_PAGE_INDEX);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(wordsProcess.toHtml(TEST_FILE_PATH, TEST_PAGE_INDEX, TEST_EA, TEST_PATH))
            .thenReturn(TEST_RESULT);

        // When
        ConvertFormat.Result result = convertFormatService.convertFormat(arg);

        // Then
        assertNotNull(result);
        assertEquals(TEST_RESULT, result.getResult());
        verify(wordsProcess).toHtml(TEST_FILE_PATH, TEST_PAGE_INDEX, TEST_EA, TEST_PATH);
    }

    @Test
    void testConvertFormat_OneArg_PPT_TO_PNG_Success() {
        // Given
        ConvertFormat.OneArg arg = new ConvertFormat.OneArg();
        arg.setTargetFormat(ConvertFormat.Operation.PPT_TO_PNG);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setPageIndex(TEST_PAGE_INDEX);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(slidesProcess.toPng(TEST_FILE_PATH, TEST_PAGE_INDEX, TEST_EA, TEST_PATH))
            .thenReturn(TEST_RESULT);

        // When
        ConvertFormat.Result result = convertFormatService.convertFormat(arg);

        // Then
        assertNotNull(result);
        assertEquals(TEST_RESULT, result.getResult());
        verify(slidesProcess).toPng(TEST_FILE_PATH, TEST_PAGE_INDEX, TEST_EA, TEST_PATH);
    }

    @Test
    void testConvertFormat_OneArg_PPT_TO_HTML_Success() {
        // Given
        ConvertFormat.OneArg arg = new ConvertFormat.OneArg();
        arg.setTargetFormat(ConvertFormat.Operation.PPT_TO_HTML);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setPageIndex(TEST_PAGE_INDEX);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(slidesProcess.toHtml(TEST_FILE_PATH, TEST_PAGE_INDEX, TEST_EA, TEST_PATH))
            .thenReturn(TEST_RESULT);

        // When
        ConvertFormat.Result result = convertFormatService.convertFormat(arg);

        // Then
        assertNotNull(result);
        assertEquals(TEST_RESULT, result.getResult());
        verify(slidesProcess).toHtml(TEST_FILE_PATH, TEST_PAGE_INDEX, TEST_EA, TEST_PATH);
    }

    @Test
    void testConvertFormat_OneArg_EXCEL_TO_HTML_Success() {
        // Given
        ConvertFormat.OneArg arg = new ConvertFormat.OneArg();
        arg.setTargetFormat(ConvertFormat.Operation.EXCEL_TO_HTML);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setPageIndex(TEST_PAGE_INDEX);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(cellsProcess.toHtml(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_PAGE_INDEX))
            .thenReturn(TEST_RESULT);

        // When
        ConvertFormat.Result result = convertFormatService.convertFormat(arg);

        // Then
        assertNotNull(result);
        assertEquals(TEST_RESULT, result.getResult());
        verify(cellsProcess).toHtml(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_PAGE_INDEX);
    }

    // Test RangeArg conversions
    @Test
    void testConvertFormat_RangeArg_PDF_TO_PNG_Success() {
        // Given
        ConvertFormat.RangeArg arg = new ConvertFormat.RangeArg();
        arg.setTargetFormat(ConvertFormat.Operation.PDF_TO_PNG);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setBeginPageIndex(TEST_BEGIN_PAGE);
        arg.setEndPageIndex(TEST_END_PAGE);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(pdfProcess.toPng(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_BEGIN_PAGE, TEST_END_PAGE))
            .thenReturn(TEST_RESULT);

        // When
        ConvertFormat.Result result = convertFormatService.convertFormat(arg);

        // Then
        assertNotNull(result);
        assertEquals(TEST_RESULT, result.getResult());
        verify(pdfProcess).toPng(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_BEGIN_PAGE, TEST_END_PAGE);
    }

    @Test
    void testConvertFormat_RangeArg_PDF_TO_HTML_Success() {
        // Given
        ConvertFormat.RangeArg arg = new ConvertFormat.RangeArg();
        arg.setTargetFormat(ConvertFormat.Operation.PDF_TO_HTML);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setBeginPageIndex(TEST_BEGIN_PAGE);
        arg.setEndPageIndex(TEST_END_PAGE);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(pdfProcess.toHtml(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_BEGIN_PAGE, TEST_END_PAGE))
            .thenReturn(TEST_RESULT);

        // When
        ConvertFormat.Result result = convertFormatService.convertFormat(arg);

        // Then
        assertNotNull(result);
        assertEquals(TEST_RESULT, result.getResult());
        verify(pdfProcess).toHtml(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_BEGIN_PAGE, TEST_END_PAGE);
    }

    @Test
    void testConvertFormat_RangeArg_WORDS_TO_PNG_Success() {
        // Given
        ConvertFormat.RangeArg arg = new ConvertFormat.RangeArg();
        arg.setTargetFormat(ConvertFormat.Operation.WORDS_TO_PNG);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setBeginPageIndex(TEST_BEGIN_PAGE);
        arg.setEndPageIndex(TEST_END_PAGE);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(wordsProcess.toPng(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_BEGIN_PAGE, TEST_END_PAGE))
            .thenReturn(TEST_RESULT);

        // When
        ConvertFormat.Result result = convertFormatService.convertFormat(arg);

        // Then
        assertNotNull(result);
        assertEquals(TEST_RESULT, result.getResult());
        verify(wordsProcess).toPng(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_BEGIN_PAGE, TEST_END_PAGE);
    }

    @Test
    void testConvertFormat_RangeArg_WORDS_TO_HTML_Success() {
        // Given
        ConvertFormat.RangeArg arg = new ConvertFormat.RangeArg();
        arg.setTargetFormat(ConvertFormat.Operation.WORDS_TO_HTML);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setBeginPageIndex(TEST_BEGIN_PAGE);
        arg.setEndPageIndex(TEST_END_PAGE);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(wordsProcess.toHtml(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_BEGIN_PAGE, TEST_END_PAGE))
            .thenReturn(TEST_RESULT);

        // When
        ConvertFormat.Result result = convertFormatService.convertFormat(arg);

        // Then
        assertNotNull(result);
        assertEquals(TEST_RESULT, result.getResult());
        verify(wordsProcess).toHtml(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_BEGIN_PAGE, TEST_END_PAGE);
    }

    @Test
    void testConvertFormat_RangeArg_PPT_TO_PNG_Success() {
        // Given
        ConvertFormat.RangeArg arg = new ConvertFormat.RangeArg();
        arg.setTargetFormat(ConvertFormat.Operation.PPT_TO_PNG);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setBeginPageIndex(TEST_BEGIN_PAGE);
        arg.setEndPageIndex(TEST_END_PAGE);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(slidesProcess.toPng(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_BEGIN_PAGE, TEST_END_PAGE))
            .thenReturn(TEST_RESULT);

        // When
        ConvertFormat.Result result = convertFormatService.convertFormat(arg);

        // Then
        assertNotNull(result);
        assertEquals(TEST_RESULT, result.getResult());
        verify(slidesProcess).toPng(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_BEGIN_PAGE, TEST_END_PAGE);
    }

    @Test
    void testConvertFormat_RangeArg_PPT_TO_HTML_Success() {
        // Given
        ConvertFormat.RangeArg arg = new ConvertFormat.RangeArg();
        arg.setTargetFormat(ConvertFormat.Operation.PPT_TO_HTML);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setBeginPageIndex(TEST_BEGIN_PAGE);
        arg.setEndPageIndex(TEST_END_PAGE);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(slidesProcess.toHtml(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_BEGIN_PAGE, TEST_END_PAGE))
            .thenReturn(TEST_RESULT);

        // When
        ConvertFormat.Result result = convertFormatService.convertFormat(arg);

        // Then
        assertNotNull(result);
        assertEquals(TEST_RESULT, result.getResult());
        verify(slidesProcess).toHtml(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_BEGIN_PAGE, TEST_END_PAGE);
    }

    // Test AllArg conversions
    @Test
    void testConvertFormat_AllArg_PDF_TO_PNG_Success() {
        // Given
        ConvertFormat.AllArg arg = new ConvertFormat.AllArg();
        arg.setTargetFormat(ConvertFormat.Operation.PDF_TO_PNG);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setPageCount(TEST_PAGE_COUNT);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(pdfProcess.toPng(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_PAGE_COUNT))
            .thenReturn(TEST_RESULT);

        // When
        ConvertFormat.Result result = convertFormatService.convertFormat(arg);

        // Then
        assertNotNull(result);
        assertEquals(TEST_RESULT, result.getResult());
        verify(pdfProcess).toPng(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_PAGE_COUNT);
    }

    @Test
    void testConvertFormat_AllArg_PDF_TO_HTML_Success() {
        // Given
        ConvertFormat.AllArg arg = new ConvertFormat.AllArg();
        arg.setTargetFormat(ConvertFormat.Operation.PDF_TO_HTML);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setPageCount(TEST_PAGE_COUNT);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(pdfProcess.toHtml(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_PAGE_COUNT))
            .thenReturn(TEST_RESULT);

        // When
        ConvertFormat.Result result = convertFormatService.convertFormat(arg);

        // Then
        assertNotNull(result);
        assertEquals(TEST_RESULT, result.getResult());
        verify(pdfProcess).toHtml(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_PAGE_COUNT);
    }

    @Test
    void testConvertFormat_AllArg_WORDS_TO_PNG_Success() {
        // Given
        ConvertFormat.AllArg arg = new ConvertFormat.AllArg();
        arg.setTargetFormat(ConvertFormat.Operation.WORDS_TO_PNG);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setPageCount(TEST_PAGE_COUNT);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(wordsProcess.toPng(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_PAGE_COUNT))
            .thenReturn(TEST_RESULT);

        // When
        ConvertFormat.Result result = convertFormatService.convertFormat(arg);

        // Then
        assertNotNull(result);
        assertEquals(TEST_RESULT, result.getResult());
        verify(wordsProcess).toPng(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_PAGE_COUNT);
    }

    @Test
    void testConvertFormat_AllArg_WORDS_TO_HTML_Success() {
        // Given
        ConvertFormat.AllArg arg = new ConvertFormat.AllArg();
        arg.setTargetFormat(ConvertFormat.Operation.WORDS_TO_HTML);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setPageCount(TEST_PAGE_COUNT);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(wordsProcess.toHtml(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_PAGE_COUNT))
            .thenReturn(TEST_RESULT);

        // When
        ConvertFormat.Result result = convertFormatService.convertFormat(arg);

        // Then
        assertNotNull(result);
        assertEquals(TEST_RESULT, result.getResult());
        verify(wordsProcess).toHtml(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_PAGE_COUNT);
    }

    @Test
    void testConvertFormat_AllArg_PPT_TO_PNG_Success() {
        // Given
        ConvertFormat.AllArg arg = new ConvertFormat.AllArg();
        arg.setTargetFormat(ConvertFormat.Operation.PPT_TO_PNG);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setPageCount(TEST_PAGE_COUNT);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(slidesProcess.toPng(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_PAGE_COUNT))
            .thenReturn(TEST_RESULT);

        // When
        ConvertFormat.Result result = convertFormatService.convertFormat(arg);

        // Then
        assertNotNull(result);
        assertEquals(TEST_RESULT, result.getResult());
        verify(slidesProcess).toPng(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_PAGE_COUNT);
    }

    @Test
    void testConvertFormat_AllArg_PPT_TO_HTML_Success() {
        // Given
        ConvertFormat.AllArg arg = new ConvertFormat.AllArg();
        arg.setTargetFormat(ConvertFormat.Operation.PPT_TO_HTML);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setPageCount(TEST_PAGE_COUNT);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(slidesProcess.toHtml(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_PAGE_COUNT))
            .thenReturn(TEST_RESULT);

        // When
        ConvertFormat.Result result = convertFormatService.convertFormat(arg);

        // Then
        assertNotNull(result);
        assertEquals(TEST_RESULT, result.getResult());
        verify(slidesProcess).toHtml(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_PAGE_COUNT);
    }

    @Test
    void testConstructor_AllDependenciesInjected() {
        // When
        ConvertFormatService service = new ConvertFormatService(cellsProcess, slidesProcess, wordsProcess, pdfProcess);

        // Then
        assertNotNull(service);
        // Verify that all dependencies are properly injected by calling a method
        ConvertFormat.OneArg arg = new ConvertFormat.OneArg();
        arg.setTargetFormat(ConvertFormat.Operation.PDF_TO_PNG);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setPageIndex(TEST_PAGE_INDEX);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(pdfProcess.toPng(anyString(), anyInt(), anyString(), anyString())).thenReturn("test");
        ConvertFormat.Result result = service.convertFormat(arg);
        assertNotNull(result);
    }

    // Exception tests
    @Test
    void testConvertFormat_OneArg_ProcessThrowsException_PropagatesException() {
        // Given
        ConvertFormat.OneArg arg = new ConvertFormat.OneArg();
        arg.setTargetFormat(ConvertFormat.Operation.PDF_TO_PNG);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setPageIndex(TEST_PAGE_INDEX);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(pdfProcess.toPng(TEST_FILE_PATH, TEST_PAGE_INDEX, TEST_EA, TEST_PATH))
            .thenThrow(new RuntimeException("PDF processing failed"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class,
            () -> convertFormatService.convertFormat(arg));

        assertEquals("PDF processing failed", exception.getMessage());
        verify(pdfProcess).toPng(TEST_FILE_PATH, TEST_PAGE_INDEX, TEST_EA, TEST_PATH);
    }

    @Test
    void testConvertFormat_RangeArg_ProcessThrowsException_PropagatesException() {
        // Given
        ConvertFormat.RangeArg arg = new ConvertFormat.RangeArg();
        arg.setTargetFormat(ConvertFormat.Operation.WORDS_TO_HTML);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setBeginPageIndex(TEST_BEGIN_PAGE);
        arg.setEndPageIndex(TEST_END_PAGE);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(wordsProcess.toHtml(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_BEGIN_PAGE, TEST_END_PAGE))
            .thenThrow(new RuntimeException("Words processing failed"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class,
            () -> convertFormatService.convertFormat(arg));

        assertEquals("Words processing failed", exception.getMessage());
        verify(wordsProcess).toHtml(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_BEGIN_PAGE, TEST_END_PAGE);
    }

    @Test
    void testConvertFormat_AllArg_ProcessThrowsException_PropagatesException() {
        // Given
        ConvertFormat.AllArg arg = new ConvertFormat.AllArg();
        arg.setTargetFormat(ConvertFormat.Operation.PPT_TO_PNG);
        arg.setFilePath(TEST_FILE_PATH);
        arg.setPageCount(TEST_PAGE_COUNT);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        when(slidesProcess.toPng(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_PAGE_COUNT))
            .thenThrow(new RuntimeException("Slides processing failed"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class,
            () -> convertFormatService.convertFormat(arg));

        assertEquals("Slides processing failed", exception.getMessage());
        verify(slidesProcess).toPng(TEST_EA, TEST_PATH, TEST_FILE_PATH, TEST_PAGE_COUNT);
    }

    @Test
    void testConvertFormat_RangeArg_UnsupportedOperation_ThrowsException() {
        // Given
        ConvertFormat.RangeArg arg = new ConvertFormat.RangeArg();
        arg.setTargetFormat(ConvertFormat.Operation.EXCEL_TO_HTML); // Not supported in RangeArg
        arg.setFilePath(TEST_FILE_PATH);
        arg.setBeginPageIndex(TEST_BEGIN_PAGE);
        arg.setEndPageIndex(TEST_END_PAGE);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        // When & Then
        BaseException exception = assertThrows(BaseException.class,
            () -> convertFormatService.convertFormat(arg));

        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("不支持的操作类型"));
        assertTrue(exception.getModule().contains("ConvertFormatService-convertFormat"));
    }

    @Test
    void testConvertFormat_AllArg_UnsupportedOperation_ThrowsException() {
        // Given
        ConvertFormat.AllArg arg = new ConvertFormat.AllArg();
        arg.setTargetFormat(ConvertFormat.Operation.EXCEL_TO_HTML); // Not supported in AllArg
        arg.setFilePath(TEST_FILE_PATH);
        arg.setPageCount(TEST_PAGE_COUNT);
        arg.setEa(TEST_EA);
        arg.setPath(TEST_PATH);

        // When & Then
        BaseException exception = assertThrows(BaseException.class,
            () -> convertFormatService.convertFormat(arg));

        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("不支持的操作类型"));
        assertTrue(exception.getModule().contains("ConvertFormatService-convertFormat"));
    }

    @Test
    void testConvertFormat_OneArg_NullArg_ThrowsException() {
        // When & Then
        assertThrows(NullPointerException.class,
            () -> convertFormatService.convertFormat((ConvertFormat.OneArg) null));
    }

    @Test
    void testConvertFormat_RangeArg_NullArg_ThrowsException() {
        // When & Then
        assertThrows(NullPointerException.class,
            () -> convertFormatService.convertFormat((ConvertFormat.RangeArg) null));
    }

    @Test
    void testConvertFormat_AllArg_NullArg_ThrowsException() {
        // When & Then
        assertThrows(NullPointerException.class,
            () -> convertFormatService.convertFormat((ConvertFormat.AllArg) null));
    }
}
