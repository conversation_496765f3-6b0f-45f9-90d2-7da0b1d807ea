package com.fxiaoke.document.convert.process.options;

import com.aspose.words.Document;
import com.aspose.words.HtmlFixedSaveOptions;
import com.aspose.words.ImageSaveOptions;
import com.aspose.words.LoadOptions;
import com.aspose.words.PdfSaveOptions;
import com.fxiaoke.document.convert.dao.impl.PreviewInfoDao;
import com.fxiaoke.document.convert.domain.exception.BaseException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class WordsOptionsTest {

    @Mock
    private PreviewInfoDao previewInfoDao;

    @Mock
    private Document mockDocument;

    private WordsOptions wordsOptions;

    @TempDir
    Path tempDir;

    private String testFilePath;
    private String testEa = "testEa";
    private String testPath = "testPath";

    @BeforeEach
    void setUp() throws IOException {
        wordsOptions = new WordsOptions(previewInfoDao);

        // Create a test file
        Path testFile = tempDir.resolve("test.docx");
        Files.write(testFile, "test content".getBytes());
        testFilePath = testFile.toString();
    }

    @Test
    void testGetLoadOptions() {
        // When
        LoadOptions loadOptions = wordsOptions.getLoadOptions();

        // Then
        assertNotNull(loadOptions);
        assertTrue(loadOptions.getConvertMetafilesToPng());
        assertTrue(loadOptions.getIgnoreOleData());
    }

    @Test
    void testInit_Success() {
        // Given
        try (MockedStatic<Document> documentMock = mockStatic(Document.class)) {
            documentMock.when(() -> new Document(eq(testFilePath), any(LoadOptions.class)))
                    .thenReturn(mockDocument);

            // When
            Document result = wordsOptions.init(testFilePath);

            // Then
            assertNotNull(result);
            assertEquals(mockDocument, result);
        }
    }

    @Test
    void testInit_ThrowsException() {
        // Given
        try (MockedStatic<Document> documentMock = mockStatic(Document.class)) {
            documentMock.when(() -> new Document(eq(testFilePath), any(LoadOptions.class)))
                    .thenThrow(new RuntimeException("Test exception"));

            // When & Then
            BaseException exception = assertThrows(BaseException.class,
                    () -> wordsOptions.init(testFilePath));

            assertEquals(400, exception.getCode());
            assertTrue(exception.getMessage().contains("Word 文件已加密"));
        }
    }

    @Test
    void testIsCheckEncrypt_Success() {
        // Given
        try (MockedStatic<com.aspose.words.FileFormatUtil> fileFormatUtilMock =
                mockStatic(com.aspose.words.FileFormatUtil.class)) {
            com.aspose.words.FileFormatInfo mockFormatInfo = mock(com.aspose.words.FileFormatInfo.class);
            fileFormatUtilMock.when(() -> com.aspose.words.FileFormatUtil.detectFileFormat(testFilePath))
                    .thenReturn(mockFormatInfo);
            when(mockFormatInfo.isEncrypted()).thenReturn(false);

            // When & Then - Should not throw exception
            assertDoesNotThrow(() -> wordsOptions.isCheckEncrypt(testFilePath));
        }
    }

    @Test
    void testIsCheckEncrypt_ThrowsException() {
        // Given
        try (MockedStatic<com.aspose.words.FileFormatUtil> fileFormatUtilMock =
                mockStatic(com.aspose.words.FileFormatUtil.class)) {
            fileFormatUtilMock.when(() -> com.aspose.words.FileFormatUtil.detectFileFormat(testFilePath))
                    .thenThrow(new RuntimeException("Test exception"));

            // When & Then
            BaseException exception = assertThrows(BaseException.class,
                    () -> wordsOptions.isCheckEncrypt(testFilePath));

            assertEquals(400, exception.getCode());
            assertTrue(exception.getMessage().contains("Word 文件已损坏"));
        }
    }

    @Test
    void testUpgrade_FileExists() throws IOException {
        // Given
        Path upgradeFile = tempDir.resolve("test_upgrade.docx");
        Files.write(upgradeFile, "upgraded content".getBytes());
        String upgradeFilePath = upgradeFile.toString();

        try (MockedStatic<com.fxiaoke.document.convert.utils.FileNameUtil> fileNameUtilMock =
                mockStatic(com.fxiaoke.document.convert.utils.FileNameUtil.class);
             MockedStatic<Files> filesMock = mockStatic(Files.class);
             MockedStatic<Path> pathMock = mockStatic(Path.class)) {

            fileNameUtilMock.when(() -> com.fxiaoke.document.convert.utils.FileNameUtil
                    .createTempFileName(testFilePath, ".docx")).thenReturn(upgradeFilePath);
            pathMock.when(() -> Path.of(upgradeFilePath)).thenReturn(upgradeFile);
            filesMock.when(() -> Files.exists(upgradeFile)).thenReturn(true);

            // When
            String result = wordsOptions.upgrade(testFilePath);

            // Then
            assertEquals(upgradeFilePath, result);
        }
    }

    @Test
    void testUpgrade_FileNotExists() throws Exception {
        // Given
        Path upgradeFile = tempDir.resolve("test_upgrade.docx");
        String upgradeFilePath = upgradeFile.toString();

        try (MockedStatic<com.fxiaoke.document.convert.utils.FileNameUtil> fileNameUtilMock =
                mockStatic(com.fxiaoke.document.convert.utils.FileNameUtil.class);
             MockedStatic<Files> filesMock = mockStatic(Files.class);
             MockedStatic<Path> pathMock = mockStatic(Path.class);
             MockedStatic<Document> documentMock = mockStatic(Document.class)) {

            fileNameUtilMock.when(() -> com.fxiaoke.document.convert.utils.FileNameUtil
                    .createTempFileName(testFilePath, ".docx")).thenReturn(upgradeFilePath);
            pathMock.when(() -> Path.of(upgradeFilePath)).thenReturn(upgradeFile);
            filesMock.when(() -> Files.exists(upgradeFile)).thenReturn(false);
            documentMock.when(() -> new Document(eq(testFilePath), any(LoadOptions.class)))
                    .thenReturn(mockDocument);

            // When
            String result = wordsOptions.upgrade(testFilePath);

            // Then
            assertEquals(upgradeFilePath, result);
            verify(mockDocument).save(eq(upgradeFilePath), any());
        }
    }

    @Test
    void testGetMetaInfo_Success() throws Exception {
        // Given
        try (MockedStatic<Document> documentMock = mockStatic(Document.class)) {
            documentMock.when(() -> new Document(eq(testFilePath), any(LoadOptions.class)))
                    .thenReturn(mockDocument);
            when(mockDocument.getPageCount()).thenReturn(5);

            // When
            int result = wordsOptions.getMetaInfo(testFilePath, testEa);

            // Then
            assertEquals(5, result);
        }
    }

    @Test
    void testGetMetaInfo_ThrowsException() throws Exception {
        // Given
        try (MockedStatic<Document> documentMock = mockStatic(Document.class)) {
            documentMock.when(() -> new Document(eq(testFilePath), any(LoadOptions.class)))
                    .thenReturn(mockDocument);
            when(mockDocument.getPageCount()).thenThrow(new RuntimeException("Test exception"));

            // When & Then
            BaseException exception = assertThrows(BaseException.class,
                    () -> wordsOptions.getMetaInfo(testFilePath, testEa));

            assertEquals(500, exception.getCode());
        }
    }

    @Test
    void testGetImageOption() {
        // When
        ImageSaveOptions imageOption = wordsOptions.getImageOption();

        // Then
        assertNotNull(imageOption);
        assertTrue(imageOption.getMemoryOptimization());
        assertFalse(imageOption.getOptimizeOutput());
        assertFalse(imageOption.getPrettyFormat());
        assertFalse(imageOption.getUseHighQualityRendering());
        assertFalse(imageOption.getAllowEmbeddingPostScriptFonts());
        assertFalse(imageOption.getExportGeneratorName());
    }

    @Test
    void testGetHtmlFixedOption() {
        // When
        HtmlFixedSaveOptions htmlOption = wordsOptions.getHtmlFixedOption();

        // Then
        assertNotNull(htmlOption);
        assertFalse(htmlOption.getOptimizeOutput());
        assertFalse(htmlOption.getExportGeneratorName());
        assertTrue(htmlOption.getExportEmbeddedImages());
        assertTrue(htmlOption.getExportEmbeddedCss());
        assertTrue(htmlOption.getExportEmbeddedSvg());
        assertTrue(htmlOption.getExportEmbeddedFonts());
        assertEquals(72, htmlOption.getJpegQuality());
        assertTrue(htmlOption.getMemoryOptimization());
        assertFalse(htmlOption.getShowPageBorder());
        assertEquals(0, htmlOption.getPageMargins());
        assertTrue(htmlOption.getPrettyFormat());
        assertFalse(htmlOption.getExportFormFields());
        assertFalse(htmlOption.getUseAntiAliasing());
        assertFalse(htmlOption.getUseHighQualityRendering());
        assertEquals("fxiaoke", htmlOption.getCssClassNamesPrefix());
    }

    @Test
    void testGetPdfOption() {
        // When
        PdfSaveOptions pdfOption = wordsOptions.getPdfOption();

        // Then
        assertNotNull(pdfOption);
        assertFalse(pdfOption.getExportGeneratorName());
        assertEquals(90, pdfOption.getJpegQuality());
        assertTrue(pdfOption.getMemoryOptimization());
        assertFalse(pdfOption.getUseAntiAliasing());
        assertFalse(pdfOption.getUseHighQualityRendering());
        assertEquals(2, pdfOption.getZoomBehavior());
        assertEquals(50, pdfOption.getZoomFactor());
        assertEquals(2, pdfOption.getFontEmbeddingMode());
    }

    @Test
    void testToPng_SinglePage() throws Exception {
        // Given
        String expectedPath = tempDir.resolve("test_1.png").toString();

        try (MockedStatic<Document> documentMock = mockStatic(Document.class);
             MockedStatic<com.fxiaoke.document.convert.utils.FileNameUtil> fileNameUtilMock =
                mockStatic(com.fxiaoke.document.convert.utils.FileNameUtil.class)) {

            documentMock.when(() -> new Document(eq(testFilePath), any(LoadOptions.class)))
                    .thenReturn(mockDocument);
            fileNameUtilMock.when(() -> com.fxiaoke.document.convert.utils.FileNameUtil
                    .createTempFileName(testFilePath, 1, "png")).thenReturn(expectedPath);

            // When
            String result = wordsOptions.toPng(testFilePath, 1, testEa, testPath);

            // Then
            assertEquals(expectedPath, result);
            verify(mockDocument).save(eq(expectedPath), any(ImageSaveOptions.class));
            verify(previewInfoDao).updateFilePathList(testEa, testPath, expectedPath);
        }
    }

    @Test
    void testToPng_SinglePage_ThrowsException() throws Exception {
        // Given
        String expectedPath = tempDir.resolve("test_1.png").toString();

        try (MockedStatic<Document> documentMock = mockStatic(Document.class);
             MockedStatic<com.fxiaoke.document.convert.utils.FileNameUtil> fileNameUtilMock =
                mockStatic(com.fxiaoke.document.convert.utils.FileNameUtil.class)) {

            documentMock.when(() -> new Document(eq(testFilePath), any(LoadOptions.class)))
                    .thenReturn(mockDocument);
            fileNameUtilMock.when(() -> com.fxiaoke.document.convert.utils.FileNameUtil
                    .createTempFileName(testFilePath, 1, "png")).thenReturn(expectedPath);
            doThrow(new RuntimeException("Test exception")).when(mockDocument)
                    .save(eq(expectedPath), any(ImageSaveOptions.class));

            // When & Then
            BaseException exception = assertThrows(BaseException.class,
                    () -> wordsOptions.toPng(testFilePath, 1, testEa, testPath));

            assertEquals(500, exception.getCode());
        }
    }

    @Test
    void testToPng_Range() throws Exception {
        // Given
        String expectedPath1 = tempDir.resolve("test_1.png").toString();
        String expectedPath2 = tempDir.resolve("test_2.png").toString();

        try (MockedStatic<Document> documentMock = mockStatic(Document.class);
             MockedStatic<com.fxiaoke.document.convert.utils.FileNameUtil> fileNameUtilMock =
                mockStatic(com.fxiaoke.document.convert.utils.FileNameUtil.class);
             MockedStatic<java.io.File> fileMock = mockStatic(java.io.File.class)) {

            documentMock.when(() -> new Document(eq(testFilePath), any(LoadOptions.class)))
                    .thenReturn(mockDocument);
            fileNameUtilMock.when(() -> com.fxiaoke.document.convert.utils.FileNameUtil
                    .createTempFileName(testFilePath, 1, "png")).thenReturn(expectedPath1);
            fileNameUtilMock.when(() -> com.fxiaoke.document.convert.utils.FileNameUtil
                    .createTempFileName(testFilePath, 2, "png")).thenReturn(expectedPath2);

            java.io.File mockFile1 = mock(java.io.File.class);
            java.io.File mockFile2 = mock(java.io.File.class);
            fileMock.when(() -> new java.io.File(expectedPath1)).thenReturn(mockFile1);
            fileMock.when(() -> new java.io.File(expectedPath2)).thenReturn(mockFile2);
            when(mockFile1.exists()).thenReturn(false);
            when(mockFile2.exists()).thenReturn(false);

            // When
            List<String> result = wordsOptions.toPng(testEa, testPath, testFilePath, 1, 2);

            // Then
            assertEquals(2, result.size());
            assertTrue(result.contains(expectedPath1));
            assertTrue(result.contains(expectedPath2));
            verify(mockDocument, times(2)).save(anyString(), any(ImageSaveOptions.class));
            verify(previewInfoDao, times(2)).updateFilePathList(eq(testEa), eq(testPath), anyString());
        }
    }

    @Test
    void testToPng_AllPages() throws Exception {
        // Given
        String expectedPath1 = tempDir.resolve("test_1.png").toString();
        String expectedPath2 = tempDir.resolve("test_2.png").toString();

        try (MockedStatic<Document> documentMock = mockStatic(Document.class);
             MockedStatic<com.fxiaoke.document.convert.utils.FileNameUtil> fileNameUtilMock =
                mockStatic(com.fxiaoke.document.convert.utils.FileNameUtil.class);
             MockedStatic<java.io.File> fileMock = mockStatic(java.io.File.class)) {

            documentMock.when(() -> new Document(eq(testFilePath), any(LoadOptions.class)))
                    .thenReturn(mockDocument);
            fileNameUtilMock.when(() -> com.fxiaoke.document.convert.utils.FileNameUtil
                    .createTempFileName(testFilePath, 1, "png")).thenReturn(expectedPath1);
            fileNameUtilMock.when(() -> com.fxiaoke.document.convert.utils.FileNameUtil
                    .createTempFileName(testFilePath, 2, "png")).thenReturn(expectedPath2);

            java.io.File mockFile1 = mock(java.io.File.class);
            java.io.File mockFile2 = mock(java.io.File.class);
            fileMock.when(() -> new java.io.File(expectedPath1)).thenReturn(mockFile1);
            fileMock.when(() -> new java.io.File(expectedPath2)).thenReturn(mockFile2);
            when(mockFile1.exists()).thenReturn(false);
            when(mockFile2.exists()).thenReturn(false);

            // When
            List<String> result = wordsOptions.toPng(testEa, testPath, testFilePath, 2);

            // Then
            assertEquals(2, result.size());
            assertTrue(result.contains(expectedPath1));
            assertTrue(result.contains(expectedPath2));
            verify(mockDocument, times(2)).save(anyString(), any(ImageSaveOptions.class));
            verify(previewInfoDao, times(2)).updateFilePathList(eq(testEa), eq(testPath), anyString());
        }
    }
}
