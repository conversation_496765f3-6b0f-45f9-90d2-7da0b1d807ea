package com.fxiaoke.document.convert.process;

import com.fxiaoke.document.convert.process.options.WordsOptions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class WordsProcessTest {

    @Mock
    private WordsOptions wordsOptions;

    private WordsProcess wordsProcess;

    @BeforeEach
    void setUp() {
        wordsProcess = new WordsProcess(wordsOptions);
    }

    @Test
    void testConstructor() {
        // When
        WordsProcess newProcess = new WordsProcess(wordsOptions);

        // Then
        assertNotNull(newProcess);
    }

    @Test
    void testGetMetaInfo() {
        // Given
        String filePath = "/test/document.docx";
        String ea = "testEa";
        int expectedPageCount = 5;
        when(wordsOptions.getMetaInfo(filePath, ea)).thenReturn(expectedPageCount);

        // When
        int result = wordsProcess.getMetaInfo(filePath, ea);

        // Then
        assertEquals(expectedPageCount, result);
        verify(wordsOptions).getMetaInfo(filePath, ea);
    }

    @Test
    void testUpgrade() {
        // Given
        String filePath = "/test/document.doc";
        String expectedUpgradedPath = "/test/document.docx";
        when(wordsOptions.upgrade(filePath)).thenReturn(expectedUpgradedPath);

        // When
        String result = wordsProcess.upgrade(filePath);

        // Then
        assertEquals(expectedUpgradedPath, result);
        verify(wordsOptions).upgrade(filePath);
    }

    @Test
    void testToPng_SinglePage() {
        // Given
        String filePath = "/test/document.docx";
        int pageIndex = 1;
        String ea = "testEa";
        String path = "testPath";
        String expectedPngPath = "/test/page1.png";
        when(wordsOptions.toPng(filePath, pageIndex, ea, path)).thenReturn(expectedPngPath);

        // When
        List<String> result = wordsProcess.toPng(filePath, pageIndex, ea, path);

        // Then
        assertEquals(1, result.size());
        assertEquals(expectedPngPath, result.get(0));
        verify(wordsOptions).toPng(filePath, pageIndex, ea, path);
    }

    @Test
    void testToPng_Range() {
        // Given
        String ea = "testEa";
        String path = "testPath";
        String filePath = "/test/document.docx";
        int beginPageIndex = 1;
        int endPageIndex = 3;
        List<String> expectedPaths = Arrays.asList("/test/page1.png", "/test/page2.png", "/test/page3.png");
        when(wordsOptions.toPng(ea, path, filePath, beginPageIndex, endPageIndex)).thenReturn(expectedPaths);

        // When
        List<String> result = wordsProcess.toPng(ea, path, filePath, beginPageIndex, endPageIndex);

        // Then
        assertEquals(expectedPaths, result);
        verify(wordsOptions).toPng(ea, path, filePath, beginPageIndex, endPageIndex);
    }

    @Test
    void testToPng_AllPages() {
        // Given
        String ea = "testEa";
        String path = "testPath";
        String filePath = "/test/document.docx";
        int pageCount = 5;
        List<String> expectedPaths = Arrays.asList(
            "/test/page1.png", "/test/page2.png", "/test/page3.png", "/test/page4.png", "/test/page5.png");
        when(wordsOptions.toPng(ea, path, filePath, pageCount)).thenReturn(expectedPaths);

        // When
        List<String> result = wordsProcess.toPng(ea, path, filePath, pageCount);

        // Then
        assertEquals(expectedPaths, result);
        verify(wordsOptions).toPng(ea, path, filePath, pageCount);
    }

    @Test
    void testToHtml_SinglePage() {
        // Given
        String filePath = "/test/document.docx";
        int pageIndex = 1;
        String ea = "testEa";
        String path = "testPath";
        String expectedHtmlPath = "/test/page1.html";
        when(wordsOptions.toHtml(filePath, pageIndex, ea, path)).thenReturn(expectedHtmlPath);

        // When
        List<String> result = wordsProcess.toHtml(filePath, pageIndex, ea, path);

        // Then
        assertEquals(1, result.size());
        assertEquals(expectedHtmlPath, result.get(0));
        verify(wordsOptions).toHtml(filePath, pageIndex, ea, path);
    }

    @Test
    void testToHtml_Range() {
        // Given
        String ea = "testEa";
        String path = "testPath";
        String filePath = "/test/document.docx";
        int beginPageIndex = 1;
        int endPageIndex = 3;
        List<String> expectedPaths = Arrays.asList("/test/page1.html", "/test/page2.html", "/test/page3.html");
        when(wordsOptions.toHtml(ea, path, filePath, beginPageIndex, endPageIndex)).thenReturn(expectedPaths);

        // When
        List<String> result = wordsProcess.toHtml(ea, path, filePath, beginPageIndex, endPageIndex);

        // Then
        assertEquals(expectedPaths, result);
        verify(wordsOptions).toHtml(ea, path, filePath, beginPageIndex, endPageIndex);
    }

    @Test
    void testToHtml_AllPages() {
        // Given
        String ea = "testEa";
        String path = "testPath";
        String filePath = "/test/document.docx";
        int pageCount = 5;
        List<String> expectedPaths = Arrays.asList(
            "/test/page1.html", "/test/page2.html", "/test/page3.html", "/test/page4.html", "/test/page5.html");
        when(wordsOptions.toHtml(ea, path, filePath, pageCount)).thenReturn(expectedPaths);

        // When
        List<String> result = wordsProcess.toHtml(ea, path, filePath, pageCount);

        // Then
        assertEquals(expectedPaths, result);
        verify(wordsOptions).toHtml(ea, path, filePath, pageCount);
    }

    @Test
    void testOfficeToPdf() {
        // Given
        String filePath = "/test/document.docx";
        String expectedPdfPath = "/test/document.pdf";
        when(wordsOptions.toPdf(filePath)).thenReturn(expectedPdfPath);

        // When
        String result = wordsProcess.officeToPdf(filePath);

        // Then
        assertEquals(expectedPdfPath, result);
        verify(wordsOptions).toPdf(filePath);
    }

    @Test
    void testMerge() {
        // Given
        List<String> filePaths = Arrays.asList("/test/doc1.docx", "/test/doc2.docx");
        String masterFilePath = "/test/merged.docx";

        // When & Then - Should not throw exception (method is empty)
        assertDoesNotThrow(() -> wordsProcess.merge(filePaths, masterFilePath));
    }

    @Test
    void testToPngZip() {
        // Given
        String filePath = "/test/document.docx";
        String expectedZipPath = "/test/document.zip";
        when(wordsOptions.toPngZip(filePath)).thenReturn(expectedZipPath);

        // When
        String result = wordsProcess.toPngZip(filePath);

        // Then
        assertEquals(expectedZipPath, result);
        verify(wordsOptions).toPngZip(filePath);
    }

    @Test
    void testToPng_EmptyResult() {
        // Given
        String filePath = "/test/document.docx";
        int pageIndex = 1;
        String ea = "testEa";
        String path = "testPath";
        when(wordsOptions.toPng(filePath, pageIndex, ea, path)).thenReturn("");

        // When
        List<String> result = wordsProcess.toPng(filePath, pageIndex, ea, path);

        // Then
        assertEquals(1, result.size());
        assertEquals("", result.get(0));
        verify(wordsOptions).toPng(filePath, pageIndex, ea, path);
    }

    @Test
    void testToPng_Range_EmptyResult() {
        // Given
        String ea = "testEa";
        String path = "testPath";
        String filePath = "/test/document.docx";
        int beginPageIndex = 1;
        int endPageIndex = 3;
        when(wordsOptions.toPng(ea, path, filePath, beginPageIndex, endPageIndex)).thenReturn(Collections.emptyList());

        // When
        List<String> result = wordsProcess.toPng(ea, path, filePath, beginPageIndex, endPageIndex);

        // Then
        assertTrue(result.isEmpty());
        verify(wordsOptions).toPng(ea, path, filePath, beginPageIndex, endPageIndex);
    }

    @Test
    void testToHtml_EmptyResult() {
        // Given
        String filePath = "/test/document.docx";
        int pageIndex = 1;
        String ea = "testEa";
        String path = "testPath";
        when(wordsOptions.toHtml(filePath, pageIndex, ea, path)).thenReturn("");

        // When
        List<String> result = wordsProcess.toHtml(filePath, pageIndex, ea, path);

        // Then
        assertEquals(1, result.size());
        assertEquals("", result.get(0));
        verify(wordsOptions).toHtml(filePath, pageIndex, ea, path);
    }

    @Test
    void testToHtml_Range_EmptyResult() {
        // Given
        String ea = "testEa";
        String path = "testPath";
        String filePath = "/test/document.docx";
        int beginPageIndex = 1;
        int endPageIndex = 3;
        when(wordsOptions.toHtml(ea, path, filePath, beginPageIndex, endPageIndex)).thenReturn(Collections.emptyList());

        // When
        List<String> result = wordsProcess.toHtml(ea, path, filePath, beginPageIndex, endPageIndex);

        // Then
        assertTrue(result.isEmpty());
        verify(wordsOptions).toHtml(ea, path, filePath, beginPageIndex, endPageIndex);
    }

    @Test
    void testGetMetaInfo_ZeroPages() {
        // Given
        String filePath = "/test/empty.docx";
        String ea = "testEa";
        when(wordsOptions.getMetaInfo(filePath, ea)).thenReturn(0);

        // When
        int result = wordsProcess.getMetaInfo(filePath, ea);

        // Then
        assertEquals(0, result);
        verify(wordsOptions).getMetaInfo(filePath, ea);
    }

    @Test
    void testToPng_AllPages_ZeroPages() {
        // Given
        String ea = "testEa";
        String path = "testPath";
        String filePath = "/test/empty.docx";
        int pageCount = 0;
        when(wordsOptions.toPng(ea, path, filePath, pageCount)).thenReturn(Collections.emptyList());

        // When
        List<String> result = wordsProcess.toPng(ea, path, filePath, pageCount);

        // Then
        assertTrue(result.isEmpty());
        verify(wordsOptions).toPng(ea, path, filePath, pageCount);
    }

    @Test
    void testToHtml_AllPages_ZeroPages() {
        // Given
        String ea = "testEa";
        String path = "testPath";
        String filePath = "/test/empty.docx";
        int pageCount = 0;
        when(wordsOptions.toHtml(ea, path, filePath, pageCount)).thenReturn(Collections.emptyList());

        // When
        List<String> result = wordsProcess.toHtml(ea, path, filePath, pageCount);

        // Then
        assertTrue(result.isEmpty());
        verify(wordsOptions).toHtml(ea, path, filePath, pageCount);
    }

    @Test
    void testMerge_WithNullParameters() {
        // When & Then - Should not throw exception (method is empty)
        assertDoesNotThrow(() -> wordsProcess.merge(null, null));
    }

    @Test
    void testMerge_WithEmptyList() {
        // Given
        List<String> emptyList = Collections.emptyList();
        String masterFilePath = "/test/merged.docx";

        // When & Then - Should not throw exception (method is empty)
        assertDoesNotThrow(() -> wordsProcess.merge(emptyList, masterFilePath));
    }
}
