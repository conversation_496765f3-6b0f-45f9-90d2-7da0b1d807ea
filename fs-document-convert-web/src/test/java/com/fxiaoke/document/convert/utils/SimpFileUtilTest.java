package com.fxiaoke.document.convert.utils;

import com.fxiaoke.document.convert.domain.exception.BaseException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

class SimpFileUtilTest {

    @TempDir
    Path tempDir;

    @Test
    void testCreateTempFile_WithValidParameters() {
        // Given
        String rootDir = tempDir.toString();
        String ea = "testEa";
        int employeeId = 12345;
        String extension = "pdf";

        // When
        Path result = SimpFileUtil.createTempFile(rootDir, ea, employeeId, extension);

        // Then
        assertNotNull(result);
        assertTrue(result.toString().startsWith(rootDir));
        assertTrue(result.toString().contains(ea));
        assertTrue(result.toString().contains(String.valueOf(employeeId)));
        assertTrue(result.toString().endsWith("." + extension));
    }

    @Test
    void testCreateTempFile_WithZeroEmployeeId() {
        // Given
        String rootDir = tempDir.toString();
        String ea = "testEa";
        int employeeId = 0; // Should be replaced with default
        String extension = "docx";

        // When
        Path result = SimpFileUtil.createTempFile(rootDir, ea, employeeId, extension);

        // Then
        assertNotNull(result);
        assertTrue(result.toString().contains("10000")); // DEFAULT_EMPLOYEE_ID
        assertFalse(result.toString().contains("/0/"));
    }

    @Test
    void testCreateTempFile_WithDifferentExtensions() {
        // Given
        String rootDir = tempDir.toString();
        String ea = "testEa";
        int employeeId = 12345;

        // When & Then
        Path pdfFile = SimpFileUtil.createTempFile(rootDir, ea, employeeId, "pdf");
        Path docxFile = SimpFileUtil.createTempFile(rootDir, ea, employeeId, "docx");
        Path xlsxFile = SimpFileUtil.createTempFile(rootDir, ea, employeeId, "xlsx");

        assertTrue(pdfFile.toString().endsWith(".pdf"));
        assertTrue(docxFile.toString().endsWith(".docx"));
        assertTrue(xlsxFile.toString().endsWith(".xlsx"));
    }

    @Test
    void testCreateFile_Success() throws IOException {
        // Given
        Path testFile = tempDir.resolve("subdir").resolve("test.txt");
        assertFalse(Files.exists(testFile));
        assertFalse(Files.exists(testFile.getParent()));

        // When
        SimpFileUtil.createFile(testFile);

        // Then
        assertTrue(Files.exists(testFile));
        assertTrue(Files.exists(testFile.getParent()));
    }

    @Test
    void testCreateFile_FileAlreadyExists() throws IOException {
        // Given
        Path testFile = tempDir.resolve("existing.txt");
        Files.createFile(testFile);
        assertTrue(Files.exists(testFile));

        // When & Then
        assertThrows(BaseException.class, () -> SimpFileUtil.createFile(testFile));
    }

    @Test
    void testSaveFileToLocal_WithInputStream() throws IOException {
        // Given
        Path testFile = tempDir.resolve("test.txt");
        String content = "Hello, World!";
        InputStream inputStream = new ByteArrayInputStream(content.getBytes());

        // When
        SimpFileUtil.saveFileToLocal(testFile, inputStream);

        // Then
        assertTrue(Files.exists(testFile));
        String savedContent = Files.readString(testFile);
        assertEquals(content, savedContent);
    }

    @Test
    void testSaveFileToLocal_WithInputStreamAndSize() throws IOException {
        // Given
        Path testFile = tempDir.resolve("test_sized.txt");
        String content = "Hello, World with size!";
        InputStream inputStream = new ByteArrayInputStream(content.getBytes());
        long fileSize = content.getBytes().length;

        // When
        SimpFileUtil.saveFileToLocal(testFile, inputStream, fileSize);

        // Then
        assertTrue(Files.exists(testFile));
        String savedContent = Files.readString(testFile);
        assertEquals(content, savedContent);
        assertEquals(fileSize, Files.size(testFile));
    }

    @Test
    void testSaveFileToLocal_WithByteArrayAndSize() throws IOException {
        // Given
        Path testFile = tempDir.resolve("test_bytes.txt");
        String content = "Hello, World from bytes!";
        byte[] data = content.getBytes();
        long fileSize = data.length;

        // When
        SimpFileUtil.saveFileToLocal(testFile, data, fileSize);

        // Then
        assertTrue(Files.exists(testFile));
        String savedContent = Files.readString(testFile);
        assertEquals(content, savedContent);
        assertEquals(fileSize, Files.size(testFile));
    }

    @Test
    void testSaveFileToLocal_LargeFile() throws IOException {
        // Given
        Path testFile = tempDir.resolve("large_test.txt");
        byte[] largeData = new byte[1024 * 1024]; // 1MB
        for (int i = 0; i < largeData.length; i++) {
            largeData[i] = (byte) (i % 256);
        }
        InputStream inputStream = new ByteArrayInputStream(largeData);

        // When
        SimpFileUtil.saveFileToLocal(testFile, inputStream);

        // Then
        assertTrue(Files.exists(testFile));
        assertEquals(largeData.length, Files.size(testFile));
    }

    @Test
    void testSaveFileToLocal_PartialSize() throws IOException {
        // Given
        Path testFile = tempDir.resolve("partial_test.txt");
        String content = "This is a longer content than what we want to save";
        byte[] data = content.getBytes();
        long partialSize = 10; // Only save first 10 bytes

        // When
        SimpFileUtil.saveFileToLocal(testFile, data, partialSize);

        // Then
        assertTrue(Files.exists(testFile));
        assertEquals(partialSize, Files.size(testFile));
        String savedContent = Files.readString(testFile);
        assertEquals("This is a ", savedContent);
    }

    @Test
    void testGetLocalFileSize_ExistingFile() throws IOException {
        // Given
        Path testFile = tempDir.resolve("size_test.txt");
        String content = "Test content for size measurement";
        Files.write(testFile, content.getBytes());

        // When
        long size = SimpFileUtil.getLocalFileSize(testFile);

        // Then
        assertEquals(content.getBytes().length, size);
    }

    @Test
    void testGetLocalFileSize_NonExistentFile() {
        // Given
        Path nonExistentFile = tempDir.resolve("nonexistent.txt");

        // When & Then
        assertThrows(BaseException.class, () -> SimpFileUtil.getLocalFileSize(nonExistentFile));
    }

    @Test
    void testGetLocalFileSize_EmptyFile() throws IOException {
        // Given
        Path emptyFile = tempDir.resolve("empty.txt");
        Files.createFile(emptyFile);

        // When
        long size = SimpFileUtil.getLocalFileSize(emptyFile);

        // Then
        assertEquals(0, size);
    }

    @Test
    void testSaveFileToLocal_EmptyInputStream() throws IOException {
        // Given
        Path testFile = tempDir.resolve("empty_stream.txt");
        InputStream emptyStream = new ByteArrayInputStream(new byte[0]);

        // When
        SimpFileUtil.saveFileToLocal(testFile, emptyStream);

        // Then
        assertTrue(Files.exists(testFile));
        assertEquals(0, Files.size(testFile));
    }

    @Test
    void testSaveFileToLocal_EmptyByteArray() throws IOException {
        // Given
        Path testFile = tempDir.resolve("empty_bytes.txt");
        byte[] emptyData = new byte[0];

        // When
        SimpFileUtil.saveFileToLocal(testFile, emptyData, 0);

        // Then
        assertTrue(Files.exists(testFile));
        assertEquals(0, Files.size(testFile));
    }

    @Test
    void testCreateTempFile_PathStructure() {
        // Given
        String rootDir = "/tmp";
        String ea = "company123";
        int employeeId = 98765;
        String extension = "pdf";

        // When
        Path result = SimpFileUtil.createTempFile(rootDir, ea, employeeId, extension);

        // Then
        String pathStr = result.toString();
        assertTrue(pathStr.startsWith("/tmp/"));
        assertTrue(pathStr.contains("/company123/"));
        assertTrue(pathStr.contains("/98765/"));
        assertTrue(pathStr.contains("/pdf/"));
        assertTrue(pathStr.endsWith(".pdf"));
        
        // Should contain date/time components
        assertTrue(pathStr.matches(".*\\d{8}/.*")); // yyyyMMdd
        assertTrue(pathStr.matches(".*/\\d{2}/.*")); // dd
        assertTrue(pathStr.matches(".*/\\d{2}/.*")); // HH
        assertTrue(pathStr.matches(".*/\\d{2}/.*")); // mm
    }

    @Test
    void testCreateTempFile_UniqueFiles() {
        // Given
        String rootDir = tempDir.toString();
        String ea = "testEa";
        int employeeId = 12345;
        String extension = "txt";

        // When
        Path file1 = SimpFileUtil.createTempFile(rootDir, ea, employeeId, extension);
        Path file2 = SimpFileUtil.createTempFile(rootDir, ea, employeeId, extension);

        // Then
        assertNotEquals(file1, file2); // Should be different due to UUID
        assertTrue(file1.toString().contains(ea));
        assertTrue(file2.toString().contains(ea));
    }
}
