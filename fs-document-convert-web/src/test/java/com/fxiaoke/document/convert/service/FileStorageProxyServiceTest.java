package com.fxiaoke.document.convert.service;

import com.facishare.fsi.proxy.exception.FsiClientException;
import com.facishare.fsi.proxy.model.warehouse.a.AGetFileMetaData;
import com.facishare.fsi.proxy.model.warehouse.g.GFileDownload;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NGetFileMetaData;
import com.facishare.fsi.proxy.service.AFileStorageService;
import com.facishare.fsi.proxy.service.GFileStorageService;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.fxiaoke.document.convert.domain.exception.BaseException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FileStorageProxyServiceTest {

    @Mock
    private AFileStorageService aFileStorageService;
    
    @Mock
    private NFileStorageService nFileStorageService;
    
    @Mock
    private GFileStorageService gFileStorageService;

    @InjectMocks
    private FileStorageProxyService fileStorageProxyService;

    private static final String TEST_EA = "testEa";
    private static final int TEST_EMPLOYEE_ID = 12345;
    private static final long TEST_FILE_SIZE = 1024L;

    @BeforeEach
    void setUp() {
        // Mocks are injected via @InjectMocks
    }

    @Test
    void testGetSizeByPath_NFile_Success() throws FsiClientException {
        // Given
        String nPath = "N_test/document.pdf";
        NGetFileMetaData.Result mockResult = mock(NGetFileMetaData.Result.class);
        when(mockResult.getSize()).thenReturn(TEST_FILE_SIZE);
        when(nFileStorageService.nGetFileMetaData(any(NGetFileMetaData.Arg.class), eq(TEST_EA)))
            .thenReturn(mockResult);

        // When
        long result = fileStorageProxyService.getSizeByPath(nPath, TEST_EA, TEST_EMPLOYEE_ID);

        // Then
        assertEquals(TEST_FILE_SIZE, result);
        
        ArgumentCaptor<NGetFileMetaData.Arg> argCaptor = 
            ArgumentCaptor.forClass(NGetFileMetaData.Arg.class);
        verify(nFileStorageService).nGetFileMetaData(argCaptor.capture(), eq(TEST_EA));
        
        NGetFileMetaData.Arg capturedArg = argCaptor.getValue();
        assertEquals(TEST_EA, capturedArg.getEa());
        assertEquals(nPath, capturedArg.getFileName());
        assertEquals("E." + TEST_EMPLOYEE_ID, capturedArg.getDownUser());
        assertEquals("XiaoKeNetDisk", capturedArg.getDownloadSecurityGroup());
    }

    @Test
    void testGetSizeByPath_TNFile_Success() throws FsiClientException {
        // Given
        String tnPath = "TN_test/document.pdf";
        NGetFileMetaData.Result mockResult = mock(NGetFileMetaData.Result.class);
        when(mockResult.getSize()).thenReturn(TEST_FILE_SIZE);
        when(nFileStorageService.nGetFileMetaData(any(NGetFileMetaData.Arg.class), eq(TEST_EA)))
            .thenReturn(mockResult);

        // When
        long result = fileStorageProxyService.getSizeByPath(tnPath, TEST_EA, TEST_EMPLOYEE_ID);

        // Then
        assertEquals(TEST_FILE_SIZE, result);
        verify(nFileStorageService).nGetFileMetaData(any(NGetFileMetaData.Arg.class), eq(TEST_EA));
    }

    @Test
    void testGetSizeByPath_AFile_Success() throws FsiClientException {
        // Given
        String aPath = "A_test/document.pdf";
        AGetFileMetaData.Result mockResult = mock(AGetFileMetaData.Result.class);
        when(mockResult.getSize()).thenReturn(TEST_FILE_SIZE);
        when(aFileStorageService.getFileMetaData(any(AGetFileMetaData.Arg.class)))
            .thenReturn(mockResult);

        // When
        long result = fileStorageProxyService.getSizeByPath(aPath, TEST_EA, TEST_EMPLOYEE_ID);

        // Then
        assertEquals(TEST_FILE_SIZE, result);
        
        ArgumentCaptor<AGetFileMetaData.Arg> argCaptor = 
            ArgumentCaptor.forClass(AGetFileMetaData.Arg.class);
        verify(aFileStorageService).getFileMetaData(argCaptor.capture());
        
        AGetFileMetaData.Arg capturedArg = argCaptor.getValue();
        assertEquals(aPath, capturedArg.getFileName());
        assertEquals("Preview", capturedArg.getBusiness());
        assertEquals("XiaoKeNetDisk", capturedArg.getFileSecurityGroup());
        assertEquals(TEST_EA, capturedArg.getUser().getEa());
        assertEquals(TEST_EMPLOYEE_ID, capturedArg.getUser().getEmployeeId());
    }

    @Test
    void testGetSizeByPath_TAFile_Success() throws FsiClientException {
        // Given
        String taPath = "TA_test/document.pdf";
        AGetFileMetaData.Result mockResult = mock(AGetFileMetaData.Result.class);
        when(mockResult.getSize()).thenReturn(TEST_FILE_SIZE);
        when(aFileStorageService.getFileMetaData(any(AGetFileMetaData.Arg.class)))
            .thenReturn(mockResult);

        // When
        long result = fileStorageProxyService.getSizeByPath(taPath, TEST_EA, TEST_EMPLOYEE_ID);

        // Then
        assertEquals(TEST_FILE_SIZE, result);
        verify(aFileStorageService).getFileMetaData(any(AGetFileMetaData.Arg.class));
    }

    @Test
    void testGetSizeByPath_GFile_Success() throws FsiClientException {
        // Given
        String gPath = "G_test/document.pdf";
        byte[] testData = new byte[1024];
        GFileDownload.Result mockResult = new GFileDownload.Result();
        mockResult.data = testData;
        when(gFileStorageService.downloadFile(any(GFileDownload.Arg.class)))
            .thenReturn(mockResult);

        // When
        long result = fileStorageProxyService.getSizeByPath(gPath, TEST_EA, TEST_EMPLOYEE_ID);

        // Then
        assertEquals(testData.length, result);
        
        ArgumentCaptor<GFileDownload.Arg> argCaptor = 
            ArgumentCaptor.forClass(GFileDownload.Arg.class);
        verify(gFileStorageService).downloadFile(argCaptor.capture());
        
        GFileDownload.Arg capturedArg = argCaptor.getValue();
        assertEquals(gPath, capturedArg.gPath);
        assertEquals("E." + TEST_EA + "." + TEST_EMPLOYEE_ID, capturedArg.downloadUser);
        assertEquals("XiaoKeNetDisk", capturedArg.downloadSecurityGroup);
    }

    @ParameterizedTest
    @ValueSource(strings = {"N_test.pdf", "TN_test.pdf"})
    void testGetSizeByPath_NFiles_ThrowsException(String path) throws FsiClientException {
        // Given
        when(nFileStorageService.nGetFileMetaData(any(NGetFileMetaData.Arg.class), eq(TEST_EA)))
            .thenThrow(new FsiClientException("File not found"));

        // When & Then
        BaseException exception = assertThrows(BaseException.class, 
            () -> fileStorageProxyService.getSizeByPath(path, TEST_EA, TEST_EMPLOYEE_ID));
        
        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("获取文件大小失败"));
        assertTrue(exception.getModule().contains("FileStorageProxyService-getSizeByPath"));
    }

    @ParameterizedTest
    @ValueSource(strings = {"A_test.pdf", "TA_test.pdf"})
    void testGetSizeByPath_AFiles_ThrowsException(String path) throws FsiClientException {
        // Given
        when(aFileStorageService.getFileMetaData(any(AGetFileMetaData.Arg.class)))
            .thenThrow(new FsiClientException("File not found"));

        // When & Then
        BaseException exception = assertThrows(BaseException.class, 
            () -> fileStorageProxyService.getSizeByPath(path, TEST_EA, TEST_EMPLOYEE_ID));
        
        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("获取文件大小失败"));
    }

    @Test
    void testGetSizeByPath_GFile_ThrowsException() throws FsiClientException {
        // Given
        String gPath = "G_test/document.pdf";
        when(gFileStorageService.downloadFile(any(GFileDownload.Arg.class)))
            .thenThrow(new FsiClientException("File not found"));

        // When & Then
        BaseException exception = assertThrows(BaseException.class, 
            () -> fileStorageProxyService.getSizeByPath(gPath, TEST_EA, TEST_EMPLOYEE_ID));
        
        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("获取文件大小失败"));
    }

    @Test
    void testGetSizeByPaths_MixedFiles_Success() throws FsiClientException {
        // Given
        List<String> paths = Arrays.asList("N_test1.pdf", "A_test2.pdf", "N_test3.pdf");
        
        // Mock N file responses
        NGetFileMetaData.Result nResult1 = mock(NGetFileMetaData.Result.class);
        when(nResult1.getSize()).thenReturn(1000L);
        NGetFileMetaData.Result nResult2 = mock(NGetFileMetaData.Result.class);
        when(nResult2.getSize()).thenReturn(3000L);
        when(nFileStorageService.nGetFileMetaData(any(NGetFileMetaData.Arg.class), eq(TEST_EA)))
            .thenReturn(nResult1, nResult2);
        
        // Mock A file response
        AGetFileMetaData.Result aResult = mock(AGetFileMetaData.Result.class);
        when(aResult.getSize()).thenReturn(2000L);
        when(aFileStorageService.getFileMetaData(any(AGetFileMetaData.Arg.class)))
            .thenReturn(aResult);

        // When
        long result = fileStorageProxyService.getSizeByPaths(paths, TEST_EA, TEST_EMPLOYEE_ID);

        // Then
        assertEquals(6000L, result); // 1000 + 2000 + 3000
        verify(nFileStorageService, times(2)).nGetFileMetaData(any(NGetFileMetaData.Arg.class), eq(TEST_EA));
        verify(aFileStorageService, times(1)).getFileMetaData(any(AGetFileMetaData.Arg.class));
    }

    @Test
    void testGetSizeByPaths_OnlyNFiles_Success() throws FsiClientException {
        // Given
        List<String> paths = Arrays.asList("N_test1.pdf", "N_test2.pdf");
        
        NGetFileMetaData.Result result1 = mock(NGetFileMetaData.Result.class);
        when(result1.getSize()).thenReturn(1500L);
        NGetFileMetaData.Result result2 = mock(NGetFileMetaData.Result.class);
        when(result2.getSize()).thenReturn(2500L);
        when(nFileStorageService.nGetFileMetaData(any(NGetFileMetaData.Arg.class), eq(TEST_EA)))
            .thenReturn(result1, result2);

        // When
        long result = fileStorageProxyService.getSizeByPaths(paths, TEST_EA, TEST_EMPLOYEE_ID);

        // Then
        assertEquals(4000L, result);
        verify(nFileStorageService, times(2)).nGetFileMetaData(any(NGetFileMetaData.Arg.class), eq(TEST_EA));
        verify(aFileStorageService, never()).getFileMetaData(any());
    }

    @Test
    void testGetSizeByPaths_OnlyAFiles_Success() throws FsiClientException {
        // Given
        List<String> paths = Arrays.asList("A_test1.pdf", "TA_test2.pdf");
        
        AGetFileMetaData.Result result1 = mock(AGetFileMetaData.Result.class);
        when(result1.getSize()).thenReturn(800L);
        AGetFileMetaData.Result result2 = mock(AGetFileMetaData.Result.class);
        when(result2.getSize()).thenReturn(1200L);
        when(aFileStorageService.getFileMetaData(any(AGetFileMetaData.Arg.class)))
            .thenReturn(result1, result2);

        // When
        long result = fileStorageProxyService.getSizeByPaths(paths, TEST_EA, TEST_EMPLOYEE_ID);

        // Then
        assertEquals(2000L, result);
        verify(aFileStorageService, times(2)).getFileMetaData(any(AGetFileMetaData.Arg.class));
        verify(nFileStorageService, never()).nGetFileMetaData(any(), any());
    }

    @Test
    void testGetSizeByPaths_EmptyList_ReturnsZero() {
        // Given
        List<String> emptyPaths = Arrays.asList();

        // When
        long result = fileStorageProxyService.getSizeByPaths(emptyPaths, TEST_EA, TEST_EMPLOYEE_ID);

        // Then
        assertEquals(0L, result);
        verify(nFileStorageService, never()).nGetFileMetaData(any(), any());
        verify(aFileStorageService, never()).getFileMetaData(any());
    }

    @Test
    void testGetSizeByPaths_ThrowsException() throws FsiClientException {
        // Given
        List<String> paths = Arrays.asList("N_test1.pdf", "A_test2.pdf");
        when(nFileStorageService.nGetFileMetaData(any(NGetFileMetaData.Arg.class), eq(TEST_EA)))
            .thenThrow(new FsiClientException("Service error"));

        // When & Then
        BaseException exception = assertThrows(BaseException.class, 
            () -> fileStorageProxyService.getSizeByPaths(paths, TEST_EA, TEST_EMPLOYEE_ID));
        
        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("获取文件大小失败"));
        assertTrue(exception.getModule().contains("FileStorageProxyService-getSizeByPath"));
    }

    @Test
    void testGetSizeByPath_ZeroSizeFile_Success() throws FsiClientException {
        // Given
        String nPath = "N_empty.pdf";
        NGetFileMetaData.Result mockResult = mock(NGetFileMetaData.Result.class);
        when(mockResult.getSize()).thenReturn(0L);
        when(nFileStorageService.nGetFileMetaData(any(NGetFileMetaData.Arg.class), eq(TEST_EA)))
            .thenReturn(mockResult);

        // When
        long result = fileStorageProxyService.getSizeByPath(nPath, TEST_EA, TEST_EMPLOYEE_ID);

        // Then
        assertEquals(0L, result);
    }

    @Test
    void testGetSizeByPath_LargeFile_Success() throws FsiClientException {
        // Given
        String aPath = "A_large.pdf";
        long largeSize = Long.MAX_VALUE;
        AGetFileMetaData.Result mockResult = mock(AGetFileMetaData.Result.class);
        when(mockResult.getSize()).thenReturn(largeSize);
        when(aFileStorageService.getFileMetaData(any(AGetFileMetaData.Arg.class)))
            .thenReturn(mockResult);

        // When
        long result = fileStorageProxyService.getSizeByPath(aPath, TEST_EA, TEST_EMPLOYEE_ID);

        // Then
        assertEquals(largeSize, result);
    }

    @Test
    void testGetSizeByPath_GFile_EmptyData_ReturnsZero() throws FsiClientException {
        // Given
        String gPath = "G_empty.pdf";
        GFileDownload.Result mockResult = new GFileDownload.Result();
        mockResult.data = new byte[0];
        when(gFileStorageService.downloadFile(any(GFileDownload.Arg.class)))
            .thenReturn(mockResult);

        // When
        long result = fileStorageProxyService.getSizeByPath(gPath, TEST_EA, TEST_EMPLOYEE_ID);

        // Then
        assertEquals(0L, result);
    }

    @Test
    void testGetSizeByPaths_PartialFailure_ThrowsException() throws FsiClientException {
        // Given
        List<String> paths = Arrays.asList("N_test1.pdf", "N_test2.pdf");
        
        NGetFileMetaData.Result result1 = mock(NGetFileMetaData.Result.class);
        when(result1.getSize()).thenReturn(1000L);
        
        when(nFileStorageService.nGetFileMetaData(any(NGetFileMetaData.Arg.class), eq(TEST_EA)))
            .thenReturn(result1)
            .thenThrow(new FsiClientException("Second file error"));

        // When & Then
        BaseException exception = assertThrows(BaseException.class, 
            () -> fileStorageProxyService.getSizeByPaths(paths, TEST_EA, TEST_EMPLOYEE_ID));
        
        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("获取文件大小失败"));
    }

    @Test
    void testGetSizeByPath_VerifyUserFormat() throws FsiClientException {
        // Given
        String nPath = "N_test.pdf";
        NGetFileMetaData.Result mockResult = mock(NGetFileMetaData.Result.class);
        when(mockResult.getSize()).thenReturn(TEST_FILE_SIZE);
        when(nFileStorageService.nGetFileMetaData(any(NGetFileMetaData.Arg.class), eq(TEST_EA)))
            .thenReturn(mockResult);

        // When
        fileStorageProxyService.getSizeByPath(nPath, TEST_EA, TEST_EMPLOYEE_ID);

        // Then
        ArgumentCaptor<NGetFileMetaData.Arg> argCaptor = 
            ArgumentCaptor.forClass(NGetFileMetaData.Arg.class);
        verify(nFileStorageService).nGetFileMetaData(argCaptor.capture(), eq(TEST_EA));
        
        NGetFileMetaData.Arg capturedArg = argCaptor.getValue();
        assertEquals("E." + TEST_EMPLOYEE_ID, capturedArg.getDownUser());
    }

    @Test
    void testGetSizeByPaths_VerifyUserFormat() throws FsiClientException {
        // Given
        List<String> paths = Arrays.asList("N_test.pdf");
        NGetFileMetaData.Result mockResult = mock(NGetFileMetaData.Result.class);
        when(mockResult.getSize()).thenReturn(TEST_FILE_SIZE);
        when(nFileStorageService.nGetFileMetaData(any(NGetFileMetaData.Arg.class), eq(TEST_EA)))
            .thenReturn(mockResult);

        // When
        fileStorageProxyService.getSizeByPaths(paths, TEST_EA, TEST_EMPLOYEE_ID);

        // Then
        ArgumentCaptor<NGetFileMetaData.Arg> argCaptor = 
            ArgumentCaptor.forClass(NGetFileMetaData.Arg.class);
        verify(nFileStorageService).nGetFileMetaData(argCaptor.capture(), eq(TEST_EA));
        
        NGetFileMetaData.Arg capturedArg = argCaptor.getValue();
        assertEquals("E." + TEST_EA + "." + TEST_EMPLOYEE_ID, capturedArg.getDownUser());
    }
}
