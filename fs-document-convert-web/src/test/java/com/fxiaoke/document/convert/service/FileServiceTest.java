package com.fxiaoke.document.convert.service;

import com.facishare.fsi.proxy.exception.FsiClientException;
import com.facishare.fsi.proxy.model.warehouse.a.ADownloadFile;
import com.facishare.fsi.proxy.model.warehouse.g.GFileDownload;
import com.facishare.fsi.proxy.service.AFileStorageService;
import com.facishare.fsi.proxy.service.GFileStorageService;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileDownloadRequest;
import com.facishare.stone.sdk.request.StoneFileGetMetaDataRequest;
import com.facishare.stone.sdk.request.StoneFileUploadRequest;
import com.facishare.stone.sdk.response.StoneFileGetFileMetaResponse;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.fxiaoke.document.convert.config.CmsPropertiesConfig;
import com.fxiaoke.document.convert.domain.exception.BaseException;
import com.fxiaoke.stone.commons.SystemPresetClient;
import com.fxiaoke.stone.commons.domain.constant.AuthModel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FileServiceTest {

    @Mock
    private StoneProxyApi stoneProxyApi;

    @Mock
    private AsmService asmService;

    @Mock
    private GFileStorageService gFileStorageService;

    @Mock
    private AFileStorageService aFileStorageService;

    @Mock
    private NFileStorageService nFileStorageService;

    @Mock
    private CmsPropertiesConfig cmsPropertiesConfig;

    @Mock
    private SystemPresetClient metadataExclusiveClient;

    @Mock
    private SystemPresetClient appFrameworkExclusiveClient;

    private FileService fileService;

    @TempDir
    Path tempDir;

    private static final String TEST_EA = "testEa";
    private static final int TEST_EMPLOYEE_ID = 12345;
    private static final String TEST_SECURITY_GROUP = "testGroup";
    private static final String TEST_PATH = "test/document.pdf";
    private static final String TEST_FILE_NAME = "test.pdf";
    private static final String TEST_EXTENSION = "pdf";
    private static final String MODULE = "FileServiceTest";

    @BeforeEach
    void setUp() {
        fileService = new FileService(stoneProxyApi, asmService, gFileStorageService,
            aFileStorageService, nFileStorageService, cmsPropertiesConfig,
            metadataExclusiveClient, appFrameworkExclusiveClient);
    }

    @Test
    void testGetFileMeta_Success() throws FRestClientException {
        // Given
        StoneFileGetFileMetaResponse expectedResponse = new StoneFileGetFileMetaResponse();
        lenient().when(stoneProxyApi.getFileMetaData(any(StoneFileGetMetaDataRequest.class)))
            .thenReturn(expectedResponse);

        // When
        StoneFileGetFileMetaResponse result = fileService.getFileMeta(TEST_EA, TEST_EMPLOYEE_ID, TEST_PATH);

        // Then
        assertNotNull(result);
        assertEquals(expectedResponse, result);

        ArgumentCaptor<StoneFileGetMetaDataRequest> requestCaptor =
            ArgumentCaptor.forClass(StoneFileGetMetaDataRequest.class);
        verify(stoneProxyApi).getFileMetaData(requestCaptor.capture());

        StoneFileGetMetaDataRequest capturedRequest = requestCaptor.getValue();
        assertEquals(TEST_EA, capturedRequest.getEa());
        assertEquals(TEST_EMPLOYEE_ID, capturedRequest.getEmployeeId());
        assertEquals(TEST_PATH, capturedRequest.getPath());
        assertEquals("XiaoKeNetDisk", capturedRequest.getSecurityGroup());
    }

    @Test
    void testGetFileMeta_ThrowsException() throws FRestClientException {
        // Given
        lenient().when(stoneProxyApi.getFileMetaData(any(StoneFileGetMetaDataRequest.class)))
            .thenThrow(new RuntimeException("API Error"));

        // When & Then
        BaseException exception = assertThrows(BaseException.class,
            () -> fileService.getFileMeta(TEST_EA, TEST_EMPLOYEE_ID, TEST_PATH));

        assertEquals(500, exception.getCode());
        verify(stoneProxyApi).getFileMetaData(any(StoneFileGetMetaDataRequest.class));
    }

    @Test
    void testDownloadFileToLocalFile_FileNotExists_Success() throws IOException, FRestClientException {
        // Given
        Path localFile = tempDir.resolve("test.pdf");
        byte[] testData = "test content".getBytes();
        InputStream testStream = new ByteArrayInputStream(testData);

        lenient().when(stoneProxyApi.downloadStream(any(StoneFileDownloadRequest.class)))
            .thenReturn(testStream);

        // When
        fileService.downloadFileToLocalFile(TEST_EA, TEST_EMPLOYEE_ID, TEST_SECURITY_GROUP, "N_" + TEST_PATH, localFile);

        // Then
        assertTrue(Files.exists(localFile));
        byte[] actualData = Files.readAllBytes(localFile);
        assertArrayEquals(testData, actualData);
    }

    @Test
    void testDownloadFileToLocalFile_FileExists_SkipsDownload() throws IOException, FRestClientException {
        // Given
        Path localFile = tempDir.resolve("existing.pdf");
        Files.write(localFile, "existing content".getBytes());

        // When
        fileService.downloadFileToLocalFile(TEST_EA, TEST_EMPLOYEE_ID, TEST_SECURITY_GROUP, "N_" + TEST_PATH, localFile);

        // Then
        assertTrue(Files.exists(localFile));
        assertEquals("existing content", Files.readString(localFile));
        verify(stoneProxyApi, never()).downloadStream(any());
    }

    @Test
    void testDownloadFileToLocalFile_IOException_ThrowsException() throws IOException, FRestClientException {
        // Given
        Path localFile = tempDir.resolve("test.pdf");
        lenient().when(stoneProxyApi.downloadStream(any(StoneFileDownloadRequest.class)))
            .thenThrow(new RuntimeException("Download failed"));

        // When & Then
        BaseException exception = assertThrows(BaseException.class,
            () -> fileService.downloadFileToLocalFile(TEST_EA, TEST_EMPLOYEE_ID, TEST_SECURITY_GROUP, "N_" + TEST_PATH, localFile));

        assertEquals(500, exception.getCode());
    }

    @Test
    void testIsLocalFileExists_FileExists_ReturnsTrue() throws IOException {
        // Given
        Path existingFile = tempDir.resolve("existing.pdf");
        Files.write(existingFile, "content".getBytes());

        // When
        boolean result = fileService.isLocalFileExists(existingFile);

        // Then
        assertTrue(result);
    }

    @Test
    void testIsLocalFileExists_FileNotExists_ReturnsFalse() {
        // Given
        Path nonExistingFile = tempDir.resolve("nonexisting.pdf");

        // When
        boolean result = fileService.isLocalFileExists(nonExistingFile);

        // Then
        assertFalse(result);
    }

    @Test
    void testDownloadFileByStream_NFile_Success() throws FRestClientException {
        // Given
        String nPath = "N_test/document.pdf";
        InputStream expectedStream = new ByteArrayInputStream("test".getBytes());
        lenient().when(stoneProxyApi.downloadStream(any(StoneFileDownloadRequest.class)))
            .thenReturn(expectedStream);

        // When
        InputStream result = fileService.downloadFileByStream(nPath, TEST_EA, TEST_EMPLOYEE_ID, TEST_SECURITY_GROUP);

        // Then
        assertNotNull(result);
        assertEquals(expectedStream, result);

        ArgumentCaptor<StoneFileDownloadRequest> requestCaptor =
            ArgumentCaptor.forClass(StoneFileDownloadRequest.class);
        verify(stoneProxyApi).downloadStream(requestCaptor.capture());

        StoneFileDownloadRequest capturedRequest = requestCaptor.getValue();
        assertEquals(TEST_EA, capturedRequest.getEa());
        assertEquals(nPath, capturedRequest.getPath());
        assertEquals(TEST_EMPLOYEE_ID, capturedRequest.getEmployeeId());
        assertEquals(TEST_SECURITY_GROUP, capturedRequest.getSecurityGroup());
    }

    @Test
    void testDownloadFileByStream_TNFile_Success() throws FRestClientException {
        // Given
        String tnPath = "TN_test/document.pdf";
        InputStream expectedStream = new ByteArrayInputStream("test".getBytes());
        lenient().when(stoneProxyApi.downloadStream(any(StoneFileDownloadRequest.class)))
            .thenReturn(expectedStream);

        // When
        InputStream result = fileService.downloadFileByStream(tnPath, TEST_EA, TEST_EMPLOYEE_ID, TEST_SECURITY_GROUP);

        // Then
        assertNotNull(result);
        assertEquals(expectedStream, result);
    }

    @Test
    void testDownloadFileByStream_AFile_Success() throws FsiClientException {
        // Given
        String aPath = "A_test/document.pdf";
        byte[] testData = "test content".getBytes();
        ADownloadFile.Result mockResult = mock(ADownloadFile.Result.class);
        lenient().when(mockResult.getData()).thenReturn(testData);
        lenient().when(aFileStorageService.downloadFile(any(ADownloadFile.Arg.class)))
            .thenReturn(mockResult);

        // When
        InputStream result = fileService.downloadFileByStream(aPath, TEST_EA, TEST_EMPLOYEE_ID, TEST_SECURITY_GROUP);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof ByteArrayInputStream);

        ArgumentCaptor<ADownloadFile.Arg> argCaptor =
            ArgumentCaptor.forClass(ADownloadFile.Arg.class);
        verify(aFileStorageService).downloadFile(argCaptor.capture());

        ADownloadFile.Arg capturedArg = argCaptor.getValue();
        assertEquals(aPath, capturedArg.getaPath());
        assertEquals(TEST_SECURITY_GROUP, capturedArg.getFileSecurityGroup());
        assertNotNull(capturedArg.getUser()); // User对象已设置，但无法验证内部属性
    }

    @Test
    void testDownloadFileByStream_TAFile_Success() throws FsiClientException {
        // Given
        String taPath = "TA_test/document.pdf";
        byte[] testData = "test content".getBytes();
        ADownloadFile.Result mockResult = mock(ADownloadFile.Result.class);
        lenient().when(mockResult.getData()).thenReturn(testData);
        lenient().when(aFileStorageService.downloadFile(any(ADownloadFile.Arg.class)))
            .thenReturn(mockResult);

        // When
        InputStream result = fileService.downloadFileByStream(taPath, TEST_EA, TEST_EMPLOYEE_ID, TEST_SECURITY_GROUP);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof ByteArrayInputStream);
    }

    @Test
    void testDownloadFileByStream_GFile_Success() throws FsiClientException {
        // Given
        String gPath = "G_test/document.pdf";
        byte[] testData = "test content".getBytes();
        GFileDownload.Result mockResult = new GFileDownload.Result();
        mockResult.data = testData;
        lenient().when(gFileStorageService.downloadFile(any(GFileDownload.Arg.class)))
            .thenReturn(mockResult);

        // When
        InputStream result = fileService.downloadFileByStream(gPath, TEST_EA, TEST_EMPLOYEE_ID, TEST_SECURITY_GROUP);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof ByteArrayInputStream);

        ArgumentCaptor<GFileDownload.Arg> argCaptor =
            ArgumentCaptor.forClass(GFileDownload.Arg.class);
        verify(gFileStorageService).downloadFile(argCaptor.capture());

        GFileDownload.Arg capturedArg = argCaptor.getValue();
        assertEquals(gPath, capturedArg.gPath);
        assertEquals("E." + TEST_EMPLOYEE_ID, capturedArg.downloadUser);
        assertEquals(TEST_SECURITY_GROUP, capturedArg.downloadSecurityGroup);
    }

    @ParameterizedTest
    @ValueSource(strings = {"unknown_path", "X_test/document.pdf", "invalid/path"})
    void testDownloadFileByStream_UnknownFileType_ThrowsException(String path) {
        // When & Then
        BaseException exception = assertThrows(BaseException.class,
            () -> fileService.downloadFileByStream(path, TEST_EA, TEST_EMPLOYEE_ID, TEST_SECURITY_GROUP));

        assertEquals(500, exception.getCode());
        assertTrue(exception.getMessage().contains("Unknown file type"));
    }

    @Test
    void testDownloadFileByStream_NFile_ThrowsException() throws FRestClientException {
        // Given
        String nFilePath = "N_" + TEST_PATH;
        lenient().when(stoneProxyApi.downloadStream(any(StoneFileDownloadRequest.class)))
            .thenThrow(new RuntimeException("Download failed"));

        // When & Then
        BaseException exception = assertThrows(BaseException.class,
            () -> fileService.downloadFileByStream(nFilePath, TEST_EA, TEST_EMPLOYEE_ID, TEST_SECURITY_GROUP));

        assertEquals(500, exception.getCode());
        assertTrue(exception.getReason().contains(MODULE));
    }

    @Test
    void testDownloadFileByStream_AFile_ThrowsException() throws FsiClientException {
        // Given
        String aFilePath = "A_" + TEST_PATH;
        lenient().when(aFileStorageService.downloadFile(any()))
            .thenThrow(new RuntimeException("Download failed"));

        // When & Then
        BaseException exception = assertThrows(BaseException.class,
            () -> fileService.downloadFileByStream(aFilePath, TEST_EA, TEST_EMPLOYEE_ID, TEST_SECURITY_GROUP));

        assertEquals(500, exception.getCode());
        assertTrue(exception.getReason().contains(MODULE));
    }

    @Test
    void testDownloadFileByStream_GFile_ThrowsException() throws FsiClientException {
        // Given
        String gFilePath = "G_" + TEST_PATH;
        lenient().when(gFileStorageService.downloadFile(any()))
            .thenThrow(new RuntimeException("Download failed"));

        // When & Then
        BaseException exception = assertThrows(BaseException.class,
            () -> fileService.downloadFileByStream(gFilePath, TEST_EA, TEST_EMPLOYEE_ID, TEST_SECURITY_GROUP));

        assertEquals(500, exception.getCode());
        assertTrue(exception.getReason().contains(MODULE));
    }

    @Test
    void testUploadFile_Success() throws IOException, FRestClientException {
        // Given
        Path localFile = tempDir.resolve("upload.pdf");
        Files.write(localFile, "upload content".getBytes());

        StoneFileUploadResponse expectedResponse = new StoneFileUploadResponse();
        expectedResponse.setPath("uploaded/path");
        lenient().when(stoneProxyApi.uploadByStream(eq("n"), any(StoneFileUploadRequest.class), any(InputStream.class)))
            .thenReturn(expectedResponse);

        // When
        StoneFileUploadResponse result = fileService.uploadFile(TEST_EA, localFile, TEST_FILE_NAME, TEST_EXTENSION);

        // Then
        assertNotNull(result);
        assertEquals("uploaded/path", result.getPath());

        ArgumentCaptor<StoneFileUploadRequest> requestCaptor =
            ArgumentCaptor.forClass(StoneFileUploadRequest.class);
        verify(stoneProxyApi).uploadByStream(eq("n"), requestCaptor.capture(), any(InputStream.class));

        StoneFileUploadRequest capturedRequest = requestCaptor.getValue();
        assertEquals(TEST_EA, capturedRequest.getEa());
        assertEquals(-10000, capturedRequest.getEmployeeId());
        assertEquals(TEST_FILE_NAME, capturedRequest.getOriginName());
        assertEquals(TEST_EXTENSION, capturedRequest.getExtensionName());
        assertFalse(capturedRequest.getNeedCdn());
        assertFalse(capturedRequest.getNeedThumbnail());
    }

    @Test
    void testUploadFile_IOException_ThrowsException() throws IOException {
        // Given
        Path localFile = tempDir.resolve("test.pdf");
        lenient().when(Files.newInputStream(localFile))
            .thenThrow(new IOException("File not found"));

        // When & Then
        BaseException exception = assertThrows(BaseException.class,
            () -> fileService.uploadFile(TEST_EA, localFile, TEST_FILE_NAME, TEST_EXTENSION));

        assertEquals(500, exception.getCode());
        assertTrue(exception.getReason().contains(MODULE + ".uploadFile"));
    }

    @Test
    void testUploadFile_FRestClientException_ThrowsException() throws IOException, FRestClientException {
        // Given
        Path localFile = tempDir.resolve("test.pdf");
        Files.write(localFile, "test content".getBytes());
        lenient().when(stoneProxyApi.uploadByStream(anyString(), any(), any()))
            .thenThrow(new RuntimeException("Upload failed"));

        // When & Then
        BaseException exception = assertThrows(BaseException.class,
            () -> fileService.uploadFile(TEST_EA, localFile, TEST_FILE_NAME, TEST_EXTENSION));

        assertEquals(500, exception.getCode());
        assertTrue(exception.getReason().contains(MODULE + ".uploadFile"));
    }

    @Test
    void testGetFileAcUrl_Success() {
        // Given
        int enterpriseId = 12345;
        String signature = "test_signature";
        String expectedUrl = "http://test.url";

        lenient().when(asmService.getEid(TEST_EA)).thenReturn(enterpriseId);
        lenient().when(metadataExclusiveClient.signature(enterpriseId, TEST_PATH)).thenReturn(signature);
        lenient().when(appFrameworkExclusiveClient.generateUrl(
            String.valueOf(enterpriseId), "", "", "", "", AuthModel.COOKIE_ALL,
            TEST_PATH, signature, 3600L, TEST_FILE_NAME, TEST_EXTENSION))
            .thenReturn(expectedUrl);

        // When
        String result = fileService.getFileAcUrl(TEST_EA, TEST_PATH, TEST_FILE_NAME, TEST_EXTENSION);

        // Then
        assertEquals(expectedUrl, result);
        verify(asmService).getEid(TEST_EA);
        verify(metadataExclusiveClient).signature(enterpriseId, TEST_PATH);
        verify(appFrameworkExclusiveClient).generateUrl(
            String.valueOf(enterpriseId), "", "", "", "", AuthModel.COOKIE_ALL,
            TEST_PATH, signature, 3600L, TEST_FILE_NAME, TEST_EXTENSION);
    }

    @Test
    void testConstructor_AllDependenciesInjected() {
        // When
        FileService service = new FileService(stoneProxyApi, asmService, gFileStorageService,
            aFileStorageService, nFileStorageService, cmsPropertiesConfig,
            metadataExclusiveClient, appFrameworkExclusiveClient);

        // Then
        assertNotNull(service);
    }
}
