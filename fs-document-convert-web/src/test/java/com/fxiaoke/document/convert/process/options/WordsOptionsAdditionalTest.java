package com.fxiaoke.document.convert.process.options;

import com.aspose.words.Document;
import com.aspose.words.HtmlFixedSaveOptions;
import com.aspose.words.LoadOptions;
import com.fxiaoke.document.convert.dao.impl.PreviewInfoDao;
import com.fxiaoke.document.convert.domain.exception.BaseException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class WordsOptionsAdditionalTest {

    @Mock
    private PreviewInfoDao previewInfoDao;

    @Mock
    private Document mockDocument;

    private WordsOptions wordsOptions;

    @TempDir
    Path tempDir;

    private String testFilePath;
    private String testEa = "testEa";
    private String testPath = "testPath";

    @BeforeEach
    void setUp() throws IOException {
        wordsOptions = new WordsOptions(previewInfoDao);
        
        // Create a test file
        Path testFile = tempDir.resolve("test.docx");
        Files.write(testFile, "test content".getBytes());
        testFilePath = testFile.toString();
    }

    @Test
    void testToHtml_SinglePage() throws Exception {
        // Given
        String expectedPath = tempDir.resolve("test_1.html").toString();
        
        try (MockedStatic<Document> documentMock = mockStatic(Document.class);
             MockedStatic<com.fxiaoke.document.convert.utils.FileNameUtil> fileNameUtilMock = 
                mockStatic(com.fxiaoke.document.convert.utils.FileNameUtil.class)) {
            
            documentMock.when(() -> new Document(eq(testFilePath), any(LoadOptions.class)))
                    .thenReturn(mockDocument);
            fileNameUtilMock.when(() -> com.fxiaoke.document.convert.utils.FileNameUtil
                    .createTempFileName(testFilePath, 1, "html")).thenReturn(expectedPath);

            // When
            String result = wordsOptions.toHtml(testFilePath, 1, testEa, testPath);

            // Then
            assertEquals(expectedPath, result);
            verify(mockDocument).save(eq(expectedPath), any(HtmlFixedSaveOptions.class));
            verify(previewInfoDao).updateFilePathList(testEa, testPath, expectedPath);
        }
    }

    @Test
    void testToHtml_SinglePage_ThrowsException() throws Exception {
        // Given
        String expectedPath = tempDir.resolve("test_1.html").toString();
        
        try (MockedStatic<Document> documentMock = mockStatic(Document.class);
             MockedStatic<com.fxiaoke.document.convert.utils.FileNameUtil> fileNameUtilMock = 
                mockStatic(com.fxiaoke.document.convert.utils.FileNameUtil.class)) {
            
            documentMock.when(() -> new Document(eq(testFilePath), any(LoadOptions.class)))
                    .thenReturn(mockDocument);
            fileNameUtilMock.when(() -> com.fxiaoke.document.convert.utils.FileNameUtil
                    .createTempFileName(testFilePath, 1, "html")).thenReturn(expectedPath);
            doThrow(new RuntimeException("Test exception")).when(mockDocument)
                    .save(eq(expectedPath), any(HtmlFixedSaveOptions.class));

            // When & Then
            BaseException exception = assertThrows(BaseException.class,
                    () -> wordsOptions.toHtml(testFilePath, 1, testEa, testPath));

            assertEquals(500, exception.getCode());
        }
    }

    @Test
    void testToHtml_Range() throws Exception {
        // Given
        String expectedPath1 = tempDir.resolve("test_1.html").toString();
        String expectedPath2 = tempDir.resolve("test_2.html").toString();
        
        try (MockedStatic<Document> documentMock = mockStatic(Document.class);
             MockedStatic<com.fxiaoke.document.convert.utils.FileNameUtil> fileNameUtilMock = 
                mockStatic(com.fxiaoke.document.convert.utils.FileNameUtil.class);
             MockedStatic<java.io.File> fileMock = mockStatic(java.io.File.class)) {
            
            documentMock.when(() -> new Document(eq(testFilePath), any(LoadOptions.class)))
                    .thenReturn(mockDocument);
            fileNameUtilMock.when(() -> com.fxiaoke.document.convert.utils.FileNameUtil
                    .createTempFileName(testFilePath, 1, "html")).thenReturn(expectedPath1);
            fileNameUtilMock.when(() -> com.fxiaoke.document.convert.utils.FileNameUtil
                    .createTempFileName(testFilePath, 2, "html")).thenReturn(expectedPath2);
            
            java.io.File mockFile1 = mock(java.io.File.class);
            java.io.File mockFile2 = mock(java.io.File.class);
            fileMock.when(() -> new java.io.File(expectedPath1)).thenReturn(mockFile1);
            fileMock.when(() -> new java.io.File(expectedPath2)).thenReturn(mockFile2);
            when(mockFile1.exists()).thenReturn(false);
            when(mockFile2.exists()).thenReturn(false);

            // When
            List<String> result = wordsOptions.toHtml(testEa, testPath, testFilePath, 1, 2);

            // Then
            assertEquals(2, result.size());
            assertTrue(result.contains(expectedPath1));
            assertTrue(result.contains(expectedPath2));
            verify(mockDocument, times(2)).save(anyString(), any(HtmlFixedSaveOptions.class));
            verify(previewInfoDao, times(2)).updateFilePathList(eq(testEa), eq(testPath), anyString());
        }
    }

    @Test
    void testToHtml_AllPages() throws Exception {
        // Given
        String expectedPath1 = tempDir.resolve("test_1.html").toString();
        String expectedPath2 = tempDir.resolve("test_2.html").toString();
        
        try (MockedStatic<Document> documentMock = mockStatic(Document.class);
             MockedStatic<com.fxiaoke.document.convert.utils.FileNameUtil> fileNameUtilMock = 
                mockStatic(com.fxiaoke.document.convert.utils.FileNameUtil.class)) {
            
            documentMock.when(() -> new Document(eq(testFilePath), any(LoadOptions.class)))
                    .thenReturn(mockDocument);
            fileNameUtilMock.when(() -> com.fxiaoke.document.convert.utils.FileNameUtil
                    .createTempFileName(testFilePath, 1, "html")).thenReturn(expectedPath1);
            fileNameUtilMock.when(() -> com.fxiaoke.document.convert.utils.FileNameUtil
                    .createTempFileName(testFilePath, 2, "html")).thenReturn(expectedPath2);

            // When
            List<String> result = wordsOptions.toHtml(testEa, testPath, testFilePath, 2);

            // Then
            assertEquals(2, result.size());
            assertTrue(result.contains(expectedPath1));
            assertTrue(result.contains(expectedPath2));
            verify(mockDocument, times(2)).save(anyString(), any(HtmlFixedSaveOptions.class));
            verify(previewInfoDao, times(2)).updateFilePathList(eq(testEa), eq(testPath), anyString());
        }
    }

    @Test
    void testToPdf() throws Exception {
        // Given
        String expectedPath = tempDir.resolve("test.pdf").toString();
        
        try (MockedStatic<Document> documentMock = mockStatic(Document.class);
             MockedStatic<com.fxiaoke.document.convert.utils.FileNameUtil> fileNameUtilMock = 
                mockStatic(com.fxiaoke.document.convert.utils.FileNameUtil.class)) {
            
            documentMock.when(() -> new Document(eq(testFilePath), any(LoadOptions.class)))
                    .thenReturn(mockDocument);
            fileNameUtilMock.when(() -> com.fxiaoke.document.convert.utils.FileNameUtil
                    .createTempFileName(testFilePath, ".pdf")).thenReturn(expectedPath);

            // When
            String result = wordsOptions.toPdf(testFilePath);

            // Then
            assertEquals(expectedPath, result);
            verify(mockDocument).save(eq(expectedPath), any());
        }
    }

    @Test
    void testToPdf_ThrowsException() throws Exception {
        // Given
        String expectedPath = tempDir.resolve("test.pdf").toString();
        
        try (MockedStatic<Document> documentMock = mockStatic(Document.class);
             MockedStatic<com.fxiaoke.document.convert.utils.FileNameUtil> fileNameUtilMock = 
                mockStatic(com.fxiaoke.document.convert.utils.FileNameUtil.class)) {
            
            documentMock.when(() -> new Document(eq(testFilePath), any(LoadOptions.class)))
                    .thenReturn(mockDocument);
            fileNameUtilMock.when(() -> com.fxiaoke.document.convert.utils.FileNameUtil
                    .createTempFileName(testFilePath, ".pdf")).thenReturn(expectedPath);
            doThrow(new RuntimeException("Test exception")).when(mockDocument)
                    .save(eq(expectedPath), any());

            // When & Then
            BaseException exception = assertThrows(BaseException.class,
                    () -> wordsOptions.toPdf(testFilePath));

            assertEquals(500, exception.getCode());
        }
    }

    @Test
    void testToPngZip() throws Exception {
        // Given
        String zipDir = tempDir.resolve("zipDir").toString();
        String zipFile = tempDir.resolve("test.zip").toString();
        
        try (MockedStatic<Document> documentMock = mockStatic(Document.class);
             MockedStatic<com.fxiaoke.document.convert.utils.FileNameUtil> fileNameUtilMock = 
                mockStatic(com.fxiaoke.document.convert.utils.FileNameUtil.class);
             MockedStatic<com.fxiaoke.document.convert.utils.ZipUtil> zipUtilMock = 
                mockStatic(com.fxiaoke.document.convert.utils.ZipUtil.class);
             MockedStatic<org.apache.commons.io.FilenameUtils> filenameUtilsMock = 
                mockStatic(org.apache.commons.io.FilenameUtils.class);
             MockedStatic<java.io.File> fileMock = mockStatic(java.io.File.class)) {
            
            documentMock.when(() -> new Document(eq(testFilePath), any(LoadOptions.class)))
                    .thenReturn(mockDocument);
            when(mockDocument.getPageCount()).thenReturn(2);
            fileNameUtilMock.when(() -> com.fxiaoke.document.convert.utils.FileNameUtil
                    .createTempDir(testFilePath)).thenReturn(zipDir);
            fileNameUtilMock.when(() -> com.fxiaoke.document.convert.utils.FileNameUtil
                    .createTempFileName(testFilePath, ".zip")).thenReturn(zipFile);
            filenameUtilsMock.when(() -> org.apache.commons.io.FilenameUtils
                    .concat(zipDir, "1.png")).thenReturn(zipDir + "/1.png");
            filenameUtilsMock.when(() -> org.apache.commons.io.FilenameUtils
                    .concat(zipDir, "2.png")).thenReturn(zipDir + "/2.png");
            
            java.io.File mockFile1 = mock(java.io.File.class);
            java.io.File mockFile2 = mock(java.io.File.class);
            fileMock.when(() -> new java.io.File(zipDir + "/1.png")).thenReturn(mockFile1);
            fileMock.when(() -> new java.io.File(zipDir + "/2.png")).thenReturn(mockFile2);
            when(mockFile1.exists()).thenReturn(false);
            when(mockFile2.exists()).thenReturn(false);

            // When
            String result = wordsOptions.toPngZip(testFilePath);

            // Then
            assertEquals(zipDir, result);
            verify(mockDocument, times(2)).save(anyString(), any());
            zipUtilMock.verify(() -> com.fxiaoke.document.convert.utils.ZipUtil.zip(zipDir, zipFile));
        }
    }

    @Test
    void testToPngZip_ThrowsException() throws Exception {
        // Given
        try (MockedStatic<Document> documentMock = mockStatic(Document.class)) {
            documentMock.when(() -> new Document(eq(testFilePath), any(LoadOptions.class)))
                    .thenThrow(new RuntimeException("Test exception"));

            // When & Then
            BaseException exception = assertThrows(BaseException.class,
                    () -> wordsOptions.toPngZip(testFilePath));

            assertEquals(500, exception.getCode());
        }
    }
}
