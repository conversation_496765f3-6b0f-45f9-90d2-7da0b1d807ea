package com.fxiaoke.document.convert.service;

import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.fxiaoke.document.convert.config.CmsPropertiesConfig;
import com.fxiaoke.document.convert.domain.exception.BaseException;
import com.fxiaoke.document.convert.domain.open.DocPageQRCode;
import com.fxiaoke.document.convert.domain.open.GetMeatInfo;
import com.fxiaoke.document.convert.domain.open.MergerOffice;
import com.fxiaoke.document.convert.domain.open.OfficeToPdf;
import com.fxiaoke.document.convert.domain.open.ToPngZip;
import com.fxiaoke.document.convert.domain.open.Watermark;
import com.fxiaoke.document.convert.process.CellsProcess;
import com.fxiaoke.document.convert.process.PdfProcess;
import com.fxiaoke.document.convert.process.SlidesProcess;
import com.fxiaoke.document.convert.process.WordsProcess;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OpenApiServiceTest {

    @Mock
    private StoneApiService stoneApiService;

    @Mock
    private FileStorageProxyService fileStorageProxyService;

    @Mock
    private CmsPropertiesConfig cmsPropertiesConfig;

    @Mock
    private SlidesProcess slidesProcess;

    @Mock
    private WordsProcess wordsProcess;

    @Mock
    private PdfProcess pdfProcess;

    @Mock
    private CellsProcess cellsProcess;

    @Mock
    private IPDFBoxService pdfBoxService;

    @Mock
    private FileCleanupManager fileCleanupManager;

    @InjectMocks
    private OpenApiService openApiService;

    private static final String TEST_EA = "testEa";
    private static final int TEST_EMPLOYEE_ID = 12345;
    private static final String TEST_PATH = "test/document.pdf";
    private static final long TEST_FILE_SIZE = 1024 * 1024; // 1MB

    @BeforeEach
    void setUp() {
        when(cmsPropertiesConfig.getRootDir()).thenReturn("/tmp");
    }

    @Test
    void testGetMetaInfo_PDF_Success() {
        // Given
        String pdfPath = "test/document.pdf";
        when(fileStorageProxyService.getSizeByPath(pdfPath, TEST_EA, TEST_EMPLOYEE_ID))
            .thenReturn(TEST_FILE_SIZE);
        when(stoneApiService.downloadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID), eq(pdfPath)))
            .thenReturn(new ByteArrayInputStream("test content".getBytes()));
        when(pdfProcess.getMetaInfo(anyString())).thenReturn(10);

        // When
        GetMeatInfo.Result result = openApiService.getMetaInfo(TEST_EA, TEST_EMPLOYEE_ID, pdfPath);

        // Then
        assertNotNull(result);
        assertEquals(10, result.getPageCount());
        verify(pdfProcess).getMetaInfo(anyString());
    }

    @ParameterizedTest
    @CsvSource({
        "test/document.doc, DOC",
        "test/document.docx, DOCX",
        "test/document.xls, XLS",
        "test/document.xlsx, XLSX",
        "test/document.ppt, PPT",
        "test/document.pptx, PPTX"
    })
    void testGetMetaInfo_OfficeFormats_Success(String filePath, String fileType) {
        // Given
        when(fileStorageProxyService.getSizeByPath(filePath, TEST_EA, TEST_EMPLOYEE_ID))
            .thenReturn(TEST_FILE_SIZE);
        when(stoneApiService.downloadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID), eq(filePath)))
            .thenReturn(new ByteArrayInputStream("test content".getBytes()));

        switch (fileType) {
            case "DOC", "DOCX" -> when(wordsProcess.getMetaInfo(anyString(), eq(TEST_EA))).thenReturn(5);
            case "XLS", "XLSX" -> {
                com.fxiaoke.document.convert.domain.preview.GetMateInfo.Result mateResult =
                    com.fxiaoke.document.convert.domain.preview.GetMateInfo.Result.of(3);
                when(cellsProcess.getMetaInfo(anyString())).thenReturn(mateResult);
            }
            case "PPT", "PPTX" -> when(slidesProcess.getMetaInfo(anyString(), eq(false))).thenReturn(8);
        }

        // When
        GetMeatInfo.Result result = openApiService.getMetaInfo(TEST_EA, TEST_EMPLOYEE_ID, filePath);

        // Then
        assertNotNull(result);
        assertTrue(result.getPageCount() > 0);
    }

    @Test
    void testGetMetaInfo_UnsupportedFileType_ThrowsException() {
        // Given
        String unsupportedPath = "test/document.txt";
        when(fileStorageProxyService.getSizeByPath(unsupportedPath, TEST_EA, TEST_EMPLOYEE_ID))
            .thenReturn(TEST_FILE_SIZE);

        // When & Then
        BaseException exception = assertThrows(BaseException.class,
            () -> openApiService.getMetaInfo(TEST_EA, TEST_EMPLOYEE_ID, unsupportedPath));

        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("文件类型不支持"));
    }

    @ParameterizedTest
    @ValueSource(longs = {0, -1, 104857601L}) // 0, negative, over 100MB
    void testGetMetaInfo_InvalidFileSize_ThrowsException(long fileSize) {
        // Given
        when(fileStorageProxyService.getSizeByPath(TEST_PATH, TEST_EA, TEST_EMPLOYEE_ID))
            .thenReturn(fileSize);

        // When & Then
        BaseException exception = assertThrows(BaseException.class,
            () -> openApiService.getMetaInfo(TEST_EA, TEST_EMPLOYEE_ID, TEST_PATH));

        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("文件不能为空且大小应在100M以内"));
    }

    @Test
    void testOfficeToPdf_WordDocument_Success() {
        // Given
        String docPath = "test/document.docx";
        String tempPdfPath = "/tmp/converted.pdf";

        when(fileStorageProxyService.getSizeByPath(docPath, TEST_EA, TEST_EMPLOYEE_ID))
            .thenReturn(TEST_FILE_SIZE);
        when(stoneApiService.downloadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID), eq(docPath)))
            .thenReturn(new ByteArrayInputStream("test content".getBytes()));
        when(wordsProcess.officeToPdf(anyString())).thenReturn(tempPdfPath);
        when(pdfProcess.getMetaInfo(tempPdfPath)).thenReturn(5);

        StoneFileUploadResponse uploadResponse = new StoneFileUploadResponse();
        uploadResponse.setPath("uploaded/converted.pdf");
        when(stoneApiService.tempFileUploadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID),
            anyInt(), anyString(), eq("pdf"), eq(3), any(InputStream.class)))
            .thenReturn(uploadResponse);

        // When
        OfficeToPdf.Result result = openApiService.officeToPdf(TEST_EA, TEST_EMPLOYEE_ID,
            docPath, false, false);

        // Then
        assertNotNull(result);
        assertEquals("uploaded/converted.pdf", result.getNpaths().get(0));
        assertEquals(5, result.getPageCount());
        assertEquals("pdf", result.getFileType());
        verify(wordsProcess).officeToPdf(anyString());
        verify(pdfProcess).getMetaInfo(tempPdfPath);
    }

    @Test
    void testOfficeToPdf_ExcelDocument_Success() {
        // Given
        String xlsPath = "test/document.xlsx";
        String tempPdfPath = "/tmp/converted.pdf";

        when(fileStorageProxyService.getSizeByPath(xlsPath, TEST_EA, TEST_EMPLOYEE_ID))
            .thenReturn(TEST_FILE_SIZE);
        when(stoneApiService.downloadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID), eq(xlsPath)))
            .thenReturn(new ByteArrayInputStream("test content".getBytes()));
        when(cellsProcess.officeToPdf(anyString(), eq(true), eq(true))).thenReturn(tempPdfPath);
        when(pdfProcess.getMetaInfo(tempPdfPath)).thenReturn(3);

        StoneFileUploadResponse uploadResponse = new StoneFileUploadResponse();
        uploadResponse.setPath("uploaded/converted.pdf");
        when(stoneApiService.tempFileUploadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID),
            anyInt(), anyString(), eq("pdf"), eq(3), any(InputStream.class)))
            .thenReturn(uploadResponse);

        // When
        OfficeToPdf.Result result = openApiService.officeToPdf(TEST_EA, TEST_EMPLOYEE_ID,
            xlsPath, true, true);

        // Then
        assertNotNull(result);
        assertEquals("uploaded/converted.pdf", result.getNpaths().get(0));
        assertEquals(3, result.getPageCount());
        verify(cellsProcess).officeToPdf(anyString(), eq(true), eq(true));
    }

    @Test
    void testOfficeToPdf_PowerPointDocument_Success() {
        // Given
        String pptPath = "test/document.pptx";
        String tempPdfPath = "/tmp/converted.pdf";

        when(fileStorageProxyService.getSizeByPath(pptPath, TEST_EA, TEST_EMPLOYEE_ID))
            .thenReturn(TEST_FILE_SIZE);
        when(stoneApiService.downloadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID), eq(pptPath)))
            .thenReturn(new ByteArrayInputStream("test content".getBytes()));
        when(slidesProcess.officeToPdf(anyString())).thenReturn(tempPdfPath);
        when(pdfProcess.getMetaInfo(tempPdfPath)).thenReturn(8);

        StoneFileUploadResponse uploadResponse = new StoneFileUploadResponse();
        uploadResponse.setPath("uploaded/converted.pdf");
        when(stoneApiService.tempFileUploadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID),
            anyInt(), anyString(), eq("pdf"), eq(3), any(InputStream.class)))
            .thenReturn(uploadResponse);

        // When
        OfficeToPdf.Result result = openApiService.officeToPdf(TEST_EA, TEST_EMPLOYEE_ID,
            pptPath, false, false);

        // Then
        assertNotNull(result);
        assertEquals("uploaded/converted.pdf", result.getNpaths().get(0));
        assertEquals(8, result.getPageCount());
        verify(slidesProcess).officeToPdf(anyString());
    }

    @Test
    void testOfficeToPdf_UnsupportedFileType_ThrowsException() {
        // Given
        String unsupportedPath = "test/document.txt";
        when(fileStorageProxyService.getSizeByPath(unsupportedPath, TEST_EA, TEST_EMPLOYEE_ID))
            .thenReturn(TEST_FILE_SIZE);

        // When & Then
        BaseException exception = assertThrows(BaseException.class,
            () -> openApiService.officeToPdf(TEST_EA, TEST_EMPLOYEE_ID, unsupportedPath, false, false));

        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("文件类型不支持"));
    }

    @Test
    void testMergerOffice_PDFFiles_Success() {
        // Given
        List<String> pdfPaths = Arrays.asList("test/doc1.pdf", "test/doc2.pdf");
        String mergedPath = "/tmp/merged.pdf";

        when(fileStorageProxyService.getSizeByPaths(pdfPaths, TEST_EA, TEST_EMPLOYEE_ID))
            .thenReturn(TEST_FILE_SIZE * 2);
        when(fileStorageProxyService.getSizeByPath(anyString(), eq(TEST_EA), eq(TEST_EMPLOYEE_ID)))
            .thenReturn(TEST_FILE_SIZE);
        when(stoneApiService.downloadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID), anyString()))
            .thenReturn(new ByteArrayInputStream("test content".getBytes()));

        StoneFileUploadResponse uploadResponse = new StoneFileUploadResponse();
        uploadResponse.setPath("uploaded/merged.pdf");
        when(stoneApiService.tempFileUploadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID),
            anyInt(), anyString(), eq("pdf"), eq(3), any(InputStream.class)))
            .thenReturn(uploadResponse);

        // When
        MergerOffice.Result result = openApiService.mergerOffice(TEST_EA, TEST_EMPLOYEE_ID,
            pdfPaths, "pdf");

        // Then
        assertNotNull(result);
        assertEquals("uploaded/merged.pdf", result.getNpaths().get(0));
        assertEquals("pdf", result.getFileType());
        verify(pdfProcess).merge(anyList(), anyString());
    }

    @Test
    void testMergerOffice_UnsupportedFileType_ThrowsException() {
        // Given
        List<String> paths = Arrays.asList("test/doc1.txt", "test/doc2.txt");
        when(fileStorageProxyService.getSizeByPaths(paths, TEST_EA, TEST_EMPLOYEE_ID))
            .thenReturn(TEST_FILE_SIZE * 2);

        // When & Then
        BaseException exception = assertThrows(BaseException.class,
            () -> openApiService.mergerOffice(TEST_EA, TEST_EMPLOYEE_ID, paths, "txt"));

        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("文件类型不支持"));
    }

    @Test
    void testToPngZip_PDFFile_Success() {
        // Given
        String pdfPath = "test/document.pdf";
        String zipPath = "/tmp/images.zip";

        when(fileStorageProxyService.getSizeByPath(pdfPath, TEST_EA, TEST_EMPLOYEE_ID))
            .thenReturn(TEST_FILE_SIZE);
        when(stoneApiService.downloadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID), eq(pdfPath)))
            .thenReturn(new ByteArrayInputStream("test content".getBytes()));
        when(pdfProcess.toPngZip(anyString())).thenReturn(zipPath);

        StoneFileUploadResponse uploadResponse = new StoneFileUploadResponse();
        uploadResponse.setPath("uploaded/images.zip");
        when(stoneApiService.tempFileUploadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID),
            anyInt(), anyString(), eq("zip"), eq(3), any(InputStream.class)))
            .thenReturn(uploadResponse);

        // When
        ToPngZip.Result result = openApiService.toPngZip(TEST_EA, TEST_EMPLOYEE_ID, pdfPath);

        // Then
        assertNotNull(result);
        assertEquals("uploaded/images.zip", result.getPath());
        verify(pdfProcess).toPngZip(anyString());
    }

    @Test
    void testToPngZip_WordFile_Success() {
        // Given
        String docPath = "test/document.docx";
        String zipPath = "/tmp/images.zip";

        when(fileStorageProxyService.getSizeByPath(docPath, TEST_EA, TEST_EMPLOYEE_ID))
            .thenReturn(TEST_FILE_SIZE);
        when(stoneApiService.downloadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID), eq(docPath)))
            .thenReturn(new ByteArrayInputStream("test content".getBytes()));
        when(wordsProcess.toPngZip(anyString())).thenReturn(zipPath);

        StoneFileUploadResponse uploadResponse = new StoneFileUploadResponse();
        uploadResponse.setPath("uploaded/images.zip");
        when(stoneApiService.tempFileUploadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID),
            anyInt(), anyString(), eq("zip"), eq(3), any(InputStream.class)))
            .thenReturn(uploadResponse);

        // When
        ToPngZip.Result result = openApiService.toPngZip(TEST_EA, TEST_EMPLOYEE_ID, docPath);

        // Then
        assertNotNull(result);
        assertEquals("uploaded/images.zip", result.getPath());
        verify(wordsProcess).toPngZip(anyString());
    }

    @Test
    void testToPngZip_UnsupportedFileType_ThrowsException() {
        // Given
        String unsupportedPath = "test/document.txt";
        when(fileStorageProxyService.getSizeByPath(unsupportedPath, TEST_EA, TEST_EMPLOYEE_ID))
            .thenReturn(TEST_FILE_SIZE);

        // When & Then
        BaseException exception = assertThrows(BaseException.class,
            () -> openApiService.toPngZip(TEST_EA, TEST_EMPLOYEE_ID, unsupportedPath));

        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("文件类型不支持"));
    }

    @Test
    void testWatermark_Success() throws IOException {
        // Given
        Watermark.Arg arg = new Watermark.Arg();
        arg.setEa(TEST_EA);
        arg.setEmployeeId(TEST_EMPLOYEE_ID);
        arg.setPath(TEST_PATH);
        arg.setExtension("pdf");
        arg.setFileName("test.pdf");

        when(fileStorageProxyService.getSizeByPath(TEST_PATH, TEST_EA, TEST_EMPLOYEE_ID))
            .thenReturn(TEST_FILE_SIZE);
        when(stoneApiService.downloadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID), eq(TEST_PATH)))
            .thenReturn(new ByteArrayInputStream("test content".getBytes()));

        Path watermarkPath = Paths.get("/tmp/watermarked.pdf");
        when(pdfBoxService.addTextWatermark(any(InputStream.class), anyString(), eq(arg)))
            .thenReturn(watermarkPath);

        StoneFileUploadResponse uploadResponse = new StoneFileUploadResponse();
        uploadResponse.setPath("uploaded/watermarked.pdf");
        uploadResponse.setSize(TEST_FILE_SIZE);
        uploadResponse.setExtensionName("pdf");
        when(stoneApiService.tempFileUploadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID),
            anyInt(), eq("test.pdf"), eq("pdf"), eq(3), any(InputStream.class)))
            .thenReturn(uploadResponse);

        // When
        Watermark.Result result = openApiService.watermark(arg);

        // Then
        assertNotNull(result);
        assertEquals("uploaded/watermarked.pdf", result.getPath());
        assertEquals(TEST_FILE_SIZE, result.getSize());
        assertEquals("pdf", result.getExtension());
        verify(pdfBoxService).addTextWatermark(any(InputStream.class), anyString(), eq(arg));
        verify(fileCleanupManager).addFileToCleanup(watermarkPath);
    }

    @Test
    void testWatermark_IOException_ThrowsException() {
        // Given
        Watermark.Arg arg = new Watermark.Arg();
        arg.setEa(TEST_EA);
        arg.setEmployeeId(TEST_EMPLOYEE_ID);
        arg.setPath(TEST_PATH);
        arg.setExtension("pdf");

        when(fileStorageProxyService.getSizeByPath(TEST_PATH, TEST_EA, TEST_EMPLOYEE_ID))
            .thenReturn(TEST_FILE_SIZE);
        when(stoneApiService.downloadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID), eq(TEST_PATH)))
            .thenThrow(new RuntimeException("IO Error"));

        // When & Then
        BaseException exception = assertThrows(BaseException.class,
            () -> openApiService.watermark(arg));

        assertEquals(500, exception.getCode());
        assertTrue(exception.getMessage().contains("返回流异常"));
    }

    @Test
    void testAddPageQRCode_PDFFile_Success() throws IOException {
        // Given
        DocPageQRCode.Arg arg = new DocPageQRCode.Arg();
        arg.setEa(TEST_EA);
        arg.setEmployeeId(TEST_EMPLOYEE_ID);
        arg.setPath(TEST_PATH);
        arg.setExtension("pdf");
        arg.setFileName("test.pdf");
        arg.setPageQRCodes(Arrays.asList());
        arg.setOwnerPassword("owner");
        arg.setAccessPassword("access");

        when(fileStorageProxyService.getSizeByPath(TEST_PATH, TEST_EA, TEST_EMPLOYEE_ID))
            .thenReturn(TEST_FILE_SIZE);
        when(stoneApiService.downloadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID), eq(TEST_PATH)))
            .thenReturn(new ByteArrayInputStream("test content".getBytes()));

        Path qrCodePath = Paths.get("/tmp/qrcode.pdf");
        when(pdfBoxService.addQRCode(anyString(), anyList(), eq("owner"), eq("access")))
            .thenReturn(qrCodePath);

        StoneFileUploadResponse uploadResponse = new StoneFileUploadResponse();
        uploadResponse.setPath("uploaded/qrcode.pdf");
        uploadResponse.setSize(TEST_FILE_SIZE);
        uploadResponse.setExtensionName("pdf");
        when(stoneApiService.tempFileUploadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID),
            anyInt(), eq("test.pdf"), eq("pdf"), eq(3), any(InputStream.class)))
            .thenReturn(uploadResponse);

        // When
        DocPageQRCode.Result result = openApiService.addPageQRCode(arg);

        // Then
        assertNotNull(result);
        assertEquals("uploaded/qrcode.pdf", result.getPath());
        assertEquals(TEST_FILE_SIZE, result.getSize());
        assertEquals("pdf", result.getExtension());
        verify(pdfBoxService).addQRCode(anyString(), anyList(), eq("owner"), eq("access"));
        verify(fileCleanupManager).addFileToCleanup(qrCodePath.getParent());
    }

    @Test
    void testAddPageQRCode_WordFile_Success() throws IOException {
        // Given
        DocPageQRCode.Arg arg = new DocPageQRCode.Arg();
        arg.setEa(TEST_EA);
        arg.setEmployeeId(TEST_EMPLOYEE_ID);
        arg.setPath("test/document.docx");
        arg.setExtension("docx");
        arg.setFileName("test.docx");
        arg.setPageQRCodes(Arrays.asList());
        arg.setOwnerPassword("owner");
        arg.setAccessPassword("access");

        when(fileStorageProxyService.getSizeByPath("test/document.docx", TEST_EA, TEST_EMPLOYEE_ID))
            .thenReturn(TEST_FILE_SIZE);
        when(stoneApiService.downloadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID), eq("test/document.docx")))
            .thenReturn(new ByteArrayInputStream("test content".getBytes()));
        when(wordsProcess.officeToPdf(anyString())).thenReturn("/tmp/converted.pdf");

        Path qrCodePath = Paths.get("/tmp/qrcode.pdf");
        when(pdfBoxService.addQRCode(eq("/tmp/converted.pdf"), anyList(), eq("owner"), eq("access")))
            .thenReturn(qrCodePath);

        StoneFileUploadResponse uploadResponse = new StoneFileUploadResponse();
        uploadResponse.setPath("uploaded/qrcode.pdf");
        uploadResponse.setSize(TEST_FILE_SIZE);
        uploadResponse.setExtensionName("pdf");
        when(stoneApiService.tempFileUploadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID),
            anyInt(), eq("test.docx"), eq("pdf"), eq(3), any(InputStream.class)))
            .thenReturn(uploadResponse);

        // When
        DocPageQRCode.Result result = openApiService.addPageQRCode(arg);

        // Then
        assertNotNull(result);
        assertEquals("uploaded/qrcode.pdf", result.getPath());
        assertEquals(TEST_FILE_SIZE, result.getSize());
        assertEquals("pdf", result.getExtension());
        verify(wordsProcess).officeToPdf(anyString());
        verify(pdfBoxService).addQRCode(eq("/tmp/converted.pdf"), anyList(), eq("owner"), eq("access"));
        verify(fileCleanupManager).addFileToCleanup(qrCodePath.getParent());
    }

    @Test
    void testAddPageQRCode_IOException_ThrowsException() {
        // Given
        DocPageQRCode.Arg arg = new DocPageQRCode.Arg();
        arg.setEa(TEST_EA);
        arg.setEmployeeId(TEST_EMPLOYEE_ID);
        arg.setPath(TEST_PATH);
        arg.setExtension("pdf");

        when(fileStorageProxyService.getSizeByPath(TEST_PATH, TEST_EA, TEST_EMPLOYEE_ID))
            .thenReturn(TEST_FILE_SIZE);
        when(stoneApiService.downloadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID), eq(TEST_PATH)))
            .thenThrow(new RuntimeException("IO Error"));

        // When & Then
        BaseException exception = assertThrows(BaseException.class,
            () -> openApiService.addPageQRCode(arg));

        assertEquals(500, exception.getCode());
        assertTrue(exception.getMessage().contains("返回流异常"));
    }

    @Test
    void testDownloadSaveToLocal_IOException_ThrowsException() {
        // Given
        when(fileStorageProxyService.getSizeByPath(TEST_PATH, TEST_EA, TEST_EMPLOYEE_ID))
            .thenReturn(TEST_FILE_SIZE);
        when(stoneApiService.downloadByStream(eq(TEST_EA), eq(TEST_EMPLOYEE_ID), eq(TEST_PATH)))
            .thenThrow(new RuntimeException("Download failed"));

        // When & Then
        BaseException exception = assertThrows(BaseException.class,
            () -> openApiService.getMetaInfo(TEST_EA, TEST_EMPLOYEE_ID, TEST_PATH));

        assertEquals(500, exception.getCode());
        assertTrue(exception.getMessage().contains("下载文件,返回流异常"));
    }

    @Test
    void testMergerOffice_FileSizeExceeded_ThrowsException() {
        // Given
        List<String> paths = Arrays.asList("test/doc1.pdf", "test/doc2.pdf");
        when(fileStorageProxyService.getSizeByPaths(paths, TEST_EA, TEST_EMPLOYEE_ID))
            .thenReturn(104857601L); // Over 100MB

        // When & Then
        BaseException exception = assertThrows(BaseException.class,
            () -> openApiService.mergerOffice(TEST_EA, TEST_EMPLOYEE_ID, paths, "pdf"));

        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("文件不能为空且大小应在100M以内"));
    }
}
