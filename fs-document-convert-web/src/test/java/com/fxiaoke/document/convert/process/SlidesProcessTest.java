package com.fxiaoke.document.convert.process;

import com.fxiaoke.document.convert.process.options.SlidesOptions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SlidesProcessTest {

    @Mock
    private SlidesOptions slidesOptions;

    private SlidesProcess slidesProcess;

    @BeforeEach
    void setUp() {
        slidesProcess = new SlidesProcess(slidesOptions);
    }

    @Test
    void testConstructor() {
        // When
        SlidesProcess newProcess = new SlidesProcess(slidesOptions);

        // Then
        assertNotNull(newProcess);
    }

    @Test
    void testGetMetaInfo_WithPreprocessing() {
        // Given
        String filePath = "/test/presentation.pptx";
        boolean isPreprocessing = true;
        int expectedSlideCount = 10;
        when(slidesOptions.getMetaInfo(filePath, isPreprocessing)).thenReturn(expectedSlideCount);

        // When
        int result = slidesProcess.getMetaInfo(filePath, isPreprocessing);

        // Then
        assertEquals(expectedSlideCount, result);
        verify(slidesOptions).getMetaInfo(filePath, isPreprocessing);
    }

    @Test
    void testGetMetaInfo_WithoutPreprocessing() {
        // Given
        String filePath = "/test/presentation.pptx";
        boolean isPreprocessing = false;
        int expectedSlideCount = 10;
        when(slidesOptions.getMetaInfo(filePath, isPreprocessing)).thenReturn(expectedSlideCount);

        // When
        int result = slidesProcess.getMetaInfo(filePath, isPreprocessing);

        // Then
        assertEquals(expectedSlideCount, result);
        verify(slidesOptions).getMetaInfo(filePath, isPreprocessing);
    }

    @Test
    void testUpgrade() {
        // Given
        String filePath = "/test/presentation.ppt";
        String expectedUpgradedPath = "/test/presentation.pptx";
        when(slidesOptions.upgrade(filePath)).thenReturn(expectedUpgradedPath);

        // When
        String result = slidesProcess.upgrade(filePath);

        // Then
        assertEquals(expectedUpgradedPath, result);
        verify(slidesOptions).upgrade(filePath);
    }

    @Test
    void testToPng_SingleSlide() {
        // Given
        String filePath = "/test/presentation.pptx";
        int pageIndex = 1;
        String ea = "testEa";
        String path = "testPath";
        String expectedPngPath = "/test/slide1.png";
        when(slidesOptions.toPng(filePath, pageIndex, ea, path)).thenReturn(expectedPngPath);

        // When
        List<String> result = slidesProcess.toPng(filePath, pageIndex, ea, path);

        // Then
        assertEquals(1, result.size());
        assertEquals(expectedPngPath, result.get(0));
        verify(slidesOptions).toPng(filePath, pageIndex, ea, path);
    }

    @Test
    void testToPng_Range() {
        // Given
        String ea = "testEa";
        String path = "testPath";
        String filePath = "/test/presentation.pptx";
        int beginPageIndex = 1;
        int endPageIndex = 3;
        List<String> expectedPaths = Arrays.asList("/test/slide1.png", "/test/slide2.png", "/test/slide3.png");
        when(slidesOptions.toPng(ea, path, filePath, beginPageIndex, endPageIndex)).thenReturn(expectedPaths);

        // When
        List<String> result = slidesProcess.toPng(ea, path, filePath, beginPageIndex, endPageIndex);

        // Then
        assertEquals(expectedPaths, result);
        verify(slidesOptions).toPng(ea, path, filePath, beginPageIndex, endPageIndex);
    }

    @Test
    void testToPng_AllSlides() {
        // Given
        String ea = "testEa";
        String path = "testPath";
        String filePath = "/test/presentation.pptx";
        int pageCount = 5;
        List<String> expectedPaths = Arrays.asList(
            "/test/slide1.png", "/test/slide2.png", "/test/slide3.png", "/test/slide4.png", "/test/slide5.png");
        when(slidesOptions.toPng(ea, path, filePath, pageCount)).thenReturn(expectedPaths);

        // When
        List<String> result = slidesProcess.toPng(ea, path, filePath, pageCount);

        // Then
        assertEquals(expectedPaths, result);
        verify(slidesOptions).toPng(ea, path, filePath, pageCount);
    }

    @Test
    void testToHtml_SingleSlide() {
        // Given
        String filePath = "/test/presentation.pptx";
        int pageIndex = 1;
        String ea = "testEa";
        String path = "testPath";
        String expectedHtmlPath = "/test/slide1.html";
        when(slidesOptions.toHtml(filePath, pageIndex, ea, path)).thenReturn(expectedHtmlPath);

        // When
        List<String> result = slidesProcess.toHtml(filePath, pageIndex, ea, path);

        // Then
        assertEquals(1, result.size());
        assertEquals(expectedHtmlPath, result.get(0));
        verify(slidesOptions).toHtml(filePath, pageIndex, ea, path);
    }

    @Test
    void testToHtml_Range() {
        // Given
        String ea = "testEa";
        String path = "testPath";
        String filePath = "/test/presentation.pptx";
        int beginPageIndex = 1;
        int endPageIndex = 3;
        List<String> expectedPaths = Arrays.asList("/test/slide1.html", "/test/slide2.html", "/test/slide3.html");
        when(slidesOptions.toHtml(ea, path, filePath, beginPageIndex, endPageIndex)).thenReturn(expectedPaths);

        // When
        List<String> result = slidesProcess.toHtml(ea, path, filePath, beginPageIndex, endPageIndex);

        // Then
        assertEquals(expectedPaths, result);
        verify(slidesOptions).toHtml(ea, path, filePath, beginPageIndex, endPageIndex);
    }

    @Test
    void testToHtml_AllSlides() {
        // Given
        String ea = "testEa";
        String path = "testPath";
        String filePath = "/test/presentation.pptx";
        int pageCount = 5;
        List<String> expectedPaths = Arrays.asList(
            "/test/slide1.html", "/test/slide2.html", "/test/slide3.html", "/test/slide4.html", "/test/slide5.html");
        when(slidesOptions.toHtml(ea, path, filePath, pageCount)).thenReturn(expectedPaths);

        // When
        List<String> result = slidesProcess.toHtml(ea, path, filePath, pageCount);

        // Then
        assertEquals(expectedPaths, result);
        verify(slidesOptions).toHtml(ea, path, filePath, pageCount);
    }

    @Test
    void testOfficeToPdf() {
        // Given
        String filePath = "/test/presentation.pptx";
        String expectedPdfPath = "/test/presentation.pdf";
        when(slidesOptions.toPdf(filePath)).thenReturn(expectedPdfPath);

        // When
        String result = slidesProcess.officeToPdf(filePath);

        // Then
        assertEquals(expectedPdfPath, result);
        verify(slidesOptions).toPdf(filePath);
    }

    @Test
    void testMerge() {
        // Given
        List<String> filePaths = Arrays.asList("/test/ppt1.pptx", "/test/ppt2.pptx");
        String masterFilePath = "/test/merged.pptx";

        // When & Then - Should not throw exception (method is empty)
        assertDoesNotThrow(() -> slidesProcess.merge(filePaths, masterFilePath));
    }

    @Test
    void testToPng_EmptyResult() {
        // Given
        String filePath = "/test/presentation.pptx";
        int pageIndex = 1;
        String ea = "testEa";
        String path = "testPath";
        when(slidesOptions.toPng(filePath, pageIndex, ea, path)).thenReturn("");

        // When
        List<String> result = slidesProcess.toPng(filePath, pageIndex, ea, path);

        // Then
        assertEquals(1, result.size());
        assertEquals("", result.get(0));
        verify(slidesOptions).toPng(filePath, pageIndex, ea, path);
    }

    @Test
    void testToPng_Range_EmptyResult() {
        // Given
        String ea = "testEa";
        String path = "testPath";
        String filePath = "/test/presentation.pptx";
        int beginPageIndex = 1;
        int endPageIndex = 3;
        when(slidesOptions.toPng(ea, path, filePath, beginPageIndex, endPageIndex)).thenReturn(Collections.emptyList());

        // When
        List<String> result = slidesProcess.toPng(ea, path, filePath, beginPageIndex, endPageIndex);

        // Then
        assertTrue(result.isEmpty());
        verify(slidesOptions).toPng(ea, path, filePath, beginPageIndex, endPageIndex);
    }

    @Test
    void testToHtml_EmptyResult() {
        // Given
        String filePath = "/test/presentation.pptx";
        int pageIndex = 1;
        String ea = "testEa";
        String path = "testPath";
        when(slidesOptions.toHtml(filePath, pageIndex, ea, path)).thenReturn("");

        // When
        List<String> result = slidesProcess.toHtml(filePath, pageIndex, ea, path);

        // Then
        assertEquals(1, result.size());
        assertEquals("", result.get(0));
        verify(slidesOptions).toHtml(filePath, pageIndex, ea, path);
    }

    @Test
    void testToHtml_Range_EmptyResult() {
        // Given
        String ea = "testEa";
        String path = "testPath";
        String filePath = "/test/presentation.pptx";
        int beginPageIndex = 1;
        int endPageIndex = 3;
        when(slidesOptions.toHtml(ea, path, filePath, beginPageIndex, endPageIndex)).thenReturn(Collections.emptyList());

        // When
        List<String> result = slidesProcess.toHtml(ea, path, filePath, beginPageIndex, endPageIndex);

        // Then
        assertTrue(result.isEmpty());
        verify(slidesOptions).toHtml(ea, path, filePath, beginPageIndex, endPageIndex);
    }

    @Test
    void testGetMetaInfo_ZeroSlides() {
        // Given
        String filePath = "/test/empty.pptx";
        boolean isPreprocessing = false;
        when(slidesOptions.getMetaInfo(filePath, isPreprocessing)).thenReturn(0);

        // When
        int result = slidesProcess.getMetaInfo(filePath, isPreprocessing);

        // Then
        assertEquals(0, result);
        verify(slidesOptions).getMetaInfo(filePath, isPreprocessing);
    }

    @Test
    void testToPng_AllSlides_ZeroSlides() {
        // Given
        String ea = "testEa";
        String path = "testPath";
        String filePath = "/test/empty.pptx";
        int pageCount = 0;
        when(slidesOptions.toPng(ea, path, filePath, pageCount)).thenReturn(Collections.emptyList());

        // When
        List<String> result = slidesProcess.toPng(ea, path, filePath, pageCount);

        // Then
        assertTrue(result.isEmpty());
        verify(slidesOptions).toPng(ea, path, filePath, pageCount);
    }

    @Test
    void testToHtml_AllSlides_ZeroSlides() {
        // Given
        String ea = "testEa";
        String path = "testPath";
        String filePath = "/test/empty.pptx";
        int pageCount = 0;
        when(slidesOptions.toHtml(ea, path, filePath, pageCount)).thenReturn(Collections.emptyList());

        // When
        List<String> result = slidesProcess.toHtml(ea, path, filePath, pageCount);

        // Then
        assertTrue(result.isEmpty());
        verify(slidesOptions).toHtml(ea, path, filePath, pageCount);
    }

    @Test
    void testMerge_WithNullParameters() {
        // When & Then - Should not throw exception (method is empty)
        assertDoesNotThrow(() -> slidesProcess.merge(null, null));
    }

    @Test
    void testMerge_WithEmptyList() {
        // Given
        List<String> emptyList = Collections.emptyList();
        String masterFilePath = "/test/merged.pptx";

        // When & Then - Should not throw exception (method is empty)
        assertDoesNotThrow(() -> slidesProcess.merge(emptyList, masterFilePath));
    }
}
