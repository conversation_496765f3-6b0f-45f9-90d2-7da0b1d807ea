package com.fxiaoke.document.convert.process;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import ws.schild.jave.info.AudioInfo;
import ws.schild.jave.info.MultimediaInfo;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AudioConvertProgressListenerTest {

    @Mock
    private MultimediaInfo multimediaInfo;

    @Mock
    private AudioInfo audioInfo;

    private AudioConvertProgressListener listener;
    private final String testFilename = "test-audio.mp3";

    @BeforeEach
    void setUp() {
        listener = new AudioConvertProgressListener(testFilename);
    }

    @Test
    void testConstructor() {
        // When
        AudioConvertProgressListener newListener = new AudioConvertProgressListener("another-file.wav");

        // Then
        assertNotNull(newListener);
    }

    @Test
    void testSourceInfo_WithAudioInfo() {
        // Given
        when(multimediaInfo.getAudio()).thenReturn(audioInfo);
        when(multimediaInfo.getFormat()).thenReturn("mp3");
        when(multimediaInfo.getDuration()).thenReturn(120000L); // 2 minutes
        when(audioInfo.getBitRate()).thenReturn(128000);
        when(audioInfo.getChannels()).thenReturn(2);
        when(audioInfo.getSamplingRate()).thenReturn(44100);

        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> listener.sourceInfo(multimediaInfo));

        // Verify methods were called (allowing multiple calls)
        verify(multimediaInfo, atLeastOnce()).getAudio();
        verify(multimediaInfo, atLeastOnce()).getFormat();
        verify(multimediaInfo, atLeastOnce()).getDuration();
        verify(audioInfo, atLeastOnce()).getBitRate();
        verify(audioInfo, atLeastOnce()).getChannels();
        verify(audioInfo, atLeastOnce()).getSamplingRate();
    }

    @Test
    void testSourceInfo_WithoutAudioInfo() {
        // Given
        when(multimediaInfo.getAudio()).thenReturn(null);

        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> listener.sourceInfo(multimediaInfo));

        // Verify getAudio was called
        verify(multimediaInfo).getAudio();
        // Other methods should not be called when audio is null
        verify(multimediaInfo, never()).getFormat();
        verify(multimediaInfo, never()).getDuration();
    }

    @Test
    void testProgress_AtTenPercentIntervals() {
        // Given & When & Then - Should not throw exception for 10% intervals
        assertDoesNotThrow(() -> listener.progress(0));    // 0%
        assertDoesNotThrow(() -> listener.progress(100));  // 10%
        assertDoesNotThrow(() -> listener.progress(200));  // 20%
        assertDoesNotThrow(() -> listener.progress(500));  // 50%
        assertDoesNotThrow(() -> listener.progress(1000)); // 100%
    }

    @Test
    void testProgress_NotAtTenPercentIntervals() {
        // Given & When & Then - Should not throw exception for non-10% intervals
        assertDoesNotThrow(() -> listener.progress(50));   // 5%
        assertDoesNotThrow(() -> listener.progress(150));  // 15%
        assertDoesNotThrow(() -> listener.progress(250));  // 25%
        assertDoesNotThrow(() -> listener.progress(999));  // 99.9%
    }

    @Test
    void testProgress_EdgeCases() {
        // Given & When & Then - Should handle edge cases
        assertDoesNotThrow(() -> listener.progress(-1));   // Negative
        assertDoesNotThrow(() -> listener.progress(1001)); // Over 100%
        assertDoesNotThrow(() -> listener.progress(Integer.MAX_VALUE));
        assertDoesNotThrow(() -> listener.progress(Integer.MIN_VALUE));
    }

    @Test
    void testMessage_WithValidMessage() {
        // Given
        String testMessage = "Encoding in progress...";

        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> listener.message(testMessage));
    }

    @Test
    void testMessage_WithEmptyMessage() {
        // Given
        String emptyMessage = "";

        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> listener.message(emptyMessage));
    }

    @Test
    void testMessage_WithNullMessage() {
        // Given
        String nullMessage = null;

        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> listener.message(nullMessage));
    }

    @Test
    void testMessage_WithSpecialCharacters() {
        // Given
        String specialMessage = "编码消息: 特殊字符 !@#$%^&*()";

        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> listener.message(specialMessage));
    }

    @Test
    void testMessage_WithLongMessage() {
        // Given
        StringBuilder longMessage = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longMessage.append("Long message part ").append(i).append(" ");
        }

        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> listener.message(longMessage.toString()));
    }

    @Test
    void testSourceInfo_WithCompleteAudioInfo() {
        // Given
        when(multimediaInfo.getAudio()).thenReturn(audioInfo);
        when(multimediaInfo.getFormat()).thenReturn("wav");
        when(multimediaInfo.getDuration()).thenReturn(300000L); // 5 minutes
        when(audioInfo.getBitRate()).thenReturn(320000);
        when(audioInfo.getChannels()).thenReturn(1); // Mono
        when(audioInfo.getSamplingRate()).thenReturn(48000);

        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> listener.sourceInfo(multimediaInfo));

        // Verify all audio info methods were called
        verify(audioInfo).getBitRate();
        verify(audioInfo).getChannels();
        verify(audioInfo).getSamplingRate();
    }

    @Test
    void testSourceInfo_WithZeroValues() {
        // Given
        when(multimediaInfo.getAudio()).thenReturn(audioInfo);
        when(multimediaInfo.getFormat()).thenReturn("unknown");
        when(multimediaInfo.getDuration()).thenReturn(0L);
        when(audioInfo.getBitRate()).thenReturn(0);
        when(audioInfo.getChannels()).thenReturn(0);
        when(audioInfo.getSamplingRate()).thenReturn(0);

        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> listener.sourceInfo(multimediaInfo));
    }

    @Test
    void testProgress_SequentialCalls() {
        // Given & When & Then - Should handle sequential progress calls
        for (int i = 0; i <= 1000; i += 50) {
            final int progress = i;
            assertDoesNotThrow(() -> listener.progress(progress));
        }
    }

    @Test
    void testAllMethods_WithDifferentFilenames() {
        // Given
        AudioConvertProgressListener listener1 = new AudioConvertProgressListener("file1.mp3");
        AudioConvertProgressListener listener2 = new AudioConvertProgressListener("file2.wav");
        AudioConvertProgressListener listener3 = new AudioConvertProgressListener("file with spaces.opus");

        when(multimediaInfo.getAudio()).thenReturn(audioInfo);
        when(multimediaInfo.getFormat()).thenReturn("mp3");
        when(multimediaInfo.getDuration()).thenReturn(60000L);
        when(audioInfo.getBitRate()).thenReturn(128000);
        when(audioInfo.getChannels()).thenReturn(2);
        when(audioInfo.getSamplingRate()).thenReturn(44100);

        // When & Then - All should work without exception
        assertDoesNotThrow(() -> {
            listener1.sourceInfo(multimediaInfo);
            listener1.progress(500);
            listener1.message("Test message 1");

            listener2.sourceInfo(multimediaInfo);
            listener2.progress(750);
            listener2.message("Test message 2");

            listener3.sourceInfo(multimediaInfo);
            listener3.progress(1000);
            listener3.message("Test message 3");
        });
    }

    @Test
    void testProgress_PercentageCalculation() {
        // Test that the percentage calculation works correctly
        // permil / 10 = percentage
        // 0 permil = 0%
        // 100 permil = 10%
        // 1000 permil = 100%

        // When & Then - Should not throw exception for various permil values
        assertDoesNotThrow(() -> listener.progress(0));    // 0%
        assertDoesNotThrow(() -> listener.progress(1));    // 0.1%
        assertDoesNotThrow(() -> listener.progress(10));   // 1%
        assertDoesNotThrow(() -> listener.progress(100));  // 10%
        assertDoesNotThrow(() -> listener.progress(500));  // 50%
        assertDoesNotThrow(() -> listener.progress(999));  // 99.9%
        assertDoesNotThrow(() -> listener.progress(1000)); // 100%
    }

    @Test
    void testSourceInfo_ExceptionHandling() {
        // Given
        when(multimediaInfo.getAudio()).thenReturn(audioInfo);
        when(multimediaInfo.getFormat()).thenThrow(new RuntimeException("Format error"));

        // When & Then - Exception will be thrown since the method doesn't handle it
        assertThrows(RuntimeException.class, () -> listener.sourceInfo(multimediaInfo));
    }

    @Test
    void testListenerWithNullFilename() {
        // Given
        AudioConvertProgressListener nullFilenameListener = new AudioConvertProgressListener(null);

        when(multimediaInfo.getAudio()).thenReturn(audioInfo);
        when(multimediaInfo.getFormat()).thenReturn("mp3");
        when(multimediaInfo.getDuration()).thenReturn(60000L);
        when(audioInfo.getBitRate()).thenReturn(128000);
        when(audioInfo.getChannels()).thenReturn(2);
        when(audioInfo.getSamplingRate()).thenReturn(44100);

        // When & Then - Should handle null filename gracefully
        assertDoesNotThrow(() -> {
            nullFilenameListener.sourceInfo(multimediaInfo);
            nullFilenameListener.progress(500);
            nullFilenameListener.message("Test message");
        });
    }

    @Test
    void testListenerWithEmptyFilename() {
        // Given
        AudioConvertProgressListener emptyFilenameListener = new AudioConvertProgressListener("");

        when(multimediaInfo.getAudio()).thenReturn(audioInfo);
        when(multimediaInfo.getFormat()).thenReturn("mp3");
        when(multimediaInfo.getDuration()).thenReturn(60000L);
        when(audioInfo.getBitRate()).thenReturn(128000);
        when(audioInfo.getChannels()).thenReturn(2);
        when(audioInfo.getSamplingRate()).thenReturn(44100);

        // When & Then - Should handle empty filename gracefully
        assertDoesNotThrow(() -> {
            emptyFilenameListener.sourceInfo(multimediaInfo);
            emptyFilenameListener.progress(500);
            emptyFilenameListener.message("Test message");
        });
    }
}
