package com.fxiaoke.document.convert.utils;

import com.fxiaoke.document.convert.domain.exception.BaseException;
import com.fxiaoke.document.convert.domain.model.CorrectionLevel;
import com.fxiaoke.document.convert.domain.model.QrCodeParams;
import com.google.zxing.BinaryBitmap;
import com.google.zxing.DecodeHintType;
import com.google.zxing.MultiFormatReader;
import com.google.zxing.NotFoundException;
import com.google.zxing.Result;
import com.google.zxing.client.j2se.BufferedImageLuminanceSource;
import com.google.zxing.common.HybridBinarizer;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.EnumMap;
import java.util.Map;
import javax.imageio.ImageIO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mockito;

class QrCodeUtilTest {

  @Test
  void testGenerateQrCode_withBasicParams() throws Exception {
    // 准备参数
    QrCodeParams params = QrCodeParams.builder()
        .content("测试二维码内容")
        .size(200)
        .correctionLevel(CorrectionLevel.M)
        .build();

    // 生成二维码
    byte[] qrCodeData = QrCodeUtil.generateQrCode(params);

    // 验证生成的二维码
    Assertions.assertNotNull(qrCodeData);
    Assertions.assertTrue(qrCodeData.length > 0);

    // 解码验证内容
    String decodedContent = decodeQrCode(qrCodeData);
    Assertions.assertEquals(params.getContent(), decodedContent);
  }

  @ParameterizedTest
  @EnumSource(CorrectionLevel.class)
  void testGenerateQrCode_withDifferentCorrectionLevels(CorrectionLevel level) throws Exception {
    QrCodeParams params = QrCodeParams.builder()
        .content("测试不同纠错级别: " + level.name())
        .size(200)
        .correctionLevel(level)
        .build();

    byte[] qrCodeData = QrCodeUtil.generateQrCode(params);

    // 验证生成的二维码
    Assertions.assertNotNull(qrCodeData);
    Assertions.assertTrue(qrCodeData.length > 0);

    // 解码验证内容
    String decodedContent = decodeQrCode(qrCodeData);
    Assertions.assertEquals(params.getContent(), decodedContent);
  }

  @ParameterizedTest
  @ValueSource(ints = {84, 200, 500, 1000})
  void testGenerateQrCode_withDifferentSizes(int size) throws Exception {
    QrCodeParams params = QrCodeParams.builder()
        .content("测试不同尺寸")
        .size(size)
        .correctionLevel(CorrectionLevel.M)
        .build();

    byte[] qrCodeData = QrCodeUtil.generateQrCode(params);

    // 验证生成的二维码
    Assertions.assertNotNull(qrCodeData);
    Assertions.assertTrue(qrCodeData.length > 0);

    // 加载图片验证尺寸
    BufferedImage image = ImageIO.read(new ByteArrayInputStream(qrCodeData));
    Assertions.assertEquals(size, image.getWidth());
    Assertions.assertEquals(size, image.getHeight());

    // 解码验证内容
    String decodedContent = decodeQrCode(qrCodeData);
    Assertions.assertEquals(params.getContent(), decodedContent);
  }

  @Test
  void testGenerateQrCode_withLogo() throws Exception {
    // 准备一个简单的logo（1x1像素的黑色图片）
    String simpleLogoBase64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAADElEQVQI12P4//8/AAX+Av7czFnnAAAAAElFTkSuQmCC";

    QrCodeParams params = QrCodeParams.builder()
        .content("测试带Logo的二维码")
        .size(300)
        .correctionLevel(CorrectionLevel.H) // 使用高纠错级别以支持Logo
        .logoBase64(simpleLogoBase64)
        .build();

    byte[] qrCodeData = QrCodeUtil.generateQrCode(params);

    // 验证生成的二维码
    Assertions.assertNotNull(qrCodeData);
    Assertions.assertTrue(qrCodeData.length > 0);

    // 解码验证内容
    String decodedContent = decodeQrCode(qrCodeData);
    Assertions.assertEquals(params.getContent(), decodedContent);
  }

  @Test
  void testGenerateQrCodeBase64() throws Exception {
    QrCodeParams params = QrCodeParams.builder()
        .content("测试Base64编码")
        .size(200)
        .correctionLevel(CorrectionLevel.M)
        .build();

    String base64QrCode = QrCodeUtil.generateQrCodeBase64(params);

    // 验证Base64编码
    Assertions.assertNotNull(base64QrCode);
    Assertions.assertFalse(base64QrCode.isEmpty());

    // 解码Base64并验证二维码内容
    byte[] decodedBytes = Base64.getDecoder().decode(base64QrCode);
    String decodedContent = decodeQrCode(decodedBytes);
    Assertions.assertEquals(params.getContent(), decodedContent);
  }

  @Test
  void testGenerateQrCode_invalidParams() throws Exception {
    // 创建一个会在validateContent方法返回false的模拟对象
    QrCodeParams mockEmptyContentParams = Mockito.mock(QrCodeParams.class);
    Mockito.Mockito.lenient().when(mockEmptyContentParams.isContentValid()).thenReturn(false);
    Mockito.Mockito.lenient().when(mockEmptyContentParams.getContent()).thenReturn("");
    Mockito.Mockito.lenient().when(mockEmptyContentParams.getSize()).thenReturn(200);
    Mockito.Mockito.lenient().when(mockEmptyContentParams.getCorrectionLevel()).thenReturn(CorrectionLevel.M);
    
    // 验证空内容会抛出异常
    BaseException emptyContentException = Assertions.assertThrows(BaseException.class, 
        () -> QrCodeUtil.generateQrCode(mockEmptyContentParams));
    Assertions.assertTrue(emptyContentException.getReason().contains("QrCodeUtil"));
    
    // 创建一个会在validateLogo方法返回false的模拟对象
    QrCodeParams mockInvalidLogoParams = Mockito.mock(QrCodeParams.class);
    Mockito.Mockito.lenient().when(mockInvalidLogoParams.isContentValid()).thenReturn(true);
    Mockito.Mockito.lenient().when(mockInvalidLogoParams.isLogoValid()).thenReturn(false);
    Mockito.Mockito.lenient().when(mockInvalidLogoParams.getContent()).thenReturn("测试内容");
    Mockito.Mockito.lenient().when(mockInvalidLogoParams.getSize()).thenReturn(200);
    Mockito.Mockito.lenient().when(mockInvalidLogoParams.getCorrectionLevel()).thenReturn(CorrectionLevel.M);
    Mockito.Mockito.lenient().when(mockInvalidLogoParams.getLogoBase64()).thenReturn("invalid_base64");
    
    // 验证无效Logo会抛出异常
    BaseException invalidLogoException = Assertions.assertThrows(BaseException.class, 
        () -> QrCodeUtil.generateQrCode(mockInvalidLogoParams));
    Assertions.assertTrue(invalidLogoException.getReason().contains("QrCodeUtil"));
    
    // 测试尺寸过小的场景
    QrCodeParams smallSizeParams = QrCodeParams.builder()
        .content("测试内容")
        .size(50) // 小于最小尺寸84
        .correctionLevel(CorrectionLevel.M)
        .build();
    
    // 因为QrCodeUtil在generateQrCode方法中会调用validateContent和validateLogo
    // 所以可以使用模拟对象测试尺寸问题
    QrCodeParams mockSmallSizeParams = Mockito.mock(QrCodeParams.class);
    Mockito.Mockito.lenient().when(mockSmallSizeParams.isContentValid()).thenReturn(false); // 尺寸过小模拟验证失败
    Mockito.Mockito.lenient().when(mockSmallSizeParams.getContent()).thenReturn("测试内容");
    Mockito.Mockito.lenient().when(mockSmallSizeParams.getSize()).thenReturn(50);
    Mockito.Mockito.lenient().when(mockSmallSizeParams.getCorrectionLevel()).thenReturn(CorrectionLevel.M);
    
    BaseException smallSizeException = Assertions.assertThrows(BaseException.class, 
        () -> QrCodeUtil.generateQrCode(mockSmallSizeParams));
    Assertions.assertTrue(smallSizeException.getReason().contains("QrCodeUtil"));
  }

  @Test
  void testGenerateQrCode_withLongContent() {
    // 创建一个长内容
    StringBuilder longContent = new StringBuilder();
    longContent.append("测试".repeat(500));
    
    // 通过模拟对象测试内容过长的情况
    QrCodeParams mockLongContentParams = Mockito.mock(QrCodeParams.class);
    Mockito.Mockito.lenient().when(mockLongContentParams.isContentValid()).thenReturn(false); // 内容过长会验证失败
    Mockito.Mockito.lenient().when(mockLongContentParams.getContent()).thenReturn(longContent.toString());
    Mockito.Mockito.lenient().when(mockLongContentParams.getSize()).thenReturn(500);
    Mockito.Mockito.lenient().when(mockLongContentParams.getCorrectionLevel()).thenReturn(CorrectionLevel.L);
    
    BaseException longContentException = Assertions.assertThrows(BaseException.class, 
        () -> QrCodeUtil.generateQrCode(mockLongContentParams));
    Assertions.assertTrue(longContentException.getReason().contains("QrCodeUtil"));
    
    // 也可以直接使用一个实际超出容量的QrCodeParams对象
    QrCodeParams realLongContentParams = QrCodeParams.builder()
        .content(longContent.toString())
        .size(84) // 使用最小尺寸，使其更容易超出容量
        .correctionLevel(CorrectionLevel.H) // 使用高纠错级别，减少可容纳内容
        .build();
    
    // 如果实际验证失败，将抛出异常
    if (!realLongContentParams.isContentValid()) {
      BaseException exception = Assertions.assertThrows(BaseException.class,
          () -> QrCodeUtil.generateQrCode(realLongContentParams));
      Assertions.assertTrue(exception.getReason().contains("QrCodeUtil"));
    }
  }

  @Test
  void testPrivateConstructor() throws Exception {
    // 测试私有构造函数
    java.lang.reflect.Constructor<QrCodeUtil> constructor = QrCodeUtil.class.getDeclaredConstructor();
    constructor.setAccessible(true);
    constructor.newInstance();
  }

  /**
   * 工具方法：解码二维码图片数据
   */
  private String decodeQrCode(byte[] qrCodeData) throws IOException, NotFoundException {
    BufferedImage bufferedImage = ImageIO.read(new ByteArrayInputStream(qrCodeData));
    BinaryBitmap binaryBitmap = new BinaryBitmap(
        new HybridBinarizer(
            new BufferedImageLuminanceSource(bufferedImage)
        )
    );

    Map<DecodeHintType, Object> hints = new EnumMap<>(DecodeHintType.class);
    hints.put(DecodeHintType.CHARACTER_SET, "UTF-8");

    Result result = new MultiFormatReader().decode(binaryBitmap, hints);
    return result.getText();
  }
}