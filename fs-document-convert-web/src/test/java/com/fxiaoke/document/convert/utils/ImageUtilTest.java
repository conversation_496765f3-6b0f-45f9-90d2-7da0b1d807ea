package com.fxiaoke.document.convert.utils;

import com.fxiaoke.document.convert.domain.model.ImageDimension;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

class ImageUtilTest {

    @Test
    void testGetCompressingWH_NoCompressionNeeded() {
        // Given
        int width = 800;
        int height = 600;
        double maxResolution = 1000000; // 1M pixels

        // When
        ImageDimension result = ImageUtil.getCompressingWH(width, height, maxResolution);

        // Then
        assertEquals(800, result.getWidth());
        assertEquals(600, result.getHeight());
    }

    @Test
    void testGetCompressingWH_CompressionNeeded() {
        // Given
        int width = 2000;
        int height = 2000;
        double maxResolution = 1000000; // 1M pixels

        // When
        ImageDimension result = ImageUtil.getCompressingWH(width, height, maxResolution);

        // Then
        assertTrue(result.getWidth() < width);
        assertTrue(result.getHeight() < height);
        assertTrue(result.getWidth() * result.getHeight() <= maxResolution);
    }

    @Test
    void testGetCompressingWH_ExactMaxResolution() {
        // Given
        int width = 1000;
        int height = 1000;
        double maxResolution = 1000000; // Exactly 1M pixels

        // When
        ImageDimension result = ImageUtil.getCompressingWH(width, height, maxResolution);

        // Then
        assertEquals(1000, result.getWidth());
        assertEquals(1000, result.getHeight());
    }

    @Test
    void testGetCompressingWH_SmallImage() {
        // Given
        int width = 100;
        int height = 100;
        double maxResolution = 1000000;

        // When
        ImageDimension result = ImageUtil.getCompressingWH(width, height, maxResolution);

        // Then
        assertEquals(100, result.getWidth());
        assertEquals(100, result.getHeight());
    }

    @Test
    void testGetCompressingWH_ZeroMaxResolution() {
        // Given
        int width = 800;
        int height = 600;
        double maxResolution = 0;

        // When
        ImageDimension result = ImageUtil.getCompressingWH(width, height, maxResolution);

        // Then
        assertTrue(result.getWidth() < width);
        assertTrue(result.getHeight() < height);
    }

    @Test
    void testGetCompressingWH_VeryLargeImage() {
        // Given
        int width = 10000;
        int height = 10000;
        double maxResolution = 100000; // 100K pixels

        // When
        ImageDimension result = ImageUtil.getCompressingWH(width, height, maxResolution);

        // Then
        assertTrue(result.getWidth() < width);
        assertTrue(result.getHeight() < height);
        assertTrue(result.getWidth() * result.getHeight() <= maxResolution);
    }

    @ParameterizedTest
    @ValueSource(strings = {"image/gif", "image/gif;charset=utf-8"})
    void testIsSupportedConvertImageType_SupportedTypes(String contentType) {
        // When
        boolean result = ImageUtil.isSupportedConvertImageType(contentType);

        // Then
        assertTrue(result);
    }

    @ParameterizedTest
    @ValueSource(strings = {"image/jpeg", "image/png", "image/bmp", "text/plain", "application/pdf"})
    void testIsSupportedConvertImageType_UnsupportedTypes(String contentType) {
        // When
        boolean result = ImageUtil.isSupportedConvertImageType(contentType);

        // Then
        assertFalse(result);
    }

    @ParameterizedTest
    @NullAndEmptySource
    void testIsSupportedConvertImageType_NullOrEmpty(String contentType) {
        // When
        boolean result = ImageUtil.isSupportedConvertImageType(contentType);

        // Then
        assertFalse(result);
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "image/jpeg",
        "image/png",
        "image/gif",
        "image/jpeg;charset=utf-8",
        "image/png;boundary=something"
    })
    void testIsSupportedCompressingImageType_SupportedTypes(String contentType) {
        // When
        boolean result = ImageUtil.isSupportedCompressingImageType(contentType);

        // Then
        assertTrue(result);
    }

    @ParameterizedTest
    @ValueSource(strings = {"image/bmp", "image/tiff", "text/plain", "application/pdf", "image/webp", "image/JPEG", "image/PNG", "image/GIF"})
    void testIsSupportedCompressingImageType_UnsupportedTypes(String contentType) {
        // When
        boolean result = ImageUtil.isSupportedCompressingImageType(contentType);

        // Then
        assertFalse(result);
    }

    @ParameterizedTest
    @NullAndEmptySource
    void testIsSupportedCompressingImageType_NullOrEmpty(String contentType) {
        // When
        boolean result = ImageUtil.isSupportedCompressingImageType(contentType);

        // Then
        assertFalse(result);
    }

    @Test
    void testGif2Png_ValidGifData() throws IOException {
        // Given - Create a minimal GIF data (this is a simplified test)
        // In a real scenario, you would use actual GIF bytes
        byte[] gifData = createMinimalGifData();

        // When & Then - This will likely throw an exception with our minimal data
        // but we're testing that the method exists and handles the call
        assertThrows(Exception.class, () -> ImageUtil.gif2Png(gifData));
    }

    @Test
    void testGif2Png_NullData() {
        // When & Then
        assertThrows(Exception.class, () -> ImageUtil.gif2Png(null));
    }

    @Test
    void testGif2Png_EmptyData() {
        // Given
        byte[] emptyData = new byte[0];

        // When & Then
        assertThrows(Exception.class, () -> ImageUtil.gif2Png(emptyData));
    }

    @Test
    void testGif2Png_InvalidData() {
        // Given
        byte[] invalidData = "not a gif".getBytes();

        // When & Then
        assertThrows(Exception.class, () -> ImageUtil.gif2Png(invalidData));
    }

    private byte[] createMinimalGifData() {
        // Create a minimal GIF header (this won't be a valid GIF, but tests the method signature)
        byte[] gifData = new byte[20];
        // GIF signature
        gifData[0] = (byte) 0x47; // G
        gifData[1] = (byte) 0x49; // I
        gifData[2] = (byte) 0x46; // F
        gifData[3] = (byte) 0x38; // 8
        gifData[4] = (byte) 0x39; // 9
        gifData[5] = (byte) 0x61; // a
        return gifData;
    }
}
