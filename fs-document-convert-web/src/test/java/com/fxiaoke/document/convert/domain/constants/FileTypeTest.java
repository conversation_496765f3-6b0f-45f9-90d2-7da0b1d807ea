package com.fxiaoke.document.convert.domain.constants;

import com.fxiaoke.document.convert.domain.exception.BaseException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.*;

class FileTypeTest {

    @Test
    void testEnumValues() {
        // Test that all enum values exist
        assertEquals("doc", FileType.DOC.getFileTypeName());
        assertEquals("docx", FileType.DOCX.getFileTypeName());
        assertEquals("ppt", FileType.PPT.getFileTypeName());
        assertEquals("pptx", FileType.PPTX.getFileTypeName());
        assertEquals("xls", FileType.XLS.getFileTypeName());
        assertEquals("xlsx", FileType.XLSX.getFileTypeName());
        assertEquals("pdf", FileType.PDF.getFileTypeName());
    }

    @ParameterizedTest
    @ValueSource(strings = {"doc", "docx", "ppt", "pptx", "xls", "xlsx", "pdf"})
    void testIsOffice_SupportedTypes(String fileType) {
        assertTrue(FileType.isOffice(fileType));
    }

    @ParameterizedTest
    @ValueSource(strings = {"DOC", "DOCX", "PPT", "PPTX", "XLS", "XLSX", "PDF"})
    void testIsOffice_SupportedTypes_UpperCase(String fileType) {
        assertTrue(FileType.isOffice(fileType));
    }

    @ParameterizedTest
    @ValueSource(strings = {"txt", "jpg", "png", "mp3", "unknown"})
    void testIsOffice_UnsupportedTypes(String fileType) {
        assertFalse(FileType.isOffice(fileType));
    }

    @ParameterizedTest
    @ValueSource(strings = {"xls", "xlsx"})
    void testIsCells_SupportedTypes(String fileType) {
        assertTrue(FileType.isCells(fileType));
    }

    @ParameterizedTest
    @ValueSource(strings = {"XLS", "XLSX"})
    void testIsCells_SupportedTypes_UpperCase(String fileType) {
        assertTrue(FileType.isCells(fileType));
    }

    @ParameterizedTest
    @ValueSource(strings = {"doc", "docx", "ppt", "pptx", "pdf", "txt"})
    void testIsCells_UnsupportedTypes(String fileType) {
        assertFalse(FileType.isCells(fileType));
    }

    @Test
    void testIsXls_True() {
        assertTrue(FileType.isXls("xls"));
        assertTrue(FileType.isXls("XLS"));
    }

    @Test
    void testIsXls_False() {
        assertFalse(FileType.isXls("xlsx"));
        assertFalse(FileType.isXls("doc"));
        assertFalse(FileType.isXls("pdf"));
    }

    @ParameterizedTest
    @ValueSource(strings = {"doc", "docx"})
    void testIsWords_SupportedTypes(String fileType) {
        assertTrue(FileType.isWords(fileType));
    }

    @ParameterizedTest
    @ValueSource(strings = {"DOC", "DOCX"})
    void testIsWords_SupportedTypes_UpperCase(String fileType) {
        assertTrue(FileType.isWords(fileType));
    }

    @ParameterizedTest
    @ValueSource(strings = {"xls", "xlsx", "ppt", "pptx", "pdf"})
    void testIsWords_UnsupportedTypes(String fileType) {
        assertFalse(FileType.isWords(fileType));
    }

    @Test
    void testIsDoc_True() {
        assertTrue(FileType.isDoc("doc"));
        assertTrue(FileType.isDoc("DOC"));
    }

    @Test
    void testIsDoc_False() {
        assertFalse(FileType.isDoc("docx"));
        assertFalse(FileType.isDoc("pdf"));
        assertFalse(FileType.isDoc("xls"));
    }

    @ParameterizedTest
    @ValueSource(strings = {"ppt", "pptx"})
    void testIsSlides_SupportedTypes(String fileType) {
        assertTrue(FileType.isSlides(fileType));
    }

    @ParameterizedTest
    @ValueSource(strings = {"PPT", "PPTX"})
    void testIsSlides_SupportedTypes_UpperCase(String fileType) {
        assertTrue(FileType.isSlides(fileType));
    }

    @ParameterizedTest
    @ValueSource(strings = {"doc", "docx", "xls", "xlsx", "pdf"})
    void testIsSlides_UnsupportedTypes(String fileType) {
        assertFalse(FileType.isSlides(fileType));
    }

    @Test
    void testIsPpt_True() {
        assertTrue(FileType.isPpt("ppt"));
        assertTrue(FileType.isPpt("PPT"));
    }

    @Test
    void testIsPpt_False() {
        assertFalse(FileType.isPpt("pptx"));
        assertFalse(FileType.isPpt("doc"));
        assertFalse(FileType.isPpt("pdf"));
    }

    @Test
    void testIsPDF_True() {
        assertTrue(FileType.isPDF("pdf"));
        assertTrue(FileType.isPDF("PDF"));
    }

    @Test
    void testIsPDF_False() {
        assertFalse(FileType.isPDF("doc"));
        assertFalse(FileType.isPDF("xls"));
        assertFalse(FileType.isPDF("ppt"));
    }

    @ParameterizedTest
    @ValueSource(strings = {"jpg", "jpeg", "png", "bmp", "svg"})
    void testIsImage_SupportedTypes(String fileType) {
        assertTrue(FileType.isImage(fileType));
    }

    @ParameterizedTest
    @ValueSource(strings = {"JPG", "JPEG", "PNG", "BMP", "SVG"})
    void testIsImage_SupportedTypes_UpperCase(String fileType) {
        assertTrue(FileType.isImage(fileType));
    }

    @ParameterizedTest
    @ValueSource(strings = {"doc", "pdf", "txt", "mp3"})
    void testIsImage_UnsupportedTypes(String fileType) {
        assertFalse(FileType.isImage(fileType));
    }

    @ParameterizedTest
    @ValueSource(strings = {"mp4", "mp3", "mov"})
    void testIsMultimedia_SupportedTypes(String fileType) {
        assertTrue(FileType.isMultimedia(fileType));
    }

    @ParameterizedTest
    @ValueSource(strings = {"MP4", "MP3", "MOV"})
    void testIsMultimedia_SupportedTypes_UpperCase(String fileType) {
        assertTrue(FileType.isMultimedia(fileType));
    }

    @ParameterizedTest
    @ValueSource(strings = {"doc", "pdf", "jpg", "xls"})
    void testIsMultimedia_UnsupportedTypes(String fileType) {
        assertFalse(FileType.isMultimedia(fileType));
    }

    @ParameterizedTest
    @ValueSource(strings = {"txt", "text", "html", "htm", "xml", "json", "aspx", "csv", "sql", "msg", "js", "css", "groovy", "java"})
    void testIsText_SupportedTypes(String fileType) {
        assertTrue(FileType.isText(fileType));
    }

    @ParameterizedTest
    @ValueSource(strings = {"TXT", "TEXT", "HTML", "HTM", "XML", "JSON", "ASPX", "CSV", "SQL", "MSG", "JS", "CSS", "GROOVY", "JAVA"})
    void testIsText_SupportedTypes_UpperCase(String fileType) {
        assertTrue(FileType.isText(fileType));
    }

    @ParameterizedTest
    @ValueSource(strings = {"doc", "pdf", "jpg", "mp3"})
    void testIsText_UnsupportedTypes(String fileType) {
        assertFalse(FileType.isText(fileType));
    }

    @Test
    void testValueOfName_ValidTypes() {
        assertEquals(FileType.DOC, FileType.valueOfName("doc"));
        assertEquals(FileType.DOCX, FileType.valueOfName("docx"));
        assertEquals(FileType.PPT, FileType.valueOfName("ppt"));
        assertEquals(FileType.PPTX, FileType.valueOfName("pptx"));
        assertEquals(FileType.XLS, FileType.valueOfName("xls"));
        assertEquals(FileType.XLSX, FileType.valueOfName("xlsx"));
        assertEquals(FileType.PDF, FileType.valueOfName("pdf"));
    }

    @Test
    void testValueOfName_CaseInsensitive() {
        assertEquals(FileType.DOC, FileType.valueOfName("DOC"));
        assertEquals(FileType.DOCX, FileType.valueOfName("DOCX"));
        assertEquals(FileType.PDF, FileType.valueOfName("PDF"));
    }

    @Test
    void testValueOfName_InvalidType() {
        assertThrows(BaseException.class, () -> FileType.valueOfName("txt"));
        assertThrows(BaseException.class, () -> FileType.valueOfName("jpg"));
        assertThrows(BaseException.class, () -> FileType.valueOfName("unknown"));
    }

    @Test
    void testValueOfName_NullInput() {
        assertThrows(Exception.class, () -> FileType.valueOfName(null));
    }

    @Test
    void testValueOfName_EmptyInput() {
        assertThrows(BaseException.class, () -> FileType.valueOfName(""));
    }

    @Test
    void testGetFileTypeName() {
        assertEquals("doc", FileType.DOC.getFileTypeName());
        assertEquals("docx", FileType.DOCX.getFileTypeName());
        assertEquals("pdf", FileType.PDF.getFileTypeName());
        assertEquals("xls", FileType.XLS.getFileTypeName());
        assertEquals("xlsx", FileType.XLSX.getFileTypeName());
        assertEquals("ppt", FileType.PPT.getFileTypeName());
        assertEquals("pptx", FileType.PPTX.getFileTypeName());
    }
}
