package com.fxiaoke.document.convert.domain.model;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.Base64;

import static org.junit.jupiter.api.Assertions.*;

class QrCodeParamsTest {

    @Test
    void testBuilder() {
        // Given & When
        QrCodeParams params = QrCodeParams.builder()
                .content("Test content")
                .size(200)
                .correctionLevel(CorrectionLevel.M)
                .logoBase64("test-logo")
                .build();

        // Then
        assertEquals("Test content", params.getContent());
        assertEquals(200, params.getSize());
        assertEquals(CorrectionLevel.M, params.getCorrectionLevel());
        assertEquals("test-logo", params.getLogoBase64());
    }

    @Test
    void testSetCorrectionLevel_String() {
        // Given
        QrCodeParams params = new QrCodeParams();

        // When
        params.setCorrectionLevel("H");

        // Then
        assertEquals(CorrectionLevel.H, params.getCorrectionLevel());
    }

    @Test
    void testIsContentValid_ValidContent() {
        // Given
        QrCodeParams params = QrCodeParams.builder()
                .content("Valid content")
                .size(200)
                .correctionLevel(CorrectionLevel.M)
                .build();

        // When & Then
        assertTrue(params.isContentValid());
    }

    @Test
    void testIsContentValid_NullContent() {
        // Given
        QrCodeParams params = QrCodeParams.builder()
                .content(null)
                .size(200)
                .correctionLevel(CorrectionLevel.M)
                .build();

        // When & Then
        assertFalse(params.isContentValid());
    }

    @Test
    void testIsContentValid_EmptyContent() {
        // Given
        QrCodeParams params = QrCodeParams.builder()
                .content("")
                .size(200)
                .correctionLevel(CorrectionLevel.M)
                .build();

        // When & Then
        assertFalse(params.isContentValid());
    }

    @Test
    void testIsContentValid_TooLongContent() {
        // Given
        StringBuilder longContent = new StringBuilder();
        for (int i = 0; i < 10000; i++) {
            longContent.append("a");
        }
        
        QrCodeParams params = QrCodeParams.builder()
                .content(longContent.toString())
                .size(100) // Small size to make content invalid
                .correctionLevel(CorrectionLevel.L)
                .build();

        // When & Then
        assertFalse(params.isContentValid());
    }

    @Test
    void testIsLogoValid_NoLogo() {
        // Given
        QrCodeParams params = QrCodeParams.builder()
                .content("Test")
                .size(200)
                .correctionLevel(CorrectionLevel.M)
                .logoBase64(null)
                .build();

        // When & Then
        assertTrue(params.isLogoValid());
    }

    @Test
    void testIsLogoValid_EmptyLogo() {
        // Given
        QrCodeParams params = QrCodeParams.builder()
                .content("Test")
                .size(200)
                .correctionLevel(CorrectionLevel.M)
                .logoBase64("")
                .build();

        // When & Then
        assertTrue(params.isLogoValid());
    }

    @Test
    void testIsLogoValid_ValidSmallLogo() {
        // Given - 1x1 pixel PNG image
        String smallLogoBase64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAADElEQVQI12P4//8/AAX+Av7czFnnAAAAAElFTkSuQmCC";
        
        QrCodeParams params = QrCodeParams.builder()
                .content("Test")
                .size(300)
                .correctionLevel(CorrectionLevel.H)
                .logoBase64(smallLogoBase64)
                .build();

        // When & Then
        assertTrue(params.isLogoValid());
    }

    @Test
    void testIsLogoValid_InvalidBase64() {
        // Given
        QrCodeParams params = QrCodeParams.builder()
                .content("Test")
                .size(200)
                .correctionLevel(CorrectionLevel.M)
                .logoBase64("invalid-base64")
                .build();

        // When & Then
        assertFalse(params.isLogoValid());
    }

    @Test
    void testGetVersion_SmallSize() {
        // Given
        QrCodeParams params = QrCodeParams.builder()
                .content("Test")
                .size(84) // 21 modules
                .correctionLevel(CorrectionLevel.M)
                .build();

        // When & Then
        assertEquals(1, params.getVersion());
    }

    @Test
    void testGetVersion_LargeSize() {
        // Given
        QrCodeParams params = QrCodeParams.builder()
                .content("Test")
                .size(400) // 100 modules
                .correctionLevel(CorrectionLevel.M)
                .build();

        // When & Then
        assertTrue(params.getVersion() > 1);
        assertTrue(params.getVersion() <= 40);
    }

    @Test
    void testGetVersion_MaxSize() {
        // Given
        QrCodeParams params = QrCodeParams.builder()
                .content("Test")
                .size(1000) // Very large size
                .correctionLevel(CorrectionLevel.M)
                .build();

        // When & Then
        assertEquals(40, params.getVersion()); // Should cap at 40
    }

    @Test
    void testGetModules() {
        // Given
        QrCodeParams params = QrCodeParams.builder()
                .content("Test")
                .size(200)
                .correctionLevel(CorrectionLevel.M)
                .build();

        // When & Then
        assertEquals(50, params.getModules()); // 200 / 4 = 50
    }

    @Test
    void testGetRecommendedSize() {
        // Given
        QrCodeParams params = QrCodeParams.builder()
                .content("Test")
                .size(200)
                .correctionLevel(CorrectionLevel.M)
                .build();

        // When & Then
        assertEquals(200, params.getRecommendedSize()); // 50 * 4 = 200
    }

    @ParameterizedTest
    @ValueSource(ints = {0, 1, 2, 3, 4})
    void testGetMaxLengthByDataType(int dataType) {
        // Given
        QrCodeParams params = QrCodeParams.builder()
                .content("Test")
                .size(200)
                .correctionLevel(CorrectionLevel.M)
                .build();

        // When
        int maxLength = params.getMaxLengthByDataType(dataType);

        // Then
        assertTrue(maxLength > 0);
    }

    @Test
    void testGetMaxLengthByDataType_InvalidType() {
        // Given
        QrCodeParams params = QrCodeParams.builder()
                .content("Test")
                .size(200)
                .correctionLevel(CorrectionLevel.M)
                .build();

        // When
        int maxLength = params.getMaxLengthByDataType(99); // Invalid type

        // Then
        assertTrue(maxLength > 0); // Should return default
    }

    @ParameterizedTest
    @EnumSource(CorrectionLevel.class)
    void testWithDifferentCorrectionLevels(CorrectionLevel level) {
        // Given
        QrCodeParams params = QrCodeParams.builder()
                .content("Test content")
                .size(200)
                .correctionLevel(level)
                .build();

        // When & Then
        assertTrue(params.isContentValid());
        assertEquals(level, params.getCorrectionLevel());
        assertTrue(params.getVersion() >= 1);
        assertTrue(params.getVersion() <= 40);
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        QrCodeParams params1 = QrCodeParams.builder()
                .content("Test")
                .size(200)
                .correctionLevel(CorrectionLevel.M)
                .logoBase64("logo")
                .build();

        QrCodeParams params2 = QrCodeParams.builder()
                .content("Test")
                .size(200)
                .correctionLevel(CorrectionLevel.M)
                .logoBase64("logo")
                .build();

        QrCodeParams params3 = QrCodeParams.builder()
                .content("Different")
                .size(200)
                .correctionLevel(CorrectionLevel.M)
                .logoBase64("logo")
                .build();

        // When & Then
        assertEquals(params1, params2);
        assertEquals(params1.hashCode(), params2.hashCode());
        assertNotEquals(params1, params3);
        assertNotEquals(params1.hashCode(), params3.hashCode());
    }

    @Test
    void testToString() {
        // Given
        QrCodeParams params = QrCodeParams.builder()
                .content("Test")
                .size(200)
                .correctionLevel(CorrectionLevel.M)
                .logoBase64("logo")
                .build();

        // When
        String toString = params.toString();

        // Then
        assertNotNull(toString);
        assertTrue(toString.contains("Test"));
        assertTrue(toString.contains("200"));
        assertTrue(toString.contains("M"));
        assertTrue(toString.contains("logo"));
    }

    @Test
    void testNoArgsConstructor() {
        // When
        QrCodeParams params = new QrCodeParams();

        // Then
        assertNotNull(params);
        assertNull(params.getContent());
        assertNull(params.getSize());
        assertNull(params.getCorrectionLevel());
        assertNull(params.getLogoBase64());
    }

    @Test
    void testAllArgsConstructor() {
        // When
        QrCodeParams params = new QrCodeParams("Test", 200, CorrectionLevel.M, "logo");

        // Then
        assertEquals("Test", params.getContent());
        assertEquals(200, params.getSize());
        assertEquals(CorrectionLevel.M, params.getCorrectionLevel());
        assertEquals("logo", params.getLogoBase64());
    }
}
