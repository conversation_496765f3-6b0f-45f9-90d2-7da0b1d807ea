package com.fxiaoke.document.convert.utils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

import static org.junit.jupiter.api.Assertions.*;

class ZipUtilTest {

    @TempDir
    Path tempDir;

    private Path sourceDir;
    private Path zipFile;

    @BeforeEach
    void setUp() throws IOException {
        sourceDir = tempDir.resolve("source");
        zipFile = tempDir.resolve("test.zip");
        Files.createDirectory(sourceDir);
    }

    @Test
    void testZip_SingleFile() throws IOException {
        // Given
        Path testFile = sourceDir.resolve("test.txt");
        Files.write(testFile, "Hello World".getBytes());

        // When
        ZipUtil.zip(sourceDir.toString(), zipFile.toString());

        // Then
        assertTrue(Files.exists(zipFile));
        assertTrue(Files.size(zipFile) > 0);

        // Verify zip contents
        try (ZipFile zip = new ZipFile(zipFile.toFile())) {
            assertEquals(1, zip.size());
            ZipEntry entry = zip.getEntry("test.txt");
            assertNotNull(entry);
            assertEquals("test.txt", entry.getName());
        }
    }

    @Test
    void testZip_MultipleFiles() throws IOException {
        // Given
        Path file1 = sourceDir.resolve("file1.txt");
        Path file2 = sourceDir.resolve("file2.txt");
        Files.write(file1, "Content 1".getBytes());
        Files.write(file2, "Content 2".getBytes());

        // When
        ZipUtil.zip(sourceDir.toString(), zipFile.toString());

        // Then
        assertTrue(Files.exists(zipFile));

        // Verify zip contents
        try (ZipFile zip = new ZipFile(zipFile.toFile())) {
            assertEquals(2, zip.size());
            assertNotNull(zip.getEntry("file1.txt"));
            assertNotNull(zip.getEntry("file2.txt"));
        }
    }

    @Test
    void testZip_NestedDirectories() throws IOException {
        // Given
        Path subDir = sourceDir.resolve("subdir");
        Files.createDirectory(subDir);
        Path file1 = sourceDir.resolve("root.txt");
        Path file2 = subDir.resolve("nested.txt");
        Files.write(file1, "Root content".getBytes());
        Files.write(file2, "Nested content".getBytes());

        // When
        ZipUtil.zip(sourceDir.toString(), zipFile.toString());

        // Then
        assertTrue(Files.exists(zipFile));

        // Verify zip contents
        try (ZipFile zip = new ZipFile(zipFile.toFile())) {
            assertEquals(2, zip.size());
            assertNotNull(zip.getEntry("root.txt"));
            assertNotNull(zip.getEntry("subdir/nested.txt"));
        }
    }

    @Test
    void testZip_EmptyDirectory() throws IOException {
        // Given - empty source directory

        // When
        ZipUtil.zip(sourceDir.toString(), zipFile.toString());

        // Then
        assertTrue(Files.exists(zipFile));

        // Verify zip is empty
        try (ZipFile zip = new ZipFile(zipFile.toFile())) {
            assertEquals(0, zip.size());
        }
    }

    @Test
    void testZip_DirectoryWithSubdirectories() throws IOException {
        // Given
        Path subDir1 = sourceDir.resolve("dir1");
        Path subDir2 = sourceDir.resolve("dir2");
        Path nestedDir = subDir1.resolve("nested");
        Files.createDirectories(nestedDir);
        Files.createDirectory(subDir2);

        Path file1 = sourceDir.resolve("root.txt");
        Path file2 = subDir1.resolve("file1.txt");
        Path file3 = nestedDir.resolve("deep.txt");
        Path file4 = subDir2.resolve("file2.txt");

        Files.write(file1, "Root".getBytes());
        Files.write(file2, "Dir1".getBytes());
        Files.write(file3, "Deep".getBytes());
        Files.write(file4, "Dir2".getBytes());

        // When
        ZipUtil.zip(sourceDir.toString(), zipFile.toString());

        // Then
        assertTrue(Files.exists(zipFile));

        // Verify zip contents
        try (ZipFile zip = new ZipFile(zipFile.toFile())) {
            assertEquals(4, zip.size());
            assertNotNull(zip.getEntry("root.txt"));
            assertNotNull(zip.getEntry("dir1/file1.txt"));
            assertNotNull(zip.getEntry("dir1/nested/deep.txt"));
            assertNotNull(zip.getEntry("dir2/file2.txt"));
        }
    }

    @Test
    void testZip_LargeFile() throws IOException {
        // Given
        Path largeFile = sourceDir.resolve("large.txt");
        byte[] content = new byte[10000]; // 10KB
        for (int i = 0; i < content.length; i++) {
            content[i] = (byte) (i % 256);
        }
        Files.write(largeFile, content);

        // When
        ZipUtil.zip(sourceDir.toString(), zipFile.toString());

        // Then
        assertTrue(Files.exists(zipFile));
        assertTrue(Files.size(zipFile) > 0);
        assertTrue(Files.size(zipFile) < Files.size(largeFile)); // Should be compressed

        // Verify zip contents
        try (ZipFile zip = new ZipFile(zipFile.toFile())) {
            assertEquals(1, zip.size());
            ZipEntry entry = zip.getEntry("large.txt");
            assertNotNull(entry);
            assertEquals(content.length, entry.getSize());
        }
    }

    @Test
    void testZip_SpecialCharactersInFilename() throws IOException {
        // Given
        Path specialFile = sourceDir.resolve("file with spaces & symbols.txt");
        Files.write(specialFile, "Special content".getBytes());

        // When
        ZipUtil.zip(sourceDir.toString(), zipFile.toString());

        // Then
        assertTrue(Files.exists(zipFile));

        // Verify zip contents
        try (ZipFile zip = new ZipFile(zipFile.toFile())) {
            assertEquals(1, zip.size());
            assertNotNull(zip.getEntry("file with spaces & symbols.txt"));
        }
    }

    @Test
    void testZip_NonExistentSourceDirectory() {
        // Given
        String nonExistentDir = tempDir.resolve("nonexistent").toString();

        // When & Then
        assertThrows(IOException.class, () -> 
            ZipUtil.zip(nonExistentDir, zipFile.toString()));
    }

    @Test
    void testZip_InvalidZipPath() throws IOException {
        // Given
        Path testFile = sourceDir.resolve("test.txt");
        Files.write(testFile, "content".getBytes());
        String invalidZipPath = "/invalid/path/test.zip";

        // When & Then
        assertThrows(IOException.class, () -> 
            ZipUtil.zip(sourceDir.toString(), invalidZipPath));
    }

    @Test
    void testZip_EmptyFiles() throws IOException {
        // Given
        Path emptyFile1 = sourceDir.resolve("empty1.txt");
        Path emptyFile2 = sourceDir.resolve("empty2.txt");
        Files.createFile(emptyFile1);
        Files.createFile(emptyFile2);

        // When
        ZipUtil.zip(sourceDir.toString(), zipFile.toString());

        // Then
        assertTrue(Files.exists(zipFile));

        // Verify zip contents
        try (ZipFile zip = new ZipFile(zipFile.toFile())) {
            assertEquals(2, zip.size());
            ZipEntry entry1 = zip.getEntry("empty1.txt");
            ZipEntry entry2 = zip.getEntry("empty2.txt");
            assertNotNull(entry1);
            assertNotNull(entry2);
            assertEquals(0, entry1.getSize());
            assertEquals(0, entry2.getSize());
        }
    }

    @Test
    void testZip_MixedContent() throws IOException {
        // Given
        Path subDir = sourceDir.resolve("subdir");
        Files.createDirectory(subDir);
        
        Path normalFile = sourceDir.resolve("normal.txt");
        Path emptyFile = sourceDir.resolve("empty.txt");
        Path nestedFile = subDir.resolve("nested.txt");
        
        Files.write(normalFile, "Normal content".getBytes());
        Files.createFile(emptyFile);
        Files.write(nestedFile, "Nested content".getBytes());

        // When
        ZipUtil.zip(sourceDir.toString(), zipFile.toString());

        // Then
        assertTrue(Files.exists(zipFile));

        // Verify zip contents
        try (ZipFile zip = new ZipFile(zipFile.toFile())) {
            assertEquals(3, zip.size());
            assertNotNull(zip.getEntry("normal.txt"));
            assertNotNull(zip.getEntry("empty.txt"));
            assertNotNull(zip.getEntry("subdir/nested.txt"));
        }
    }

    @Test
    void testZip_DeepNesting() throws IOException {
        // Given
        Path current = sourceDir;
        for (int i = 0; i < 5; i++) {
            current = current.resolve("level" + i);
            Files.createDirectory(current);
        }
        Path deepFile = current.resolve("deep.txt");
        Files.write(deepFile, "Deep content".getBytes());

        // When
        ZipUtil.zip(sourceDir.toString(), zipFile.toString());

        // Then
        assertTrue(Files.exists(zipFile));

        // Verify zip contents
        try (ZipFile zip = new ZipFile(zipFile.toFile())) {
            assertEquals(1, zip.size());
            assertNotNull(zip.getEntry("level0/level1/level2/level3/level4/deep.txt"));
        }
    }

    @Test
    void testZip_OverwriteExistingZip() throws IOException {
        // Given
        Path testFile = sourceDir.resolve("test.txt");
        Files.write(testFile, "content".getBytes());
        
        // Create initial zip
        ZipUtil.zip(sourceDir.toString(), zipFile.toString());
        long originalSize = Files.size(zipFile);
        
        // Add more content
        Path newFile = sourceDir.resolve("new.txt");
        Files.write(newFile, "new content".getBytes());

        // When - zip again (should overwrite)
        ZipUtil.zip(sourceDir.toString(), zipFile.toString());

        // Then
        assertTrue(Files.exists(zipFile));
        long newSize = Files.size(zipFile);
        assertTrue(newSize > originalSize); // Should be larger due to additional file

        // Verify new zip contents
        try (ZipFile zip = new ZipFile(zipFile.toFile())) {
            assertEquals(2, zip.size());
            assertNotNull(zip.getEntry("test.txt"));
            assertNotNull(zip.getEntry("new.txt"));
        }
    }
}
