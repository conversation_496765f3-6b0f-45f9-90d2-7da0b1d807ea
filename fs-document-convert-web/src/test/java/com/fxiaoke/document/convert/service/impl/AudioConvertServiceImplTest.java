package com.fxiaoke.document.convert.service.impl;

import com.fxiaoke.document.convert.config.CmsPropertiesConfig;
import com.fxiaoke.document.convert.domain.constants.AudioConvertTypeEnum;
import com.fxiaoke.document.convert.domain.model.AudioConvertArg;
import com.fxiaoke.document.convert.domain.model.AudioConvertInstructionParams;
import com.fxiaoke.document.convert.service.AudioConverterOption;
import com.fxiaoke.document.convert.service.FileService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AudioConvertServiceImplTest {

    @Mock
    private CmsPropertiesConfig cmsPropertiesConfig;
    
    @Mock
    private AudioConverterOption audioConverterService;
    
    @Mock
    private FileService fileService;

    private AudioConvertServiceImpl audioConvertService;

    private static final String TEST_EA = "testEa";
    private static final int TEST_EMPLOYEE_ID = 12345;
    private static final String TEST_SECURITY_GROUP = "testGroup";
    private static final String TEST_PATH = "audio/test";
    private static final String TEST_CACHE_PATH = "/tmp/cache";

    @BeforeEach
    void setUp() {
        audioConvertService = new AudioConvertServiceImpl(cmsPropertiesConfig, audioConverterService, fileService);
        when(cmsPropertiesConfig.getLocalCachePath()).thenReturn(TEST_CACHE_PATH);
    }

    @Test
    void testConvert_AMR_TO_MP3_Success() {
        // Given
        AudioConvertArg arg = createAudioConvertArg("amr", "mp3");
        String expectedInputPath = TEST_CACHE_PATH + "/" + TEST_PATH + ".amr";
        String expectedOutputPath = TEST_CACHE_PATH + "/" + TEST_PATH + ".mp3";

        // When
        String result = audioConvertService.convert(arg);

        // Then
        assertEquals(expectedOutputPath, result);
        
        verify(fileService).downloadFileToLocalFile(TEST_EA, TEST_EMPLOYEE_ID, TEST_SECURITY_GROUP, 
            TEST_PATH, Path.of(expectedInputPath));
        
        ArgumentCaptor<AudioConvertInstructionParams> paramsCaptor = 
            ArgumentCaptor.forClass(AudioConvertInstructionParams.class);
        verify(audioConverterService).toMp3(paramsCaptor.capture());
        
        AudioConvertInstructionParams capturedParams = paramsCaptor.getValue();
        assertEquals(expectedInputPath, capturedParams.getInputPath());
        assertEquals(expectedOutputPath, capturedParams.getOutputPath());
        assertEquals("amr", capturedParams.getSourceType());
        assertEquals("mp3", capturedParams.getTargetType());
    }

    @Test
    void testConvert_OPUS_TO_MP3_Success() {
        // Given
        AudioConvertArg arg = createAudioConvertArg("opus", "mp3");
        String expectedInputPath = TEST_CACHE_PATH + "/" + TEST_PATH + ".opus";
        String expectedOutputPath = TEST_CACHE_PATH + "/" + TEST_PATH + ".mp3";

        // When
        String result = audioConvertService.convert(arg);

        // Then
        assertEquals(expectedOutputPath, result);
        verify(audioConverterService).toMp3(any(AudioConvertInstructionParams.class));
    }

    @Test
    void testConvert_WAV_TO_MP3_Success() {
        // Given
        AudioConvertArg arg = createAudioConvertArg("wav", "mp3");
        String expectedInputPath = TEST_CACHE_PATH + "/" + TEST_PATH + ".wav";
        String expectedOutputPath = TEST_CACHE_PATH + "/" + TEST_PATH + ".mp3";

        // When
        String result = audioConvertService.convert(arg);

        // Then
        assertEquals(expectedOutputPath, result);
        verify(audioConverterService).toMp3(any(AudioConvertInstructionParams.class));
    }

    @Test
    void testConvert_AMR_TO_WAV_Success() {
        // Given
        AudioConvertArg arg = createAudioConvertArg("amr", "wav");
        String expectedInputPath = TEST_CACHE_PATH + "/" + TEST_PATH + ".amr";
        String expectedOutputPath = TEST_CACHE_PATH + "/" + TEST_PATH + ".wav";

        // When
        String result = audioConvertService.convert(arg);

        // Then
        assertEquals(expectedOutputPath, result);
        verify(audioConverterService).toWav(any(AudioConvertInstructionParams.class));
    }

    @Test
    void testConvert_OPUS_TO_WAV_Success() {
        // Given
        AudioConvertArg arg = createAudioConvertArg("opus", "wav");
        String expectedInputPath = TEST_CACHE_PATH + "/" + TEST_PATH + ".opus";
        String expectedOutputPath = TEST_CACHE_PATH + "/" + TEST_PATH + ".wav";

        // When
        String result = audioConvertService.convert(arg);

        // Then
        assertEquals(expectedOutputPath, result);
        verify(audioConverterService).toWav(any(AudioConvertInstructionParams.class));
    }

    @Test
    void testConvert_SAME_Format_ReturnsInputPath() {
        // Given
        AudioConvertArg arg = createAudioConvertArg("mp3", "mp3");
        String expectedInputPath = TEST_CACHE_PATH + "/" + TEST_PATH + ".mp3";

        // When
        String result = audioConvertService.convert(arg);

        // Then
        assertEquals(expectedInputPath, result);
        verify(fileService).downloadFileToLocalFile(TEST_EA, TEST_EMPLOYEE_ID, TEST_SECURITY_GROUP, 
            TEST_PATH, Path.of(expectedInputPath));
        verify(audioConverterService, never()).toMp3(any());
        verify(audioConverterService, never()).toWav(any());
    }

    @ParameterizedTest
    @CsvSource({
        "mp3, mp3",
        "wav, wav", 
        "amr, amr",
        "opus, opus"
    })
    void testConvert_SameFormats_ReturnsInputPath(String sourceType, String targetType) {
        // Given
        AudioConvertArg arg = createAudioConvertArg(sourceType, targetType);
        String expectedInputPath = TEST_CACHE_PATH + "/" + TEST_PATH + "." + sourceType;

        // When
        String result = audioConvertService.convert(arg);

        // Then
        assertEquals(expectedInputPath, result);
        verify(audioConverterService, never()).toMp3(any());
        verify(audioConverterService, never()).toWav(any());
    }

    @Test
    void testConvert_UnsupportedConversion_ThrowsException() {
        // Given
        AudioConvertArg arg = createAudioConvertArg("mp3", "amr"); // Unsupported conversion

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, 
            () -> audioConvertService.convert(arg));
        
        assertEquals("不支持的音频转换类型", exception.getMessage());
        verify(audioConverterService, never()).toMp3(any());
        verify(audioConverterService, never()).toWav(any());
    }

    @Test
    void testConvert_FileServiceThrowsException_PropagatesException() {
        // Given
        AudioConvertArg arg = createAudioConvertArg("amr", "mp3");
        String expectedInputPath = TEST_CACHE_PATH + "/" + TEST_PATH + ".amr";
        
        doThrow(new RuntimeException("Download failed"))
            .when(fileService).downloadFileToLocalFile(TEST_EA, TEST_EMPLOYEE_ID, TEST_SECURITY_GROUP, 
                TEST_PATH, Path.of(expectedInputPath));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> audioConvertService.convert(arg));
        
        assertEquals("Download failed", exception.getMessage());
        verify(fileService).downloadFileToLocalFile(TEST_EA, TEST_EMPLOYEE_ID, TEST_SECURITY_GROUP, 
            TEST_PATH, Path.of(expectedInputPath));
        verify(audioConverterService, never()).toMp3(any());
    }

    @Test
    void testConvert_AudioConverterThrowsException_PropagatesException() {
        // Given
        AudioConvertArg arg = createAudioConvertArg("amr", "mp3");
        
        doThrow(new RuntimeException("Conversion failed"))
            .when(audioConverterService).toMp3(any(AudioConvertInstructionParams.class));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> audioConvertService.convert(arg));
        
        assertEquals("Conversion failed", exception.getMessage());
        verify(audioConverterService).toMp3(any(AudioConvertInstructionParams.class));
    }

    @Test
    void testConvert_NullArg_ThrowsException() {
        // When & Then
        assertThrows(NullPointerException.class, 
            () -> audioConvertService.convert(null));
    }

    @Test
    void testConvert_VerifyFilePathGeneration() {
        // Given
        AudioConvertArg arg = createAudioConvertArg("amr", "mp3");
        String expectedInputPath = TEST_CACHE_PATH + "/" + TEST_PATH + ".amr";
        String expectedOutputPath = TEST_CACHE_PATH + "/" + TEST_PATH + ".mp3";

        // When
        audioConvertService.convert(arg);

        // Then
        verify(fileService).downloadFileToLocalFile(TEST_EA, TEST_EMPLOYEE_ID, TEST_SECURITY_GROUP, 
            TEST_PATH, Path.of(expectedInputPath));
        
        ArgumentCaptor<AudioConvertInstructionParams> paramsCaptor = 
            ArgumentCaptor.forClass(AudioConvertInstructionParams.class);
        verify(audioConverterService).toMp3(paramsCaptor.capture());
        
        AudioConvertInstructionParams params = paramsCaptor.getValue();
        assertEquals(expectedInputPath, params.getInputPath());
        assertEquals(expectedOutputPath, params.getOutputPath());
    }

    @Test
    void testConvert_DifferentCachePath_UsesCorrectPath() {
        // Given
        String customCachePath = "/custom/cache";
        when(cmsPropertiesConfig.getLocalCachePath()).thenReturn(customCachePath);
        
        AudioConvertArg arg = createAudioConvertArg("amr", "mp3");
        String expectedInputPath = customCachePath + "/" + TEST_PATH + ".amr";
        String expectedOutputPath = customCachePath + "/" + TEST_PATH + ".mp3";

        // When
        String result = audioConvertService.convert(arg);

        // Then
        assertEquals(expectedOutputPath, result);
        verify(fileService).downloadFileToLocalFile(TEST_EA, TEST_EMPLOYEE_ID, TEST_SECURITY_GROUP, 
            TEST_PATH, Path.of(expectedInputPath));
    }

    @Test
    void testConstructor_AllDependenciesInjected() {
        // When
        AudioConvertServiceImpl service = new AudioConvertServiceImpl(cmsPropertiesConfig, audioConverterService, fileService);

        // Then
        assertNotNull(service);
        // Verify that all dependencies are properly injected by calling a method
        AudioConvertArg arg = createAudioConvertArg("mp3", "mp3");
        String result = service.convert(arg);
        assertNotNull(result);
    }

    @ParameterizedTest
    @CsvSource({
        "amr, mp3, toMp3",
        "opus, mp3, toMp3",
        "wav, mp3, toMp3",
        "amr, wav, toWav",
        "opus, wav, toWav"
    })
    void testConvert_AllSupportedConversions_CallsCorrectMethod(String sourceType, String targetType, String expectedMethod) {
        // Given
        AudioConvertArg arg = createAudioConvertArg(sourceType, targetType);

        // When
        audioConvertService.convert(arg);

        // Then
        if ("toMp3".equals(expectedMethod)) {
            verify(audioConverterService).toMp3(any(AudioConvertInstructionParams.class));
            verify(audioConverterService, never()).toWav(any());
        } else if ("toWav".equals(expectedMethod)) {
            verify(audioConverterService).toWav(any(AudioConvertInstructionParams.class));
            verify(audioConverterService, never()).toMp3(any());
        }
    }

    private AudioConvertArg createAudioConvertArg(String sourceType, String targetType) {
        AudioConvertArg arg = new AudioConvertArg();
        arg.setEa(TEST_EA);
        arg.setEmployeeId(TEST_EMPLOYEE_ID);
        arg.setSecurityGroup(TEST_SECURITY_GROUP);
        arg.setPath(TEST_PATH);
        arg.setSourceType(sourceType);
        arg.setTargetType(targetType);
        return arg;
    }
}
