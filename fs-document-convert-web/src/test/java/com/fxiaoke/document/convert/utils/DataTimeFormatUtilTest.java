package com.fxiaoke.document.convert.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

import static org.junit.jupiter.api.Assertions.*;

class DataTimeFormatUtilTest {

    @Test
    void testGetCurrentDate_StandardFormat() {
        // Given
        String format = "yyyyMMdd";

        // When
        String result = DataTimeFormatUtil.getCurrentDate(format);

        // Then
        assertNotNull(result);
        assertEquals(8, result.length());
        assertTrue(result.matches("\\d{8}"));
        
        // Verify it matches today's date
        String expected = LocalDate.now().format(DateTimeFormatter.ofPattern(format));
        assertEquals(expected, result);
    }

    @Test
    void testGetCurrentDate_DifferentFormat() {
        // Given
        String format = "yyyy-MM-dd";

        // When
        String result = DataTimeFormatUtil.getCurrentDate(format);

        // Then
        assertNotNull(result);
        assertEquals(10, result.length());
        assertTrue(result.matches("\\d{4}-\\d{2}-\\d{2}"));
        
        // Verify it matches today's date
        String expected = LocalDate.now().format(DateTimeFormatter.ofPattern(format));
        assertEquals(expected, result);
    }

    @ParameterizedTest
    @ValueSource(strings = {"yyyyMMdd", "yyyy-MM-dd", "dd/MM/yyyy", "MM/dd/yyyy", "yyyy.MM.dd", "dd", "MM", "yyyy"})
    void testGetCurrentDate_VariousFormats(String format) {
        // When
        String result = DataTimeFormatUtil.getCurrentDate(format);

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        
        // Verify it matches the expected format
        String expected = LocalDate.now().format(DateTimeFormatter.ofPattern(format));
        assertEquals(expected, result);
    }

    @Test
    void testGetCurrentTime_StandardFormat() {
        // Given
        String format = "HH";

        // When
        String result = DataTimeFormatUtil.getCurrentTime(format);

        // Then
        assertNotNull(result);
        assertEquals(2, result.length());
        assertTrue(result.matches("\\d{2}"));
        
        // Verify it's a valid hour (00-23)
        int hour = Integer.parseInt(result);
        assertTrue(hour >= 0 && hour <= 23);
    }

    @Test
    void testGetCurrentTime_DifferentFormat() {
        // Given
        String format = "HH:mm:ss";

        // When
        String result = DataTimeFormatUtil.getCurrentTime(format);

        // Then
        assertNotNull(result);
        assertEquals(8, result.length());
        assertTrue(result.matches("\\d{2}:\\d{2}:\\d{2}"));
        
        // Verify it matches current time format
        String expected = LocalTime.now().format(DateTimeFormatter.ofPattern(format));
        // Note: We can't do exact equality due to timing differences, but format should match
        assertTrue(result.matches("\\d{2}:\\d{2}:\\d{2}"));
    }

    @ParameterizedTest
    @ValueSource(strings = {"HH", "mm", "ss", "HH:mm", "HH:mm:ss", "HH.mm.ss", "HHmmss"})
    void testGetCurrentTime_VariousFormats(String format) {
        // When
        String result = DataTimeFormatUtil.getCurrentTime(format);

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        
        // Verify it matches the expected format pattern
        String expected = LocalTime.now().format(DateTimeFormatter.ofPattern(format));
        // The length should match the expected format
        assertEquals(expected.length(), result.length());
    }

    @Test
    void testGetMinusDays_ZeroDays() {
        // Given
        int days = 0;

        // When
        String result = DataTimeFormatUtil.getMinusDays(days);

        // Then
        assertNotNull(result);
        assertEquals(8, result.length());
        assertTrue(result.matches("\\d{8}"));
        
        // Should be today's date
        String expected = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        assertEquals(expected, result);
    }

    @Test
    void testGetMinusDays_OneDayAgo() {
        // Given
        int days = 1;

        // When
        String result = DataTimeFormatUtil.getMinusDays(days);

        // Then
        assertNotNull(result);
        assertEquals(8, result.length());
        assertTrue(result.matches("\\d{8}"));
        
        // Should be yesterday's date
        String expected = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        assertEquals(expected, result);
    }

    @Test
    void testGetMinusDays_SevenDaysAgo() {
        // Given
        int days = 7;

        // When
        String result = DataTimeFormatUtil.getMinusDays(days);

        // Then
        assertNotNull(result);
        assertEquals(8, result.length());
        assertTrue(result.matches("\\d{8}"));
        
        // Should be 7 days ago
        String expected = LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        assertEquals(expected, result);
    }

    @Test
    void testGetMinusDays_ThirtyDaysAgo() {
        // Given
        int days = 30;

        // When
        String result = DataTimeFormatUtil.getMinusDays(days);

        // Then
        assertNotNull(result);
        assertEquals(8, result.length());
        assertTrue(result.matches("\\d{8}"));
        
        // Should be 30 days ago
        String expected = LocalDate.now().minusDays(30).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        assertEquals(expected, result);
    }

    @Test
    void testGetMinusDays_LargeDays() {
        // Given
        int days = 365;

        // When
        String result = DataTimeFormatUtil.getMinusDays(days);

        // Then
        assertNotNull(result);
        assertEquals(8, result.length());
        assertTrue(result.matches("\\d{8}"));
        
        // Should be 365 days ago
        String expected = LocalDate.now().minusDays(365).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        assertEquals(expected, result);
    }

    @Test
    void testGetMinusDays_NegativeDays() {
        // Given
        int days = -1; // This should give us tomorrow

        // When
        String result = DataTimeFormatUtil.getMinusDays(days);

        // Then
        assertNotNull(result);
        assertEquals(8, result.length());
        assertTrue(result.matches("\\d{8}"));
        
        // Should be tomorrow's date (minus negative days = plus days)
        String expected = LocalDate.now().minusDays(-1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        assertEquals(expected, result);
    }

    @Test
    void testGetCurrentDate_ConsistentResults() {
        // Given
        String format = "yyyyMMdd";

        // When
        String result1 = DataTimeFormatUtil.getCurrentDate(format);
        String result2 = DataTimeFormatUtil.getCurrentDate(format);

        // Then - Should be the same if called quickly
        assertEquals(result1, result2);
    }

    @Test
    void testGetCurrentTime_ValidHourFormat() {
        // Given
        String format = "HH";

        // When
        String result = DataTimeFormatUtil.getCurrentTime(format);

        // Then
        assertNotNull(result);
        assertEquals(2, result.length());
        int hour = Integer.parseInt(result);
        assertTrue(hour >= 0 && hour <= 23);
    }

    @Test
    void testGetCurrentTime_ValidMinuteFormat() {
        // Given
        String format = "mm";

        // When
        String result = DataTimeFormatUtil.getCurrentTime(format);

        // Then
        assertNotNull(result);
        assertEquals(2, result.length());
        int minute = Integer.parseInt(result);
        assertTrue(minute >= 0 && minute <= 59);
    }

    @Test
    void testGetCurrentTime_ValidSecondFormat() {
        // Given
        String format = "ss";

        // When
        String result = DataTimeFormatUtil.getCurrentTime(format);

        // Then
        assertNotNull(result);
        assertEquals(2, result.length());
        int second = Integer.parseInt(result);
        assertTrue(second >= 0 && second <= 59);
    }

    @Test
    void testGetMinusDays_CrossMonthBoundary() {
        // Given
        int days = 35; // Should cross month boundary for most dates

        // When
        String result = DataTimeFormatUtil.getMinusDays(days);

        // Then
        assertNotNull(result);
        assertEquals(8, result.length());
        assertTrue(result.matches("\\d{8}"));
        
        // Verify the calculation is correct
        String expected = LocalDate.now().minusDays(35).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        assertEquals(expected, result);
    }

    @Test
    void testGetMinusDays_CrossYearBoundary() {
        // Given
        int days = 400; // Should cross year boundary

        // When
        String result = DataTimeFormatUtil.getMinusDays(days);

        // Then
        assertNotNull(result);
        assertEquals(8, result.length());
        assertTrue(result.matches("\\d{8}"));
        
        // Verify the calculation is correct
        String expected = LocalDate.now().minusDays(400).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        assertEquals(expected, result);
    }
}
