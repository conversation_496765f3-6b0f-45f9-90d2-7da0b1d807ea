package com.fxiaoke.document.convert.utils;

import org.junit.jupiter.api.Test;

import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

class SampleUUIDTest {

    @Test
    void testGetUUID_ReturnsValidString() {
        // When
        String uuid = SampleUUID.getUUID();

        // Then
        assertNotNull(uuid);
        assertEquals(8, uuid.length());
        assertTrue(uuid.matches("[a-z0-9]+"));
    }

    @Test
    void testGetUUID_ReturnsLowercase() {
        // When
        String uuid = SampleUUID.getUUID();

        // Then
        assertEquals(uuid.toLowerCase(), uuid);
        assertFalse(uuid.matches(".*[A-Z].*"));
    }

    @Test
    void testGetUUID_ContainsValidCharacters() {
        // When
        String uuid = SampleUUID.getUUID();

        // Then
        assertTrue(uuid.matches("[a-z0-9]+"));
        // Should only contain lowercase letters and digits
        for (char c : uuid.toCharArray()) {
            assertTrue((c >= 'a' && c <= 'z') || (c >= '0' && c <= '9'));
        }
    }

    @Test
    void testGetUUID_GeneratesUniqueValues() {
        // Given
        Set<String> uuids = new HashSet<>();
        int iterations = 1000;

        // When
        for (int i = 0; i < iterations; i++) {
            String uuid = SampleUUID.getUUID();
            uuids.add(uuid);
        }

        // Then
        // Should generate mostly unique values (allowing for very rare collisions)
        assertTrue(uuids.size() > iterations * 0.95); // At least 95% unique
    }

    @Test
    void testGetUUID_ConsistentLength() {
        // When & Then
        for (int i = 0; i < 100; i++) {
            String uuid = SampleUUID.getUUID();
            assertEquals(8, uuid.length());
        }
    }

    @Test
    void testGetUUID_AlgorithmCorrectness() {
        // This test verifies the algorithm works as expected
        // The algorithm takes a 32-character hex UUID, splits it into 8 groups of 4 chars,
        // converts each group to int, then uses modulo 62 to index into the chars array

        // When
        String uuid = SampleUUID.getUUID();

        // Then
        assertNotNull(uuid);
        assertEquals(8, uuid.length());
        
        // Each character should be from the valid character set
        String validChars = "abcdefghijklmnopqrstuvwxyz0123456789";
        for (char c : uuid.toCharArray()) {
            assertTrue(validChars.indexOf(c) >= 0);
        }
    }

    @Test
    void testGetUUID_NoSpecialCharacters() {
        // When
        String uuid = SampleUUID.getUUID();

        // Then
        assertFalse(uuid.contains("-"));
        assertFalse(uuid.contains("_"));
        assertFalse(uuid.contains(" "));
        assertFalse(uuid.contains("."));
        assertFalse(uuid.contains("/"));
        assertFalse(uuid.contains("\\"));
    }

    @Test
    void testGetUUID_MultipleCallsReturnDifferentValues() {
        // When
        String uuid1 = SampleUUID.getUUID();
        String uuid2 = SampleUUID.getUUID();
        String uuid3 = SampleUUID.getUUID();

        // Then
        assertNotEquals(uuid1, uuid2);
        assertNotEquals(uuid2, uuid3);
        assertNotEquals(uuid1, uuid3);
    }

    @Test
    void testGetUUID_PerformanceTest() {
        // This test ensures the method performs reasonably well
        long startTime = System.currentTimeMillis();
        
        // When
        for (int i = 0; i < 10000; i++) {
            SampleUUID.getUUID();
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // Then
        // Should complete 10,000 calls in reasonable time (less than 5 seconds)
        assertTrue(duration < 5000, "UUID generation took too long: " + duration + "ms");
    }

    @Test
    void testGetUUID_CharacterDistribution() {
        // Test that the algorithm produces a reasonable distribution of characters
        int[] charCounts = new int[36]; // 26 letters + 10 digits
        int iterations = 10000;
        
        // When
        for (int i = 0; i < iterations; i++) {
            String uuid = SampleUUID.getUUID();
            for (char c : uuid.toCharArray()) {
                if (c >= 'a' && c <= 'z') {
                    charCounts[c - 'a']++;
                } else if (c >= '0' && c <= '9') {
                    charCounts[26 + (c - '0')]++;
                }
            }
        }
        
        // Then
        // Each character should appear at least once in 10,000 iterations
        // (This is probabilistic, but very likely to pass)
        int totalChars = iterations * 8; // 8 chars per UUID
        int expectedPerChar = totalChars / 36; // Rough expected count per character
        
        // Allow for some variance - each char should appear at least 10% of expected
        for (int count : charCounts) {
            assertTrue(count > expectedPerChar * 0.1, 
                "Character distribution seems skewed, count: " + count + ", expected around: " + expectedPerChar);
        }
    }

    @Test
    void testGetUUID_ThreadSafety() throws InterruptedException {
        // Test that the method is thread-safe
        Set<String> uuids = new HashSet<>();
        int numThreads = 10;
        int uuidsPerThread = 100;
        Thread[] threads = new Thread[numThreads];
        
        // When
        for (int i = 0; i < numThreads; i++) {
            threads[i] = new Thread(() -> {
                for (int j = 0; j < uuidsPerThread; j++) {
                    synchronized (uuids) {
                        uuids.add(SampleUUID.getUUID());
                    }
                }
            });
            threads[i].start();
        }
        
        // Wait for all threads to complete
        for (Thread thread : threads) {
            thread.join();
        }
        
        // Then
        // Should generate mostly unique values even with concurrent access
        int expectedTotal = numThreads * uuidsPerThread;
        assertTrue(uuids.size() > expectedTotal * 0.95, 
            "Thread safety test failed, expected ~" + expectedTotal + " unique UUIDs, got " + uuids.size());
    }

    @Test
    void testGetUUID_EmptyStringNeverReturned() {
        // When & Then
        for (int i = 0; i < 1000; i++) {
            String uuid = SampleUUID.getUUID();
            assertFalse(uuid.isEmpty());
            assertNotEquals("", uuid);
        }
    }

    @Test
    void testGetUUID_NoNullReturned() {
        // When & Then
        for (int i = 0; i < 1000; i++) {
            String uuid = SampleUUID.getUUID();
            assertNotNull(uuid);
        }
    }
}
