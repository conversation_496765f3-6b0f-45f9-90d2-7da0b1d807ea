package com.fxiaoke.document.convert.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

class FileNameUtilTest {

    @Test
    void testCreateTempDir() {
        // Given
        String filePath = "/path/to/document.pdf";

        // When
        String result = FileNameUtil.createTempDir(filePath);

        // Then
        assertEquals("/path/to/document", result);
    }

    @Test
    void testCreateTempFileName_WithExtension() {
        // Given
        String filePath = "/path/to/document.pdf";
        String extension = ".txt";

        // When
        String result = FileNameUtil.createTempFileName(filePath, extension);

        // Then
        assertEquals("/path/to/document.txt", result);
    }

    @Test
    void testCreateTempFileName_WithPageIndex() {
        // Given
        String filePath = "/path/to/document.pdf";
        int pageIndex = 5;
        String extension = "png";

        // When
        String result = FileNameUtil.createTempFileName(filePath, pageIndex, extension);

        // Then
        assertEquals("/path/to/5.png", result);
    }

    @ParameterizedTest
    @ValueSource(strings = {"document.pdf", "test.docx", "file.txt", "image.png"})
    void testGetFileExtension(String filename) {
        // When
        String extension = FileNameUtil.getFileExtension(filename);

        // Then
        assertNotNull(extension);
        assertFalse(extension.contains("."));
    }

    @Test
    void testGetFileExtension_NoExtension() {
        // Given
        String filename = "document";

        // When
        String extension = FileNameUtil.getFileExtension(filename);

        // Then
        assertEquals("", extension);
    }

    @Test
    void testReplaceExtension() {
        // Given
        String sourceFilePath = "/path/to/document.pdf";
        String newExtension = "txt";

        // When
        String result = FileNameUtil.replaceExtension(sourceFilePath, newExtension);

        // Then
        assertEquals("/path/to/document.txt", result);
    }

    @Test
    void testGetFullPath() {
        // Given
        String filePath = "/path/to/document.pdf";

        // When
        String fullPath = FileNameUtil.getFullPath(filePath);

        // Then
        assertEquals("/path/to/", fullPath);
    }

    @Test
    void testGetFullPath_RootFile() {
        // Given
        String filePath = "document.pdf";

        // When
        String fullPath = FileNameUtil.getFullPath(filePath);

        // Then
        assertEquals("", fullPath);
    }

    @Test
    void testGenerateDirAsEa() {
        // Given
        String ea = "testEa";
        String rootDir = "/tmp";

        // When
        String result = FileNameUtil.generateDirAsEa(ea, rootDir);

        // Then
        assertNotNull(result);
        assertTrue(result.startsWith("/tmp/dps/"));
        assertTrue(result.contains(ea));
        assertTrue(result.length() > 20); // Should contain date/time components
    }

    @Test
    void testGenerateSimpFilePath_WithAllComponents() {
        // Given
        String prefix = "/tmp";
        String name = "document";
        String suffix = "pdf";

        // When
        String result = FileNameUtil.generateSimpFilePath(prefix, name, suffix);

        // Then
        assertEquals("/tmp/document.pdf", result);
    }

    @Test
    void testGenerateSimpFilePath_WithoutPrefix() {
        // Given
        String prefix = null;
        String name = "document";
        String suffix = "pdf";

        // When
        String result = FileNameUtil.generateSimpFilePath(prefix, name, suffix);

        // Then
        assertEquals("document.pdf", result);
    }

    @Test
    void testGenerateSimpFilePath_WithoutSuffix() {
        // Given
        String prefix = "/tmp";
        String name = "document";
        String suffix = null;

        // When
        String result = FileNameUtil.generateSimpFilePath(prefix, name, suffix);

        // Then
        assertEquals("/tmp/document", result);
    }

    @Test
    void testGenerateSimpFilePath_EmptyPrefix() {
        // Given
        String prefix = "";
        String name = "document";
        String suffix = "pdf";

        // When
        String result = FileNameUtil.generateSimpFilePath(prefix, name, suffix);

        // Then
        assertEquals("document.pdf", result);
    }

    @Test
    void testGenerateSimpFilePath_EmptySuffix() {
        // Given
        String prefix = "/tmp";
        String name = "document";
        String suffix = "";

        // When
        String result = FileNameUtil.generateSimpFilePath(prefix, name, suffix);

        // Then
        assertEquals("/tmp/document", result);
    }

    @Test
    void testGenerateFilePath_WithPrefix() {
        // Given
        String prefix = "/tmp";
        String name = "document";
        String suffix = "pdf";

        // When
        String result = FileNameUtil.generateFilePath(prefix, name, suffix);

        // Then
        assertNotNull(result);
        assertTrue(result.startsWith("/tmp/"));
        assertTrue(result.contains(name));
        assertTrue(result.endsWith(".pdf"));
        assertTrue(result.contains("_")); // Should contain UUID separator
    }

    @Test
    void testGenerateFilePath_WithoutPrefix() {
        // Given
        String prefix = null;
        String name = "document";
        String suffix = "pdf";

        // When
        String result = FileNameUtil.generateFilePath(prefix, name, suffix);

        // Then
        assertNotNull(result);
        assertTrue(result.contains(name));
        assertTrue(result.endsWith(".pdf"));
        assertTrue(result.contains("_")); // Should contain UUID separator
    }

    @Test
    void testGenerateFilePath_EmptyPrefix() {
        // Given
        String prefix = "";
        String name = "document";
        String suffix = "pdf";

        // When
        String result = FileNameUtil.generateFilePath(prefix, name, suffix);

        // Then
        assertNotNull(result);
        assertTrue(result.contains(name));
        assertTrue(result.endsWith(".pdf"));
        assertTrue(result.contains("_")); // Should contain UUID separator
    }

    @Test
    void testGenerateFilePath_WithoutSuffix() {
        // Given
        String prefix = "/tmp";
        String name = "document";
        String suffix = null;

        // When
        String result = FileNameUtil.generateFilePath(prefix, name, suffix);

        // Then
        assertNotNull(result);
        assertTrue(result.startsWith("/tmp/"));
        assertTrue(result.contains(name));
        assertFalse(result.endsWith("."));
        assertTrue(result.contains("_")); // Should contain UUID separator
    }

    @Test
    void testGetFileName() {
        // Given
        String filePath = "/path/to/document.pdf";

        // When
        String fileName = FileNameUtil.getFileName(filePath);

        // Then
        assertEquals("document.pdf", fileName);
    }

    @Test
    void testGetFileName_NoPath() {
        // Given
        String filePath = "document.pdf";

        // When
        String fileName = FileNameUtil.getFileName(filePath);

        // Then
        assertEquals("document.pdf", fileName);
    }

    @Test
    void testGenerateCleanedMDFilePath() {
        // Given
        Path originalFile = Paths.get("/path/to/document.md");

        // When
        Path cleanedPath = FileNameUtil.generateCleanedMDFilePath(originalFile);

        // Then
        assertEquals("document_cleaned.md", cleanedPath.getFileName().toString());
        assertEquals(originalFile.getParent(), cleanedPath.getParent());
    }

    @Test
    void testGenerateCleanedMDFilePath_NoMdExtension() {
        // Given
        Path originalFile = Paths.get("/path/to/document.txt");

        // When
        Path cleanedPath = FileNameUtil.generateCleanedMDFilePath(originalFile);

        // Then
        assertEquals("document.txt", cleanedPath.getFileName().toString());
        assertEquals(originalFile.getParent(), cleanedPath.getParent());
    }

    @Test
    void testGenerateFilePath_ConsistentFormat() {
        // Given
        String prefix = "/tmp";
        String name = "test";
        String suffix = "txt";

        // When
        String result1 = FileNameUtil.generateFilePath(prefix, name, suffix);
        String result2 = FileNameUtil.generateFilePath(prefix, name, suffix);

        // Then
        // Results should be different due to UUID and timestamp
        assertNotEquals(result1, result2);
        // But both should follow the same format
        assertTrue(result1.startsWith("/tmp/"));
        assertTrue(result2.startsWith("/tmp/"));
        assertTrue(result1.contains(name));
        assertTrue(result2.contains(name));
        assertTrue(result1.endsWith(".txt"));
        assertTrue(result2.endsWith(".txt"));
    }
}
