package com.fxiaoke.document.convert.service;

import com.fxiaoke.document.convert.domain.preview.GetMateInfo.Result;
import javax.annotation.Resource;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ClassPathResource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@SpringBootTest
@ExtendWith(SpringExtension.class)
class IMateInfoServiceTest {

  @Resource
  IMateInfoService iMateInfoService;

  @ParameterizedTest
  @CsvSource({"pdf", "xls", "xlsx", "ppt", "pptx", "doc", "docx", "dot", "txt"})
  void getMetaInfo(String filePath) {
    filePath = "testResource/test." + filePath;
    ClassPathResource resource = new ClassPathResource(filePath);
    try {
      String absoluteFile = resource.getFile().getAbsolutePath();
      Result metaInfo = iMateInfoService.getMetaInfo(absoluteFile,"7155");
      Assertions.assertNotNull(metaInfo);
      System.out.println(metaInfo);
    } catch (Exception e) {
      Assertions.assertTrue(e.getMessage().contains("不支持的文件类型"));
    }
  }
}