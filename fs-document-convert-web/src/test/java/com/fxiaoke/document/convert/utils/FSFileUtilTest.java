package com.fxiaoke.document.convert.utils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

class FSFileUtilTest {

    @TempDir
    Path tempDir;

    private String testDirPath;
    private String testFilePath;

    @BeforeEach
    void setUp() throws IOException {
        testDirPath = tempDir.toString();
        
        // Create a test file
        Path testFile = tempDir.resolve("test.txt");
        Files.write(testFile, "test content".getBytes());
        testFilePath = testFile.toString();
    }

    @Test
    void testDelete_ExistingFile() throws IOException {
        // Given
        assertTrue(Files.exists(Path.of(testFilePath)));

        // When
        FSFileUtil.delete(testFilePath);

        // Then
        assertFalse(Files.exists(Path.of(testFilePath)));
    }

    @Test
    void testDelete_NonExistentFile() {
        // Given
        String nonExistentPath = tempDir.resolve("nonexistent.txt").toString();
        assertFalse(Files.exists(Path.of(nonExistentPath)));

        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> FSFileUtil.delete(nonExistentPath));
    }

    @Test
    void testDeleteEmptyDir_WithEmptySubdirectories() throws IOException {
        // Given
        Path subDir1 = tempDir.resolve("empty1");
        Path subDir2 = tempDir.resolve("empty2");
        
        Files.createDirectory(subDir1);
        Files.createDirectory(subDir2);
        
        assertTrue(Files.exists(subDir1));
        assertTrue(Files.exists(subDir2));

        // When
        FSFileUtil.deleteEmptyDir(testDirPath);

        // Then
        assertFalse(Files.exists(subDir1));
        assertFalse(Files.exists(subDir2));
    }

    @Test
    void testDeleteEmptyDir_WithNonEmptySubdirectories() throws IOException {
        // Given
        Path subDir1 = tempDir.resolve("nonempty");
        Path subDir2 = tempDir.resolve("empty");
        Path fileInSubDir = subDir1.resolve("file.txt");
        
        Files.createDirectory(subDir1);
        Files.createDirectory(subDir2);
        Files.write(fileInSubDir, "content".getBytes());
        
        assertTrue(Files.exists(subDir1));
        assertTrue(Files.exists(subDir2));
        assertTrue(Files.exists(fileInSubDir));

        // When
        FSFileUtil.deleteEmptyDir(testDirPath);

        // Then
        assertTrue(Files.exists(subDir1)); // Should remain because it contains a file
        assertTrue(Files.exists(fileInSubDir)); // File should remain
        assertFalse(Files.exists(subDir2)); // Empty directory should be deleted
    }
}
