package com.fxiaoke.document.convert.service;

import com.facishare.converter.EIEAConverter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AsmServiceTest {

    @Mock
    private EIEAConverter eieaConverter;

    private AsmService asmService;

    @BeforeEach
    void setUp() {
        asmService = new AsmService(eieaConverter);
    }

    @Test
    void testGetEid_Success() {
        // Given
        String testEa = "testEa";
        int expectedEid = 12345;
        lenient().when(eieaConverter.enterpriseAccountToId(testEa)).thenReturn(expectedEid);

        // When
        int result = asmService.getEid(testEa);

        // Then
        assertEquals(expectedEid, result);
        verify(eieaConverter).enterpriseAccountToId(testEa);
    }

    @ParameterizedTest
    @CsvSource({
        "testEa1, 12345",
        "testEa2, 67890",
        "company123, 999",
        "enterprise_abc, 111",
        "test-company, 222"
    })
    void testGetEid_DifferentEaValues_Success(String ea, int expectedEid) {
        // Given
        lenient().when(eieaConverter.enterpriseAccountToId(ea)).thenReturn(expectedEid);

        // When
        int result = asmService.getEid(ea);

        // Then
        assertEquals(expectedEid, result);
        verify(eieaConverter).enterpriseAccountToId(ea);
    }

    @Test
    void testGetEid_ZeroEid_Success() {
        // Given
        String testEa = "zeroEa";
        int expectedEid = 0;
        lenient().when(eieaConverter.enterpriseAccountToId(testEa)).thenReturn(expectedEid);

        // When
        int result = asmService.getEid(testEa);

        // Then
        assertEquals(expectedEid, result);
        verify(eieaConverter).enterpriseAccountToId(testEa);
    }

    @Test
    void testGetEid_NegativeEid_Success() {
        // Given
        String testEa = "negativeEa";
        int expectedEid = -1;
        lenient().when(eieaConverter.enterpriseAccountToId(testEa)).thenReturn(expectedEid);

        // When
        int result = asmService.getEid(testEa);

        // Then
        assertEquals(expectedEid, result);
        verify(eieaConverter).enterpriseAccountToId(testEa);
    }

    @Test
    void testGetEid_MaxIntegerEid_Success() {
        // Given
        String testEa = "maxEa";
        int expectedEid = Integer.MAX_VALUE;
        lenient().when(eieaConverter.enterpriseAccountToId(testEa)).thenReturn(expectedEid);

        // When
        int result = asmService.getEid(testEa);

        // Then
        assertEquals(expectedEid, result);
        verify(eieaConverter).enterpriseAccountToId(testEa);
    }

    @Test
    void testGetEid_MinIntegerEid_Success() {
        // Given
        String testEa = "minEa";
        int expectedEid = Integer.MIN_VALUE;
        lenient().when(eieaConverter.enterpriseAccountToId(testEa)).thenReturn(expectedEid);

        // When
        int result = asmService.getEid(testEa);

        // Then
        assertEquals(expectedEid, result);
        verify(eieaConverter).enterpriseAccountToId(testEa);
    }

    @ParameterizedTest
    @ValueSource(strings = {"", " ", "   ", "\t", "\n"})
    void testGetEid_EmptyOrWhitespaceEa_Success(String ea) {
        // Given
        int expectedEid = -1;
        lenient().when(eieaConverter.enterpriseAccountToId(ea)).thenReturn(expectedEid);

        // When
        int result = asmService.getEid(ea);

        // Then
        assertEquals(expectedEid, result);
        verify(eieaConverter).enterpriseAccountToId(ea);
    }

    @Test
    void testGetEid_NullEa_CallsConverter() {
        // Given
        String nullEa = null;
        int expectedEid = -1;
        lenient().when(eieaConverter.enterpriseAccountToId(nullEa)).thenReturn(expectedEid);

        // When
        int result = asmService.getEid(nullEa);

        // Then
        assertEquals(expectedEid, result);
        verify(eieaConverter).enterpriseAccountToId(nullEa);
    }

    @Test
    void testGetEid_ConverterThrowsException_PropagatesException() {
        // Given
        String testEa = "testEa";
        lenient().when(eieaConverter.enterpriseAccountToId(testEa))
            .thenThrow(new RuntimeException("Converter error"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class,
            () -> asmService.getEid(testEa));

        assertEquals("Converter error", exception.getMessage());
        verify(eieaConverter).enterpriseAccountToId(testEa);
    }

    @Test
    void testGetEid_MultipleCallsSameEa_CallsConverterEachTime() {
        // Given
        String testEa = "testEa";
        int expectedEid = 12345;
        lenient().when(eieaConverter.enterpriseAccountToId(testEa)).thenReturn(expectedEid);

        // When
        int result1 = asmService.getEid(testEa);
        int result2 = asmService.getEid(testEa);
        int result3 = asmService.getEid(testEa);

        // Then
        assertEquals(expectedEid, result1);
        assertEquals(expectedEid, result2);
        assertEquals(expectedEid, result3);
        verify(eieaConverter, times(3)).enterpriseAccountToId(testEa);
    }

    @Test
    void testGetEid_LongEaString_Success() {
        // Given
        String longEa = "a".repeat(1000); // Very long EA string
        int expectedEid = 99999;
        lenient().when(eieaConverter.enterpriseAccountToId(longEa)).thenReturn(expectedEid);

        // When
        int result = asmService.getEid(longEa);

        // Then
        assertEquals(expectedEid, result);
        verify(eieaConverter).enterpriseAccountToId(longEa);
    }

    @Test
    void testGetEid_SpecialCharactersEa_Success() {
        // Given
        String specialEa = "test@#$%^&*()_+-={}[]|\\:;\"'<>?,./";
        int expectedEid = 55555;
        lenient().when(eieaConverter.enterpriseAccountToId(specialEa)).thenReturn(expectedEid);

        // When
        int result = asmService.getEid(specialEa);

        // Then
        assertEquals(expectedEid, result);
        verify(eieaConverter).enterpriseAccountToId(specialEa);
    }

    @Test
    void testGetEid_UnicodeEa_Success() {
        // Given
        String unicodeEa = "测试企业账号";
        int expectedEid = 88888;
        lenient().when(eieaConverter.enterpriseAccountToId(unicodeEa)).thenReturn(expectedEid);

        // When
        int result = asmService.getEid(unicodeEa);

        // Then
        assertEquals(expectedEid, result);
        verify(eieaConverter).enterpriseAccountToId(unicodeEa);
    }

    @Test
    void testConstructor_ConverterInjected() {
        // When
        AsmService service = new AsmService(eieaConverter);

        // Then
        assertNotNull(service);
        // Verify that the converter is properly injected by calling a method
        lenient().when(eieaConverter.enterpriseAccountToId(anyString())).thenReturn(123);
        int result = service.getEid("test");
        assertEquals(123, result);
    }

    @Test
    void testGetEid_VerifyExactMethodCall() {
        // Given
        String testEa = "exactTest";
        int expectedEid = 77777;
        lenient().when(eieaConverter.enterpriseAccountToId(testEa)).thenReturn(expectedEid);

        // When
        asmService.getEid(testEa);

        // Then
        verify(eieaConverter, times(1)).enterpriseAccountToId(eq(testEa));
        verify(eieaConverter, never()).enterpriseAccountToId(argThat((String arg) -> !testEa.equals(arg)));
    }

    @Test
    void testGetEid_NoOtherMethodsCalled() {
        // Given
        String testEa = "testEa";
        lenient().when(eieaConverter.enterpriseAccountToId(testEa)).thenReturn(123);

        // When
        asmService.getEid(testEa);

        // Then
        verify(eieaConverter, only()).enterpriseAccountToId(testEa);
    }
}
