package com.fxiaoke.document.convert.service.impl;

import com.fxiaoke.document.convert.config.CmsPropertiesConfig;
import com.fxiaoke.document.convert.domain.exception.BaseException;
import com.fxiaoke.document.convert.domain.preview.GetMateInfo;
import com.fxiaoke.document.convert.process.CellsProcess;
import com.fxiaoke.document.convert.process.PdfProcess;
import com.fxiaoke.document.convert.process.SlidesProcess;
import com.fxiaoke.document.convert.process.WordsProcess;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MateInfoServiceTest {

    @Mock
    private CellsProcess cellsProcess;
    
    @Mock
    private SlidesProcess slidesProcess;
    
    @Mock
    private WordsProcess wordsProcess;
    
    @Mock
    private PdfProcess pdfProcess;
    
    @Mock
    private CmsPropertiesConfig cmsPropertiesConfig;

    private MateInfoService mateInfoService;

    private static final String TEST_EA = "testEa";
    private static final String TEST_FILE_PATH = "/tmp/test";

    @BeforeEach
    void setUp() {
        mateInfoService = new MateInfoService(cellsProcess, slidesProcess, wordsProcess, pdfProcess, cmsPropertiesConfig);
        when(cmsPropertiesConfig.isPptPreprocessing()).thenReturn(true);
    }

    @Test
    void testGetMetaInfo_DOCFile_Success() {
        // Given
        String docFilePath = TEST_FILE_PATH + ".doc";
        when(wordsProcess.getMetaInfo(docFilePath, TEST_EA)).thenReturn(5);

        // When
        GetMateInfo.Result result = mateInfoService.getMetaInfo(docFilePath, TEST_EA);

        // Then
        assertNotNull(result);
        assertEquals(5, result.getPageCount());
        verify(wordsProcess).getMetaInfo(docFilePath, TEST_EA);
    }

    @Test
    void testGetMetaInfo_DOCXFile_Success() {
        // Given
        String docxFilePath = TEST_FILE_PATH + ".docx";
        when(wordsProcess.getMetaInfo(docxFilePath, TEST_EA)).thenReturn(8);

        // When
        GetMateInfo.Result result = mateInfoService.getMetaInfo(docxFilePath, TEST_EA);

        // Then
        assertNotNull(result);
        assertEquals(8, result.getPageCount());
        verify(wordsProcess).getMetaInfo(docxFilePath, TEST_EA);
    }

    @Test
    void testGetMetaInfo_XLSFile_Success() {
        // Given
        String xlsFilePath = TEST_FILE_PATH + ".xls";
        GetMateInfo.Result expectedResult = GetMateInfo.Result.of(3);
        when(cellsProcess.getMetaInfo(xlsFilePath)).thenReturn(expectedResult);

        // When
        GetMateInfo.Result result = mateInfoService.getMetaInfo(xlsFilePath, TEST_EA);

        // Then
        assertNotNull(result);
        assertEquals(3, result.getPageCount());
        verify(cellsProcess).getMetaInfo(xlsFilePath);
    }

    @Test
    void testGetMetaInfo_XLSXFile_Success() {
        // Given
        String xlsxFilePath = TEST_FILE_PATH + ".xlsx";
        GetMateInfo.Result expectedResult = GetMateInfo.Result.of(6);
        when(cellsProcess.getMetaInfo(xlsxFilePath)).thenReturn(expectedResult);

        // When
        GetMateInfo.Result result = mateInfoService.getMetaInfo(xlsxFilePath, TEST_EA);

        // Then
        assertNotNull(result);
        assertEquals(6, result.getPageCount());
        verify(cellsProcess).getMetaInfo(xlsxFilePath);
    }

    @Test
    void testGetMetaInfo_PPTFile_Success() {
        // Given
        String pptFilePath = TEST_FILE_PATH + ".ppt";
        when(slidesProcess.getMetaInfo(pptFilePath, true)).thenReturn(10);

        // When
        GetMateInfo.Result result = mateInfoService.getMetaInfo(pptFilePath, TEST_EA);

        // Then
        assertNotNull(result);
        assertEquals(10, result.getPageCount());
        verify(slidesProcess).getMetaInfo(pptFilePath, true);
        verify(cmsPropertiesConfig).isPptPreprocessing();
    }

    @Test
    void testGetMetaInfo_PPTXFile_Success() {
        // Given
        String pptxFilePath = TEST_FILE_PATH + ".pptx";
        when(slidesProcess.getMetaInfo(pptxFilePath, true)).thenReturn(15);

        // When
        GetMateInfo.Result result = mateInfoService.getMetaInfo(pptxFilePath, TEST_EA);

        // Then
        assertNotNull(result);
        assertEquals(15, result.getPageCount());
        verify(slidesProcess).getMetaInfo(pptxFilePath, true);
        verify(cmsPropertiesConfig).isPptPreprocessing();
    }

    @Test
    void testGetMetaInfo_PPTFile_WithPreprocessingDisabled_Success() {
        // Given
        String pptFilePath = TEST_FILE_PATH + ".ppt";
        when(cmsPropertiesConfig.isPptPreprocessing()).thenReturn(false);
        when(slidesProcess.getMetaInfo(pptFilePath, false)).thenReturn(12);

        // When
        GetMateInfo.Result result = mateInfoService.getMetaInfo(pptFilePath, TEST_EA);

        // Then
        assertNotNull(result);
        assertEquals(12, result.getPageCount());
        verify(slidesProcess).getMetaInfo(pptFilePath, false);
        verify(cmsPropertiesConfig).isPptPreprocessing();
    }

    @Test
    void testGetMetaInfo_PDFFile_Success() {
        // Given
        String pdfFilePath = TEST_FILE_PATH + ".pdf";
        when(pdfProcess.getMetaInfo(pdfFilePath)).thenReturn(20);

        // When
        GetMateInfo.Result result = mateInfoService.getMetaInfo(pdfFilePath, TEST_EA);

        // Then
        assertNotNull(result);
        assertEquals(20, result.getPageCount());
        verify(pdfProcess).getMetaInfo(pdfFilePath);
    }

    @ParameterizedTest
    @CsvSource({
        "test.txt, txt",
        "test.jpg, jpg",
        "test.png, png",
        "test.mp4, mp4",
        "test.unknown, unknown"
    })
    void testGetMetaInfo_UnsupportedFileTypes_ThrowsException(String fileName, String extension) {
        // Given
        String unsupportedFilePath = "/tmp/" + fileName;

        // When & Then
        BaseException exception = assertThrows(BaseException.class, 
            () -> mateInfoService.getMetaInfo(unsupportedFilePath, TEST_EA));
        
        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("不支持的文件类型"));
//        assertTrue(exception.getModule().contains("GetMateInfoService-getMetaInfo"));
    }

    @Test
    void testGetMetaInfo_FileWithoutExtension_ThrowsException() {
        // Given
        String filePathWithoutExtension = "/tmp/testfile";

        // When & Then
        BaseException exception = assertThrows(BaseException.class, 
            () -> mateInfoService.getMetaInfo(filePathWithoutExtension, TEST_EA));
        
        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("不支持的文件类型"));
    }

    @Test
    void testGetMetaInfo_EmptyFilePath_ThrowsException() {
        // Given
        String emptyFilePath = "";

        // When & Then
        BaseException exception = assertThrows(BaseException.class, 
            () -> mateInfoService.getMetaInfo(emptyFilePath, TEST_EA));
        
        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("不支持的文件类型"));
    }

    @Test
    void testGetMetaInfo_NullFilePath_ThrowsException() {
        // When & Then
        assertThrows(NullPointerException.class, 
            () -> mateInfoService.getMetaInfo(null, TEST_EA));
    }

    @Test
    void testGetMetaInfo_WordsProcessThrowsException_PropagatesException() {
        // Given
        String docFilePath = TEST_FILE_PATH + ".doc";
        when(wordsProcess.getMetaInfo(docFilePath, TEST_EA))
            .thenThrow(new RuntimeException("Words processing failed"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> mateInfoService.getMetaInfo(docFilePath, TEST_EA));
        
        assertEquals("Words processing failed", exception.getMessage());
        verify(wordsProcess).getMetaInfo(docFilePath, TEST_EA);
    }

    @Test
    void testGetMetaInfo_CellsProcessThrowsException_PropagatesException() {
        // Given
        String xlsFilePath = TEST_FILE_PATH + ".xls";
        when(cellsProcess.getMetaInfo(xlsFilePath))
            .thenThrow(new RuntimeException("Cells processing failed"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> mateInfoService.getMetaInfo(xlsFilePath, TEST_EA));
        
        assertEquals("Cells processing failed", exception.getMessage());
        verify(cellsProcess).getMetaInfo(xlsFilePath);
    }

    @Test
    void testGetMetaInfo_SlidesProcessThrowsException_PropagatesException() {
        // Given
        String pptFilePath = TEST_FILE_PATH + ".ppt";
        when(slidesProcess.getMetaInfo(pptFilePath, true))
            .thenThrow(new RuntimeException("Slides processing failed"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> mateInfoService.getMetaInfo(pptFilePath, TEST_EA));
        
        assertEquals("Slides processing failed", exception.getMessage());
        verify(slidesProcess).getMetaInfo(pptFilePath, true);
    }

    @Test
    void testGetMetaInfo_PdfProcessThrowsException_PropagatesException() {
        // Given
        String pdfFilePath = TEST_FILE_PATH + ".pdf";
        when(pdfProcess.getMetaInfo(pdfFilePath))
            .thenThrow(new RuntimeException("PDF processing failed"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> mateInfoService.getMetaInfo(pdfFilePath, TEST_EA));
        
        assertEquals("PDF processing failed", exception.getMessage());
        verify(pdfProcess).getMetaInfo(pdfFilePath);
    }

    @Test
    void testConstructor_AllDependenciesInjected() {
        // When
        MateInfoService service = new MateInfoService(cellsProcess, slidesProcess, wordsProcess, pdfProcess, cmsPropertiesConfig);

        // Then
        assertNotNull(service);
        // Verify that all dependencies are properly injected by calling a method
        when(pdfProcess.getMetaInfo(anyString())).thenReturn(1);
        GetMateInfo.Result result = service.getMetaInfo("test.pdf", TEST_EA);
        assertNotNull(result);
    }

    @ParameterizedTest
    @CsvSource({
        "TEST.DOC, doc",
        "TEST.DOCX, docx", 
        "TEST.XLS, xls",
        "TEST.XLSX, xlsx",
        "TEST.PPT, ppt",
        "TEST.PPTX, pptx",
        "TEST.PDF, pdf"
    })
    void testGetMetaInfo_UppercaseExtensions_Success(String fileName, String extension) {
        // Given
        String filePath = "/tmp/" + fileName;
        
        switch (extension.toLowerCase()) {
            case "doc", "docx" -> when(wordsProcess.getMetaInfo(filePath, TEST_EA)).thenReturn(1);
            case "xls", "xlsx" -> when(cellsProcess.getMetaInfo(filePath)).thenReturn(GetMateInfo.Result.of(1));
            case "ppt", "pptx" -> when(slidesProcess.getMetaInfo(filePath, true)).thenReturn(1);
            case "pdf" -> when(pdfProcess.getMetaInfo(filePath)).thenReturn(1);
        }

        // When
        GetMateInfo.Result result = mateInfoService.getMetaInfo(filePath, TEST_EA);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getPageCount());
    }
}
