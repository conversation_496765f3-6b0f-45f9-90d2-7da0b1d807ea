package com.fxiaoke.document.convert.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fxiaoke.document.convert.domain.exception.BaseException;
import com.fxiaoke.document.convert.domain.model.CorrectionLevel;
import com.fxiaoke.document.convert.domain.model.PageQRCode;
import com.fxiaoke.document.convert.domain.model.QrCodeParams;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.core.io.ClassPathResource;

class PDFBoxServiceTest {

    private PDFBoxService pdfBoxService;
    private Path testPdfPath;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() throws Exception {
        pdfBoxService = new PDFBoxService();
        // 从测试资源目录复制测试PDF文件到临时目录
        Path sourcePath = new ClassPathResource("test.pdf").getFile().toPath();
        testPdfPath = tempDir.resolve("test.pdf");
        Files.copy(sourcePath, testPdfPath);
    }

    @Test
    void testAddQRCode_WithDefaultQRCode() throws Exception {
        // 准备测试数据
        List<PageQRCode> pageQRCodes = new ArrayList<>();

        // 创建默认二维码
        PageQRCode defaultQRCode = createDefaultQRCode();
        pageQRCodes.add(defaultQRCode);

        // 执行测试
        Path resultPath = pdfBoxService.addQRCode(
            testPdfPath.toString(),
            pageQRCodes,
            "owner123",
            "access123"
        );

        // 验证结果
        assertNotNull(resultPath);
        assertTrue(Files.exists(resultPath));

        // 验证生成的PDF文件 - 使用正确的密码打开
        try (PDDocument document = Loader.loadPDF(resultPath.toFile(), "owner123")) {
            int expectedPages = document.getNumberOfPages();
            assertEquals(expectedPages, document.getNumberOfPages(),
                "页数应该保持不变");
        }
    }

    @Test
    void testAddQRCode_WithSpecificPageQRCode() {
        // 准备测试数据
        List<PageQRCode> pageQRCodes = new ArrayList<>();

        // 创建特定页面的二维码
        PageQRCode pageQRCode = createPageSpecificQRCode(1); // 第一页
        PageQRCode pageQRCode2 = createPageSpecificQRCode2(1); // 第一页
        PageQRCode pageQRCode3 = createPageSpecificQRCode2(2); // 第一页
        pageQRCodes.add(pageQRCode);
        pageQRCodes.add(pageQRCode2);
        pageQRCodes.add(pageQRCode3);

        // 执行测试
        Path resultPath = pdfBoxService.addQRCode(
            testPdfPath.toString(),
            pageQRCodes,
            null,
            null
        );

        // 验证结果
        assertNotNull(resultPath);
        assertTrue(Files.exists(resultPath));
        assertTrue(resultPath.toString().endsWith("_with_qr.pdf"));
    }

    @Test
    void testAddQRCode_WithMultipleQRCodes() {
        // 准备测试数据
        List<PageQRCode> pageQRCodes = new ArrayList<>();

        // 添加默认二维码
        pageQRCodes.add(createDefaultQRCode());

        // 添加特定页面的二维码
        pageQRCodes.add(createPageSpecificQRCode(1));
        pageQRCodes.add(createPageSpecificQRCode(2));

        // 执行测试
        Path resultPath = pdfBoxService.addQRCode(
            testPdfPath.toString(),
            pageQRCodes,
            "owner123",
            null
        );

        // 验证结果
        assertNotNull(resultPath);
        assertTrue(Files.exists(resultPath));
    }

    @Test
    void testAddQRCode_WithInvalidFilePath() {
        // 准备测试数据
        List<PageQRCode> pageQRCodes = new ArrayList<>();
        pageQRCodes.add(createDefaultQRCode());

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> pdfBoxService.addQRCode(
            "invalid_path.pdf",
            pageQRCodes,
            null,
            null
        ));

        // 验证异常消息
        assertTrue(exception.getMessage().contains("输入文件不存在"));
    }

    @Test
    void testAddQRCode_WithEmptyQRCodes() {
        // 准备测试数据
        List<PageQRCode> pageQRCodes = new ArrayList<>();

        // 执行测试 - 应该抛出异常，因为二维码列表为空
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            pdfBoxService.addQRCode(
                testPdfPath.toString(),
                pageQRCodes,
                null,
                null
            );
        });

        // 验证异常消息
        assertTrue(exception.getMessage().contains("文件路径和二维码列表不能为空"));
    }

    @Test
    void testAddQRCode_WithInvalidCoordinates() throws Exception {
        // 准备测试数据
        List<PageQRCode> pageQRCodes = new ArrayList<>();

        // 创建坐标无效的二维码（x坐标超出范围）
        PageQRCode invalidXQRCode = new PageQRCode();
        invalidXQRCode.setPageNumber(1);
        invalidXQRCode.setX(1000); // 超出页面宽度
        invalidXQRCode.setY(50);
        invalidXQRCode.setQrCodeParams(createQrCodeParams("Invalid X QR Code"));
        pageQRCodes.add(invalidXQRCode);

        // 创建坐标无效的二维码（y坐标超出范围）
        PageQRCode invalidYQRCode = new PageQRCode();
        invalidYQRCode.setPageNumber(1);
        invalidYQRCode.setX(50);
        invalidYQRCode.setY(1000); // 超出页面高度
        invalidYQRCode.setQrCodeParams(createQrCodeParams("Invalid Y QR Code"));
        pageQRCodes.add(invalidYQRCode);

        // 创建有效的二维码
        PageQRCode validQRCode = createPageSpecificQRCode(1);
        pageQRCodes.add(validQRCode);

        // 执行测试
        Path resultPath = pdfBoxService.addQRCode(
            testPdfPath.toString(),
            pageQRCodes,
            null,
            null
        );

        // 验证结果
        assertNotNull(resultPath);
        assertTrue(Files.exists(resultPath));

        // 验证生成的PDF文件
        try (PDDocument document = Loader.loadPDF(resultPath.toFile())) {
            assertEquals(document.getNumberOfPages(), document.getNumberOfPages());
        }
    }

    @Test
    void testAddQRCode_WithMultipleQRCodesOnSamePage() throws Exception {
        // 准备测试数据
        List<PageQRCode> pageQRCodes = new ArrayList<>();

        // 在同一页面添加多个二维码
        PageQRCode qrCode1 = createPageSpecificQRCode(1); // 右上角
        PageQRCode qrCode2 = createPageSpecificQRCode2(1); // 左下角
        pageQRCodes.add(qrCode1);
        pageQRCodes.add(qrCode2);

        // 执行测试
        Path resultPath = pdfBoxService.addQRCode(
            testPdfPath.toString(),
            pageQRCodes,
            null,
            null
        );

        // 验证结果
        assertNotNull(resultPath);
        assertTrue(Files.exists(resultPath));

        // 验证生成的PDF文件
        try (PDDocument document = Loader.loadPDF(resultPath.toFile())) {
            assertEquals(document.getNumberOfPages(), document.getNumberOfPages());
        }
    }

    @Test
    void testAddQRCode_FirstQRCodeAsDefault() throws Exception {
        // 准备测试数据
        List<PageQRCode> pageQRCodes = new ArrayList<>();

        // 添加两个特定页面的二维码，但不设置默认二维码
        PageQRCode qrCode1 = createPageSpecificQRCode(2); // 第二页的二维码
        PageQRCode qrCode2 = createPageSpecificQRCode(3); // 第三页的二维码
        pageQRCodes.add(qrCode1);
        pageQRCodes.add(qrCode2);

        // 执行测试
        Path resultPath = pdfBoxService.addQRCode(
            testPdfPath.toString(),
            pageQRCodes,
            null,
            null
        );

        // 验证结果
        assertNotNull(resultPath);
        assertTrue(Files.exists(resultPath));

        // 验证生成的PDF文件
        try (PDDocument document = Loader.loadPDF(resultPath.toFile())) {
            // 第一页应该使用第一个二维码（qrCode1）作为默认二维码
            assertEquals(document.getNumberOfPages(), document.getNumberOfPages());
        }
    }

    @Test
    void testAddQRCode_WithExplicitDefaultAndFirstQRCode() throws Exception {
        // 准备测试数据
        List<PageQRCode> pageQRCodes = new ArrayList<>();

        // 添加一个普通的二维码（将成为第一个二维码）
        PageQRCode firstQRCode = createPageSpecificQRCode(2);
        pageQRCodes.add(firstQRCode);

        // 添加一个显式的默认二维码
        PageQRCode explicitDefault = createDefaultQRCode();
        pageQRCodes.add(explicitDefault);

        // 添加另一个普通的二维码
        PageQRCode thirdQRCode = createPageSpecificQRCode(3);
        pageQRCodes.add(thirdQRCode);

        // 执行测试
        Path resultPath = pdfBoxService.addQRCode(
            testPdfPath.toString(),
            pageQRCodes,
            null,
            null
        );

        // 验证结果
        assertNotNull(resultPath);
        assertTrue(Files.exists(resultPath));

        // 验证生成的PDF文件
        try (PDDocument document = Loader.loadPDF(resultPath.toFile())) {
            // 应该使用显式设置的默认二维码，而不是第一个二维码
            assertEquals(document.getNumberOfPages(), document.getNumberOfPages());
        }
    }

    // 辅助方法：创建默认二维码
    private PageQRCode createDefaultQRCode() {
        PageQRCode defaultQRCode = new PageQRCode();
        defaultQRCode.setDefaultQRCode(true);
        defaultQRCode.setX(50);
        defaultQRCode.setY(50);
        defaultQRCode.setQrCodeParams(createQrCodeParams("Default QR Code"));
        return defaultQRCode;
    }

    // 辅助方法：创建特定页面的二维码
    private PageQRCode createPageSpecificQRCode(int pageNumber) {
        PageQRCode pageQRCode = new PageQRCode();
        pageQRCode.setPageNumber(pageNumber);
        pageQRCode.setX(500); // 靠右
        pageQRCode.setY(750); // 靠上
        pageQRCode.setQrCodeParams(createQrCodeParams("Page " + pageNumber + " QR Code (Top Right)"));
        return pageQRCode;
    }

    private PageQRCode createPageSpecificQRCode2(int pageNumber) {
        PageQRCode pageQRCode = new PageQRCode();
        pageQRCode.setPageNumber(pageNumber);
        pageQRCode.setX(10); // 靠左
        pageQRCode.setY(10); // 靠下
        pageQRCode.setQrCodeParams(createQrCodeParams("Page " + pageNumber + " QR Code (Bottom Left)"));
        return pageQRCode;
    }

    // 辅助方法：创建二维码参数
    private QrCodeParams createQrCodeParams(String content) {
        return QrCodeParams.builder()
            .content(content)
            .size(84)
            .correctionLevel(CorrectionLevel.H)
            .build();
    }
}