package com.fxiaoke.document.convert.process;

import com.fxiaoke.document.convert.domain.preview.GetMateInfo;
import com.fxiaoke.document.convert.process.options.CellsOptions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CellsProcessTest {

    @Mock
    private CellsOptions cellsOptions;

    private CellsProcess cellsProcess;

    @BeforeEach
    void setUp() {
        cellsProcess = new CellsProcess(cellsOptions);
    }

    @Test
    void testConstructor() {
        // When
        CellsProcess newProcess = new CellsProcess(cellsOptions);

        // Then
        assertNotNull(newProcess);
    }

    @Test
    void testGetMetaInfo() {
        // Given
        String filePath = "/test/spreadsheet.xlsx";
        GetMateInfo.Result expectedResult = GetMateInfo.Result.of(3, Arrays.asList("Sheet1", "Sheet2", "Sheet3"));
        when(cellsOptions.getMateInfo(filePath)).thenReturn(expectedResult);

        // When
        GetMateInfo.Result result = cellsProcess.getMetaInfo(filePath);

        // Then
        assertEquals(expectedResult, result);
        assertEquals(3, result.getPageCount());
        assertEquals(3, result.getSheetNames().size());
        verify(cellsOptions).getMateInfo(filePath);
    }

    @Test
    void testGetPageCount() {
        // Given
        String filePath = "/test/spreadsheet.xlsx";
        int expectedPageCount = 5;
        when(cellsOptions.getPageCount(filePath)).thenReturn(expectedPageCount);

        // When
        int result = cellsProcess.getPageCount(filePath);

        // Then
        assertEquals(expectedPageCount, result);
        verify(cellsOptions).getPageCount(filePath);
    }

    @Test
    void testUpgrade() {
        // Given
        String filePath = "/test/spreadsheet.xls";
        String expectedUpgradedPath = "/test/spreadsheet.xlsx";
        when(cellsOptions.upgrade(filePath)).thenReturn(expectedUpgradedPath);

        // When
        String result = cellsProcess.upgrade(filePath);

        // Then
        assertEquals(expectedUpgradedPath, result);
        verify(cellsOptions).upgrade(filePath);
    }

    @Test
    void testToHtml() {
        // Given
        String ea = "testEa";
        String path = "testPath";
        String filePath = "/test/spreadsheet.xlsx";
        int pageIndex = 1;
        String expectedHtmlPath = "/test/sheet1.html";
        when(cellsOptions.toHtml(ea, path, filePath, pageIndex)).thenReturn(expectedHtmlPath);

        // When
        List<String> result = cellsProcess.toHtml(ea, path, filePath, pageIndex);

        // Then
        assertEquals(1, result.size());
        assertEquals(expectedHtmlPath, result.get(0));
        verify(cellsOptions).toHtml(ea, path, filePath, pageIndex);
    }

    @Test
    void testOfficeToPdf_WithBothFlags() {
        // Given
        String filePath = "/test/spreadsheet.xlsx";
        boolean onePagePerSheet = true;
        boolean allColumnsInOnePagePerSheet = false;
        String expectedPdfPath = "/test/spreadsheet.pdf";
        when(cellsOptions.toPdf(filePath, onePagePerSheet, allColumnsInOnePagePerSheet)).thenReturn(expectedPdfPath);

        // When
        String result = cellsProcess.officeToPdf(filePath, onePagePerSheet, allColumnsInOnePagePerSheet);

        // Then
        assertEquals(expectedPdfPath, result);
        verify(cellsOptions).toPdf(filePath, onePagePerSheet, allColumnsInOnePagePerSheet);
    }

    @Test
    void testOfficeToPdf_OnePagePerSheetFalse() {
        // Given
        String filePath = "/test/spreadsheet.xlsx";
        boolean onePagePerSheet = false;
        boolean allColumnsInOnePagePerSheet = true;
        String expectedPdfPath = "/test/spreadsheet.pdf";
        when(cellsOptions.toPdf(filePath, onePagePerSheet, allColumnsInOnePagePerSheet)).thenReturn(expectedPdfPath);

        // When
        String result = cellsProcess.officeToPdf(filePath, onePagePerSheet, allColumnsInOnePagePerSheet);

        // Then
        assertEquals(expectedPdfPath, result);
        verify(cellsOptions).toPdf(filePath, onePagePerSheet, allColumnsInOnePagePerSheet);
    }

    @Test
    void testOfficeToPdf_BothFlagsFalse() {
        // Given
        String filePath = "/test/spreadsheet.xlsx";
        boolean onePagePerSheet = false;
        boolean allColumnsInOnePagePerSheet = false;
        String expectedPdfPath = "/test/spreadsheet.pdf";
        when(cellsOptions.toPdf(filePath, onePagePerSheet, allColumnsInOnePagePerSheet)).thenReturn(expectedPdfPath);

        // When
        String result = cellsProcess.officeToPdf(filePath, onePagePerSheet, allColumnsInOnePagePerSheet);

        // Then
        assertEquals(expectedPdfPath, result);
        verify(cellsOptions).toPdf(filePath, onePagePerSheet, allColumnsInOnePagePerSheet);
    }

    @Test
    void testMerge() {
        // Given
        List<String> filePaths = Arrays.asList("/test/sheet1.xlsx", "/test/sheet2.xlsx");
        String masterFilePath = "/test/merged.xlsx";

        // When & Then - Should not throw exception (method is empty)
        assertDoesNotThrow(() -> cellsProcess.merge(filePaths, masterFilePath));
    }

    @Test
    void testGetMetaInfo_EmptySheetNames() {
        // Given
        String filePath = "/test/empty.xlsx";
        GetMateInfo.Result expectedResult = GetMateInfo.Result.of(0, Collections.emptyList());
        when(cellsOptions.getMateInfo(filePath)).thenReturn(expectedResult);

        // When
        GetMateInfo.Result result = cellsProcess.getMetaInfo(filePath);

        // Then
        assertEquals(expectedResult, result);
        assertEquals(0, result.getPageCount());
        assertTrue(result.getSheetNames().isEmpty());
        verify(cellsOptions).getMateInfo(filePath);
    }

    @Test
    void testGetPageCount_ZeroPages() {
        // Given
        String filePath = "/test/empty.xlsx";
        when(cellsOptions.getPageCount(filePath)).thenReturn(0);

        // When
        int result = cellsProcess.getPageCount(filePath);

        // Then
        assertEquals(0, result);
        verify(cellsOptions).getPageCount(filePath);
    }

    @Test
    void testToHtml_EmptyResult() {
        // Given
        String ea = "testEa";
        String path = "testPath";
        String filePath = "/test/spreadsheet.xlsx";
        int pageIndex = 1;
        when(cellsOptions.toHtml(ea, path, filePath, pageIndex)).thenReturn("");

        // When
        List<String> result = cellsProcess.toHtml(ea, path, filePath, pageIndex);

        // Then
        assertEquals(1, result.size());
        assertEquals("", result.get(0));
        verify(cellsOptions).toHtml(ea, path, filePath, pageIndex);
    }

    @Test
    void testToHtml_DifferentPageIndex() {
        // Given
        String ea = "testEa";
        String path = "testPath";
        String filePath = "/test/spreadsheet.xlsx";
        int pageIndex = 3;
        String expectedHtmlPath = "/test/sheet3.html";
        when(cellsOptions.toHtml(ea, path, filePath, pageIndex)).thenReturn(expectedHtmlPath);

        // When
        List<String> result = cellsProcess.toHtml(ea, path, filePath, pageIndex);

        // Then
        assertEquals(1, result.size());
        assertEquals(expectedHtmlPath, result.get(0));
        verify(cellsOptions).toHtml(ea, path, filePath, pageIndex);
    }

    @Test
    void testGetMetaInfo_SingleSheet() {
        // Given
        String filePath = "/test/single.xlsx";
        GetMateInfo.Result expectedResult = GetMateInfo.Result.of(1, Collections.singletonList("Sheet1"));
        when(cellsOptions.getMateInfo(filePath)).thenReturn(expectedResult);

        // When
        GetMateInfo.Result result = cellsProcess.getMetaInfo(filePath);

        // Then
        assertEquals(expectedResult, result);
        assertEquals(1, result.getPageCount());
        assertEquals(1, result.getSheetNames().size());
        assertEquals("Sheet1", result.getSheetNames().get(0));
        verify(cellsOptions).getMateInfo(filePath);
    }

    @Test
    void testGetMetaInfo_MultipleSheets() {
        // Given
        String filePath = "/test/multiple.xlsx";
        List<String> sheetNames = Arrays.asList("Data", "Summary", "Charts", "Analysis");
        GetMateInfo.Result expectedResult = GetMateInfo.Result.of(4, sheetNames);
        when(cellsOptions.getMateInfo(filePath)).thenReturn(expectedResult);

        // When
        GetMateInfo.Result result = cellsProcess.getMetaInfo(filePath);

        // Then
        assertEquals(expectedResult, result);
        assertEquals(4, result.getPageCount());
        assertEquals(4, result.getSheetNames().size());
        assertEquals("Data", result.getSheetNames().get(0));
        assertEquals("Analysis", result.getSheetNames().get(3));
        verify(cellsOptions).getMateInfo(filePath);
    }

    @Test
    void testMerge_WithNullParameters() {
        // When & Then - Should not throw exception (method is empty)
        assertDoesNotThrow(() -> cellsProcess.merge(null, null));
    }

    @Test
    void testMerge_WithEmptyList() {
        // Given
        List<String> emptyList = Collections.emptyList();
        String masterFilePath = "/test/merged.xlsx";

        // When & Then - Should not throw exception (method is empty)
        assertDoesNotThrow(() -> cellsProcess.merge(emptyList, masterFilePath));
    }

    @Test
    void testToHtml_WithSpecialCharacters() {
        // Given
        String ea = "测试企业";
        String path = "特殊路径";
        String filePath = "/test/文件 with spaces.xlsx";
        int pageIndex = 2;
        String expectedHtmlPath = "/test/特殊表格.html";
        when(cellsOptions.toHtml(ea, path, filePath, pageIndex)).thenReturn(expectedHtmlPath);

        // When
        List<String> result = cellsProcess.toHtml(ea, path, filePath, pageIndex);

        // Then
        assertEquals(1, result.size());
        assertEquals(expectedHtmlPath, result.get(0));
        verify(cellsOptions).toHtml(ea, path, filePath, pageIndex);
    }

    @Test
    void testOfficeToPdf_BothFlagsTrue() {
        // Given
        String filePath = "/test/large.xlsx";
        boolean onePagePerSheet = true;
        boolean allColumnsInOnePagePerSheet = true;
        String expectedPdfPath = "/test/large.pdf";
        when(cellsOptions.toPdf(filePath, onePagePerSheet, allColumnsInOnePagePerSheet)).thenReturn(expectedPdfPath);

        // When
        String result = cellsProcess.officeToPdf(filePath, onePagePerSheet, allColumnsInOnePagePerSheet);

        // Then
        assertEquals(expectedPdfPath, result);
        verify(cellsOptions).toPdf(filePath, onePagePerSheet, allColumnsInOnePagePerSheet);
    }
}
