package com.fxiaoke.document.convert.process.options;

import com.fxiaoke.document.convert.dao.impl.PreviewInfoDao;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class SlidesOptionsTest {

    @Mock
    private PreviewInfoDao previewInfoDao;

    private SlidesOptions slidesOptions;

    @TempDir
    Path tempDir;

    private String testFilePath;

    @BeforeEach
    void setUp() throws IOException {
        slidesOptions = new SlidesOptions(previewInfoDao);

        // Create a test file
        Path testFile = tempDir.resolve("test.pptx");
        Files.write(testFile, "test content".getBytes());
        testFilePath = testFile.toString();
    }

    @Test
    void testGetPdfOption() {
        // When
        var pdfOption = SlidesOptions.getPdfOption();

        // Then
        assertNotNull(pdfOption);
        assertEquals(90, pdfOption.getJpegQuality());
        assertTrue(pdfOption.getSaveMetafilesAsPng());
        assertFalse(pdfOption.getBestImagesCompressionRatio());
        assertEquals(48, pdfOption.getSufficientResolution());
        assertEquals("SimSun", pdfOption.getDefaultRegularFont());
    }

    @Test
    void testConstructor() {
        // When
        SlidesOptions options = new SlidesOptions(previewInfoDao);

        // Then
        assertNotNull(options);
    }


}
