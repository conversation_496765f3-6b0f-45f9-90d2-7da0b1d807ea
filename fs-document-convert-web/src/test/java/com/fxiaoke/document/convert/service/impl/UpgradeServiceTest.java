package com.fxiaoke.document.convert.service.impl;

import com.fxiaoke.document.convert.domain.exception.BaseException;
import com.fxiaoke.document.convert.domain.preview.UpgradeFormat;
import com.fxiaoke.document.convert.process.CellsProcess;
import com.fxiaoke.document.convert.process.SlidesProcess;
import com.fxiaoke.document.convert.process.WordsProcess;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UpgradeServiceTest {

    @Mock
    private CellsProcess cellsProcess;
    
    @Mock
    private SlidesProcess slidesProcess;
    
    @Mock
    private WordsProcess wordsProcess;

    private UpgradeService upgradeService;

    private static final String TEST_UPGRADED_PATH = "/tmp/upgraded_file";

    @BeforeEach
    void setUp() {
        upgradeService = new UpgradeService(cellsProcess, slidesProcess, wordsProcess);
    }

    @Test
    void testUpgrade_DOCFile_Success() {
        // Given
        String docFilePath = "/tmp/test.doc";
        when(wordsProcess.upgrade(docFilePath)).thenReturn(TEST_UPGRADED_PATH);

        // When
        UpgradeFormat.Result result = upgradeService.upgrade(docFilePath);

        // Then
        assertNotNull(result);
        assertEquals(TEST_UPGRADED_PATH, result.getFilePath());
        verify(wordsProcess).upgrade(docFilePath);
        verify(cellsProcess, never()).upgrade(anyString());
        verify(slidesProcess, never()).upgrade(anyString());
    }

    @Test
    void testUpgrade_DOCXFile_Success() {
        // Given
        String docxFilePath = "/tmp/test.docx";
        when(wordsProcess.upgrade(docxFilePath)).thenReturn(TEST_UPGRADED_PATH);

        // When
        UpgradeFormat.Result result = upgradeService.upgrade(docxFilePath);

        // Then
        assertNotNull(result);
        assertEquals(TEST_UPGRADED_PATH, result.getFilePath());
        verify(wordsProcess).upgrade(docxFilePath);
        verify(cellsProcess, never()).upgrade(anyString());
        verify(slidesProcess, never()).upgrade(anyString());
    }

    @Test
    void testUpgrade_XLSFile_Success() {
        // Given
        String xlsFilePath = "/tmp/test.xls";
        when(cellsProcess.upgrade(xlsFilePath)).thenReturn(TEST_UPGRADED_PATH);

        // When
        UpgradeFormat.Result result = upgradeService.upgrade(xlsFilePath);

        // Then
        assertNotNull(result);
        assertEquals(TEST_UPGRADED_PATH, result.getFilePath());
        verify(cellsProcess).upgrade(xlsFilePath);
        verify(wordsProcess, never()).upgrade(anyString());
        verify(slidesProcess, never()).upgrade(anyString());
    }

    @Test
    void testUpgrade_XLSXFile_Success() {
        // Given
        String xlsxFilePath = "/tmp/test.xlsx";
        when(cellsProcess.upgrade(xlsxFilePath)).thenReturn(TEST_UPGRADED_PATH);

        // When
        UpgradeFormat.Result result = upgradeService.upgrade(xlsxFilePath);

        // Then
        assertNotNull(result);
        assertEquals(TEST_UPGRADED_PATH, result.getFilePath());
        verify(cellsProcess).upgrade(xlsxFilePath);
        verify(wordsProcess, never()).upgrade(anyString());
        verify(slidesProcess, never()).upgrade(anyString());
    }

    @Test
    void testUpgrade_PPTFile_Success() {
        // Given
        String pptFilePath = "/tmp/test.ppt";
        when(slidesProcess.upgrade(pptFilePath)).thenReturn(TEST_UPGRADED_PATH);

        // When
        UpgradeFormat.Result result = upgradeService.upgrade(pptFilePath);

        // Then
        assertNotNull(result);
        assertEquals(TEST_UPGRADED_PATH, result.getFilePath());
        verify(slidesProcess).upgrade(pptFilePath);
        verify(wordsProcess, never()).upgrade(anyString());
        verify(cellsProcess, never()).upgrade(anyString());
    }

    @Test
    void testUpgrade_PPTXFile_Success() {
        // Given
        String pptxFilePath = "/tmp/test.pptx";
        when(slidesProcess.upgrade(pptxFilePath)).thenReturn(TEST_UPGRADED_PATH);

        // When
        UpgradeFormat.Result result = upgradeService.upgrade(pptxFilePath);

        // Then
        assertNotNull(result);
        assertEquals(TEST_UPGRADED_PATH, result.getFilePath());
        verify(slidesProcess).upgrade(pptxFilePath);
        verify(wordsProcess, never()).upgrade(anyString());
        verify(cellsProcess, never()).upgrade(anyString());
    }

    @ParameterizedTest
    @CsvSource({
        "/tmp/test.DOC, wordsProcess",
        "/tmp/test.DOCX, wordsProcess",
        "/tmp/test.XLS, cellsProcess",
        "/tmp/test.XLSX, cellsProcess",
        "/tmp/test.PPT, slidesProcess",
        "/tmp/test.PPTX, slidesProcess"
    })
    void testUpgrade_UppercaseExtensions_Success(String filePath, String expectedProcess) {
        // Given
        when(wordsProcess.upgrade(anyString())).thenReturn(TEST_UPGRADED_PATH);
        when(cellsProcess.upgrade(anyString())).thenReturn(TEST_UPGRADED_PATH);
        when(slidesProcess.upgrade(anyString())).thenReturn(TEST_UPGRADED_PATH);

        // When
        UpgradeFormat.Result result = upgradeService.upgrade(filePath);

        // Then
        assertNotNull(result);
        assertEquals(TEST_UPGRADED_PATH, result.getFilePath());
        
        switch (expectedProcess) {
            case "wordsProcess" -> {
                verify(wordsProcess).upgrade(filePath);
                verify(cellsProcess, never()).upgrade(anyString());
                verify(slidesProcess, never()).upgrade(anyString());
            }
            case "cellsProcess" -> {
                verify(cellsProcess).upgrade(filePath);
                verify(wordsProcess, never()).upgrade(anyString());
                verify(slidesProcess, never()).upgrade(anyString());
            }
            case "slidesProcess" -> {
                verify(slidesProcess).upgrade(filePath);
                verify(wordsProcess, never()).upgrade(anyString());
                verify(cellsProcess, never()).upgrade(anyString());
            }
        }
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "/tmp/test.pdf",
        "/tmp/test.txt",
        "/tmp/test.jpg", 
        "/tmp/test.png",
        "/tmp/test.mp4",
        "/tmp/test.unknown"
    })
    void testUpgrade_UnsupportedFileTypes_ThrowsException(String filePath) {
        // When & Then
        BaseException exception = assertThrows(BaseException.class, 
            () -> upgradeService.upgrade(filePath));
        
        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("不支持的文件类型"));
//        assertTrue(exception.getModule().contains("UpgradeService-upgrade"));
        
        verify(wordsProcess, never()).upgrade(anyString());
        verify(cellsProcess, never()).upgrade(anyString());
        verify(slidesProcess, never()).upgrade(anyString());
    }

    @Test
    void testUpgrade_FileWithoutExtension_ThrowsException() {
        // Given
        String filePathWithoutExtension = "/tmp/testfile";

        // When & Then
        BaseException exception = assertThrows(BaseException.class, 
            () -> upgradeService.upgrade(filePathWithoutExtension));
        
        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("不支持的文件类型"));
//        assertTrue(exception.getModule().contains("UpgradeService-upgrade"));
    }

    @Test
    void testUpgrade_EmptyFilePath_ThrowsException() {
        // Given
        String emptyFilePath = "";

        // When & Then
        BaseException exception = assertThrows(BaseException.class, 
            () -> upgradeService.upgrade(emptyFilePath));
        
        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("不支持的文件类型"));
    }

    @Test
    void testUpgrade_NullFilePath_ThrowsException() {
        // When & Then
        assertThrows(NullPointerException.class, 
            () -> upgradeService.upgrade(null));
    }

    @Test
    void testUpgrade_WordsProcessThrowsException_PropagatesException() {
        // Given
        String docFilePath = "/tmp/test.doc";
        when(wordsProcess.upgrade(docFilePath))
            .thenThrow(new RuntimeException("Words upgrade failed"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> upgradeService.upgrade(docFilePath));
        
        assertEquals("Words upgrade failed", exception.getMessage());
        verify(wordsProcess).upgrade(docFilePath);
    }

    @Test
    void testUpgrade_CellsProcessThrowsException_PropagatesException() {
        // Given
        String xlsFilePath = "/tmp/test.xls";
        when(cellsProcess.upgrade(xlsFilePath))
            .thenThrow(new RuntimeException("Cells upgrade failed"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> upgradeService.upgrade(xlsFilePath));
        
        assertEquals("Cells upgrade failed", exception.getMessage());
        verify(cellsProcess).upgrade(xlsFilePath);
    }

    @Test
    void testUpgrade_SlidesProcessThrowsException_PropagatesException() {
        // Given
        String pptFilePath = "/tmp/test.ppt";
        when(slidesProcess.upgrade(pptFilePath))
            .thenThrow(new RuntimeException("Slides upgrade failed"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> upgradeService.upgrade(pptFilePath));
        
        assertEquals("Slides upgrade failed", exception.getMessage());
        verify(slidesProcess).upgrade(pptFilePath);
    }

    @Test
    void testUpgrade_ComplexFilePath_Success() {
        // Given
        String complexFilePath = "/complex/path/with spaces/test document.docx";
        when(wordsProcess.upgrade(complexFilePath)).thenReturn(TEST_UPGRADED_PATH);

        // When
        UpgradeFormat.Result result = upgradeService.upgrade(complexFilePath);

        // Then
        assertNotNull(result);
        assertEquals(TEST_UPGRADED_PATH, result.getFilePath());
        verify(wordsProcess).upgrade(complexFilePath);
    }

    @Test
    void testUpgrade_FilePathWithMultipleDots_Success() {
        // Given
        String filePathWithDots = "/tmp/test.backup.final.xlsx";
        when(cellsProcess.upgrade(filePathWithDots)).thenReturn(TEST_UPGRADED_PATH);

        // When
        UpgradeFormat.Result result = upgradeService.upgrade(filePathWithDots);

        // Then
        assertNotNull(result);
        assertEquals(TEST_UPGRADED_PATH, result.getFilePath());
        verify(cellsProcess).upgrade(filePathWithDots);
    }

    @Test
    void testUpgrade_ResultNotNull_WhenProcessReturnsNull() {
        // Given
        String docFilePath = "/tmp/test.doc";
        when(wordsProcess.upgrade(docFilePath)).thenReturn(null);

        // When
        UpgradeFormat.Result result = upgradeService.upgrade(docFilePath);

        // Then
        assertNotNull(result);
        assertNull(result.getFilePath());
        verify(wordsProcess).upgrade(docFilePath);
    }

    @Test
    void testUpgrade_ResultNotNull_WhenProcessReturnsEmptyString() {
        // Given
        String xlsFilePath = "/tmp/test.xls";
        when(cellsProcess.upgrade(xlsFilePath)).thenReturn("");

        // When
        UpgradeFormat.Result result = upgradeService.upgrade(xlsFilePath);

        // Then
        assertNotNull(result);
        assertEquals("", result.getFilePath());
        verify(cellsProcess).upgrade(xlsFilePath);
    }

    @Test
    void testConstructor_AllDependenciesInjected() {
        // When
        UpgradeService service = new UpgradeService(cellsProcess, slidesProcess, wordsProcess);

        // Then
        assertNotNull(service);
        // Verify that all dependencies are properly injected by calling a method
        when(wordsProcess.upgrade(anyString())).thenReturn("test");
        UpgradeFormat.Result result = service.upgrade("test.doc");
        assertNotNull(result);
    }

    @ParameterizedTest
    @CsvSource({
        "test.doc, wordsProcess",
        "test.docx, wordsProcess",
        "test.xls, cellsProcess",
        "test.xlsx, cellsProcess",
        "test.ppt, slidesProcess",
        "test.pptx, slidesProcess"
    })
    void testUpgrade_CorrectProcessCalled(String fileName, String expectedProcess) {
        // Given
        String filePath = "/tmp/" + fileName;
        when(wordsProcess.upgrade(anyString())).thenReturn(TEST_UPGRADED_PATH);
        when(cellsProcess.upgrade(anyString())).thenReturn(TEST_UPGRADED_PATH);
        when(slidesProcess.upgrade(anyString())).thenReturn(TEST_UPGRADED_PATH);

        // When
        UpgradeFormat.Result result = upgradeService.upgrade(filePath);

        // Then
        assertNotNull(result);
        assertEquals(TEST_UPGRADED_PATH, result.getFilePath());
        
        switch (expectedProcess) {
            case "wordsProcess" -> {
                verify(wordsProcess).upgrade(filePath);
                verify(cellsProcess, never()).upgrade(anyString());
                verify(slidesProcess, never()).upgrade(anyString());
            }
            case "cellsProcess" -> {
                verify(cellsProcess).upgrade(filePath);
                verify(wordsProcess, never()).upgrade(anyString());
                verify(slidesProcess, never()).upgrade(anyString());
            }
            case "slidesProcess" -> {
                verify(slidesProcess).upgrade(filePath);
                verify(wordsProcess, never()).upgrade(anyString());
                verify(cellsProcess, never()).upgrade(anyString());
            }
        }
    }
}
