package com.fxiaoke.document.convert.service.impl;

import com.fxiaoke.document.convert.domain.exception.BaseException;
import com.fxiaoke.document.convert.domain.preview.ToPngZip;
import com.fxiaoke.document.convert.process.PdfProcess;
import com.fxiaoke.document.convert.process.WordsProcess;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ToPngZipServiceTest {

    @Mock
    private PdfProcess pdfProcess;
    
    @Mock
    private WordsProcess wordsProcess;

    private ToPngZipService toPngZipService;

    private static final String TEST_ZIP_PATH = "/tmp/images.zip";

    @BeforeEach
    void setUp() {
        toPngZipService = new ToPngZipService(pdfProcess, wordsProcess);
    }

    @Test
    void testToPngZip_PDFFile_Success() {
        // Given
        String pdfFilePath = "/tmp/test.pdf";
        when(pdfProcess.toPngZip(pdfFilePath)).thenReturn(TEST_ZIP_PATH);

        // When
        ToPngZip.Result result = toPngZipService.toPngZip(pdfFilePath);

        // Then
        assertNotNull(result);
        assertEquals(TEST_ZIP_PATH, result.getPath());
        verify(pdfProcess).toPngZip(pdfFilePath);
        verify(wordsProcess, never()).toPngZip(anyString());
    }

    @Test
    void testToPngZip_DOCFile_Success() {
        // Given
        String docFilePath = "/tmp/test.doc";
        when(wordsProcess.toPngZip(docFilePath)).thenReturn(TEST_ZIP_PATH);

        // When
        ToPngZip.Result result = toPngZipService.toPngZip(docFilePath);

        // Then
        assertNotNull(result);
        assertEquals(TEST_ZIP_PATH, result.getPath());
        verify(wordsProcess).toPngZip(docFilePath);
        verify(pdfProcess, never()).toPngZip(anyString());
    }

    @Test
    void testToPngZip_DOCXFile_Success() {
        // Given
        String docxFilePath = "/tmp/test.docx";
        when(wordsProcess.toPngZip(docxFilePath)).thenReturn(TEST_ZIP_PATH);

        // When
        ToPngZip.Result result = toPngZipService.toPngZip(docxFilePath);

        // Then
        assertNotNull(result);
        assertEquals(TEST_ZIP_PATH, result.getPath());
        verify(wordsProcess).toPngZip(docxFilePath);
        verify(pdfProcess, never()).toPngZip(anyString());
    }

    @ParameterizedTest
    @CsvSource({
        "/tmp/test.PDF, PDF",
        "/tmp/test.DOC, DOC",
        "/tmp/test.DOCX, DOCX"
    })
    void testToPngZip_UppercaseExtensions_Success(String filePath, String fileType) {
        // Given
        switch (fileType) {
            case "PDF" -> when(pdfProcess.toPngZip(filePath)).thenReturn(TEST_ZIP_PATH);
            case "DOC", "DOCX" -> when(wordsProcess.toPngZip(filePath)).thenReturn(TEST_ZIP_PATH);
        }

        // When
        ToPngZip.Result result = toPngZipService.toPngZip(filePath);

        // Then
        assertNotNull(result);
        assertEquals(TEST_ZIP_PATH, result.getPath());
        
        if ("PDF".equals(fileType)) {
            verify(pdfProcess).toPngZip(filePath);
        } else {
            verify(wordsProcess).toPngZip(filePath);
        }
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "/tmp/test.txt",
        "/tmp/test.jpg", 
        "/tmp/test.png",
        "/tmp/test.xls",
        "/tmp/test.xlsx",
        "/tmp/test.ppt",
        "/tmp/test.pptx",
        "/tmp/test.mp4",
        "/tmp/test.unknown"
    })
    void testToPngZip_UnsupportedFileTypes_ThrowsException(String filePath) {
        // When & Then
        BaseException exception = assertThrows(BaseException.class, 
            () -> toPngZipService.toPngZip(filePath));
        
        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("文件类型不支持"));
        assertTrue(exception.getModule().contains("ToPngZipService-toPngZip"));
        
        verify(pdfProcess, never()).toPngZip(anyString());
        verify(wordsProcess, never()).toPngZip(anyString());
    }

    @Test
    void testToPngZip_FileWithoutExtension_ThrowsException() {
        // Given
        String filePathWithoutExtension = "/tmp/testfile";

        // When & Then
        BaseException exception = assertThrows(BaseException.class, 
            () -> toPngZipService.toPngZip(filePathWithoutExtension));
        
        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("文件类型不支持"));
        assertTrue(exception.getModule().contains("ToPngZipService-toPngZip"));
    }

    @Test
    void testToPngZip_EmptyFilePath_ThrowsException() {
        // Given
        String emptyFilePath = "";

        // When & Then
        BaseException exception = assertThrows(BaseException.class, 
            () -> toPngZipService.toPngZip(emptyFilePath));
        
        assertEquals(400, exception.getCode());
        assertTrue(exception.getMessage().contains("文件类型不支持"));
    }

    @Test
    void testToPngZip_NullFilePath_ThrowsException() {
        // When & Then
        assertThrows(NullPointerException.class, 
            () -> toPngZipService.toPngZip(null));
    }

    @Test
    void testToPngZip_PdfProcessThrowsException_PropagatesException() {
        // Given
        String pdfFilePath = "/tmp/test.pdf";
        when(pdfProcess.toPngZip(pdfFilePath))
            .thenThrow(new RuntimeException("PDF processing failed"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> toPngZipService.toPngZip(pdfFilePath));
        
        assertEquals("PDF processing failed", exception.getMessage());
        verify(pdfProcess).toPngZip(pdfFilePath);
    }

    @Test
    void testToPngZip_WordsProcessThrowsException_PropagatesException() {
        // Given
        String docFilePath = "/tmp/test.doc";
        when(wordsProcess.toPngZip(docFilePath))
            .thenThrow(new RuntimeException("Words processing failed"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> toPngZipService.toPngZip(docFilePath));
        
        assertEquals("Words processing failed", exception.getMessage());
        verify(wordsProcess).toPngZip(docFilePath);
    }

    @Test
    void testToPngZip_ComplexFilePath_Success() {
        // Given
        String complexFilePath = "/complex/path/with spaces/test document.pdf";
        when(pdfProcess.toPngZip(complexFilePath)).thenReturn(TEST_ZIP_PATH);

        // When
        ToPngZip.Result result = toPngZipService.toPngZip(complexFilePath);

        // Then
        assertNotNull(result);
        assertEquals(TEST_ZIP_PATH, result.getPath());
        verify(pdfProcess).toPngZip(complexFilePath);
    }

    @Test
    void testToPngZip_FilePathWithMultipleDots_Success() {
        // Given
        String filePathWithDots = "/tmp/test.backup.final.pdf";
        when(pdfProcess.toPngZip(filePathWithDots)).thenReturn(TEST_ZIP_PATH);

        // When
        ToPngZip.Result result = toPngZipService.toPngZip(filePathWithDots);

        // Then
        assertNotNull(result);
        assertEquals(TEST_ZIP_PATH, result.getPath());
        verify(pdfProcess).toPngZip(filePathWithDots);
    }

    @Test
    void testToPngZip_ResultNotNull_WhenProcessReturnsNull() {
        // Given
        String pdfFilePath = "/tmp/test.pdf";
        when(pdfProcess.toPngZip(pdfFilePath)).thenReturn(null);

        // When
        ToPngZip.Result result = toPngZipService.toPngZip(pdfFilePath);

        // Then
        assertNotNull(result);
        assertNull(result.getPath());
        verify(pdfProcess).toPngZip(pdfFilePath);
    }

    @Test
    void testToPngZip_ResultNotNull_WhenProcessReturnsEmptyString() {
        // Given
        String docFilePath = "/tmp/test.doc";
        when(wordsProcess.toPngZip(docFilePath)).thenReturn("");

        // When
        ToPngZip.Result result = toPngZipService.toPngZip(docFilePath);

        // Then
        assertNotNull(result);
        assertEquals("", result.getPath());
        verify(wordsProcess).toPngZip(docFilePath);
    }

    @Test
    void testConstructor_AllDependenciesInjected() {
        // When
        ToPngZipService service = new ToPngZipService(pdfProcess, wordsProcess);

        // Then
        assertNotNull(service);
        // Verify that all dependencies are properly injected by calling a method
        when(pdfProcess.toPngZip(anyString())).thenReturn("test");
        ToPngZip.Result result = service.toPngZip("test.pdf");
        assertNotNull(result);
    }

    @ParameterizedTest
    @CsvSource({
        "test.pdf, pdfProcess",
        "test.doc, wordsProcess",
        "test.docx, wordsProcess"
    })
    void testToPngZip_CorrectProcessCalled(String fileName, String expectedProcess) {
        // Given
        String filePath = "/tmp/" + fileName;
        when(pdfProcess.toPngZip(anyString())).thenReturn(TEST_ZIP_PATH);
        when(wordsProcess.toPngZip(anyString())).thenReturn(TEST_ZIP_PATH);

        // When
        ToPngZip.Result result = toPngZipService.toPngZip(filePath);

        // Then
        assertNotNull(result);
        assertEquals(TEST_ZIP_PATH, result.getPath());
        
        if ("pdfProcess".equals(expectedProcess)) {
            verify(pdfProcess).toPngZip(filePath);
            verify(wordsProcess, never()).toPngZip(anyString());
        } else if ("wordsProcess".equals(expectedProcess)) {
            verify(wordsProcess).toPngZip(filePath);
            verify(pdfProcess, never()).toPngZip(anyString());
        }
    }
}
